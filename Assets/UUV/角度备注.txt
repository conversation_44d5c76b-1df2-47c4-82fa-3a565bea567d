在 Unity 中，航向角（Yaw）、俯仰角（Pitch）和翻滚角（Roll）对应于对象的欧拉角（Euler Angles），通常通过 Transform 组件的 eulerAngles 属性表示。这些角度分别对应以下轴：

航向角 (Yaw)：围绕 Y 轴（垂直轴）的旋转，Unity 中对应 eulerAngles.y。
俯仰角 (Pitch)：围绕 X 轴（水平轴）的旋转，Unity 中对应 eulerAngles.x。
翻滚角 (Roll)：围绕 Z 轴（深度轴）的旋转，Unity 中对应 eulerAngles.z。
这些值以度（degrees）为单位，可以通过脚本直接读取或修改。例如：

csharp

Collapse

Wrap

Copy
Vector3 angles = transform.eulerAngles;
float yaw = angles.y;   // 航向角
float pitch = angles.x; // 俯仰角
float roll = angles.z;  // 翻滚角
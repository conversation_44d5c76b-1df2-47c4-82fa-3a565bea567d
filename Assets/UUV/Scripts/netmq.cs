#define NewFramework
#define Debug
using NetMQ;
using NetMQ.Sockets;
using System;
using System.Collections.Generic;
using UnityEngine;
using System.Threading;
using System.Threading.Tasks;
using System.Text;
using System.Diagnostics;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Unity.VisualScripting;
using Assets;
using System.IO;
//using UnityEditor.Experimental.GraphView;
using System.Net.Sockets;
using UnityEngine.UI;
using System.Net;
using System.Runtime.InteropServices;
using System.Linq;
using System.Drawing;
using System.Net.NetworkInformation;

//using TreeEditor;


public  class netmq : MonoBehaviour
{
    //qinkang新框架
    CancellationTokenSource cts1 = new CancellationTokenSource();//发布json使用
    CancellationTokenSource cts2 = new CancellationTokenSource();//订阅json使用
    CancellationTokenSource cts3 = new CancellationTokenSource();//发布前视声呐图片使用
    CancellationTokenSource cts4 = new CancellationTokenSource();//发布水雷使用
    static List<UuvState> uuvState = new List<UuvState>();//发布多个UUV的状态
    Dictionary<string, List<UuvState>> jsonToROS = new Dictionary<string, List<UuvState>>();

    #region 【1】变量
    static  string str_VeryImportantJsonData = null;

 //目标物配置
        float DynamicAcc;
        float DynamicAngleSpeed;
        float DynamicDepth;
        int DynamicModel;
        float DynamicSpeed;
        int ObstacleType;
        float StaticObstacleLength;
        float X_target_QT;
        float Y_target_QT;

 //uuv配置
        int SonarForwardConfig;
        float MaxSpeed;
        float MinTurnR;
        float PitchThreshold;//纵摇角阈值
        int SonarSideConfig;
        float SonarForwardAngleRange;//前视声呐波束角
        float SonarForwardRange;//前视声呐量程
        float SonarSideRange;//侧扫声呐量程
        int UuvID;
        int UuvType;
        float X_uuv_QT;
        float Y_uuv_QT;
        float YawRateThreshold;//艏摇角阈值
        double referLongitude = 0;//参考点经度
        double referLatitude = 0;//参考点纬度
        float SpeedLevel = 1.0f;//仿真倍速
        int AmmoNum = 0;//炮弹数量

 //控制
        string Type = "";
        int OverallView;//取值2或1
        int UuvNum = 0;
        int PersonView ;//取值2或1
        int FrontSonarShow ;
        int SideSonarShow ;


 //需要实例化的预制体
        public GameObject ropePrefab;
        public GameObject uuv100_1_Prefab;
        public GameObject uuv100_2_Prefab;
        public GameObject uuv300_1_Prefab;
        public GameObject uuv300_2_Prefab;
        public GameObject uuv1000_1_Prefab;
        public GameObject uuv1000_2_Prefab;
        public GameObject uuv_TH1402_2_Prefab;

 //UI
        public RawImage rawImage_Qian;
        public RawImage rawImage_Ce;
        public Text txt_id;
        public Text txt_speed;
        public Text txt_longitude;
        public Text txt_latitede;
        public Text txt_height;
        public RenderTexture renderTexture;
        public Text state_Show;

        public Text hasReceivedAnimationCommand_Test;
        string hasReceivedAnimationCommand_Testzz = "还没有收到搜索命令";
        int commandCount_Test;
        int stopCount_Test;

        private readonly object _lock_ROS = new object();
        bool rosNewDataReceived = false ;
        string strSendDataToROS = null;
        StringBuilder sb = new StringBuilder(2500);
        List<UuvMathModel> mathModels = new List<UuvMathModel>();
        static List<GameObject> Uuv3DModels = new List<GameObject>();
        List<GameObject> mine3DModels = new List<GameObject>();
        int[] errorNumber = new int[10] { -1, -1, -1, -1 , -1, -1, -1, -1 , -1, -1, };
        static Dictionary<int ,(float,float,float,float,float,float,float)>  dic = new Dictionary<int, (float, float, float, float,float,float,float)>();
        //(最小转弯半径MinTurnR，u，限幅后的q，限幅后的r，倍速，搜索，处置)
        static Queue<Command> queueCommand = new Queue<Command>(10000);//命令队列

    //接收到的图片数据
    byte[] JPG3 = new byte[1024*512*3];
    byte[] JPG4 = new byte[512*256*3];
 
    #endregion
    // Start is called before the first frame update
    private  void Start()
    {
        Time.fixedDeltaTime = 0.001f;//快速生成返回至ROS的数据



        /*前视声纳结构体收发
         * **/
        //SendToQianShiShengNaTest();
        //ReceiveFromQianShiShengNaTest();

        //System.Threading.Tasks.Task.Factory.StartNew(() => { Publish(); });
        //System.Threading.Tasks.Task.Factory.StartNew(() => { Subscriber(); });

#if (NewFramework)
        {

            //发布
            csharp_init();
            csharp_init_publisher("msg_u3d_state");

            string json;
            Task.Factory.StartNew(async () =>
            {
                while (!cts1.Token.IsCancellationRequested)
                {
                    while ((uuvState.Count != Uuv3DModels.Count) || (uuvState.Count == 0))
                    {
                       await Task.Delay(50);
                    }

                    json = JsonConvert.SerializeObject(jsonToROS);
                    csharp_publish("msg_u3d_state", json);
                    json = null;
                    await Task.Delay(50);
                }
            }, cts1.Token);

            SendToCeSaoShengNa_Picture_New(512, 256);
            SendToCeSaoShengNa_MaoLei_New();

            //订阅
            Task.Factory.StartNew(() =>
            {
                CallbackDelegate delegateQT = new CallbackDelegate(CallbackQT);
                CallbackDelegate delegateROS = new CallbackDelegate(CallbackROS);
                csharp_init_subscriber("csharp", delegateQT);
                csharp_init_subscriber("msg_u3d_control", delegateROS);
                csharp_subscribe();

            }, cts2.Token);

        }
 #endif


    }
    async Task Publish()
    {
        using (var pubSocket = new PublisherSocket())
        {
            pubSocket.Options.ReceiveHighWatermark = 1000;
            pubSocket.Bind("tcp:/:8396");
            Dictionary<int,(string,string)> dict = new Dictionary<int,(string,string)>();
            dict.Add(0,("TopicA", "杨旭"));
            dict.Add(1, ("TopicB", "yangxu"));
            dict.Add(2, ("TopicC", "syyzyx"));
            while (true)
            {
                int i = new System.Random().Next(0, 3);
                pubSocket.SendMoreFrame(dict[i].Item1).SendFrame(dict[i].Item2);
                await System.Threading.Tasks.Task.Delay(100);
            }
        }
    }
    async Task Subscriber()
    {
        using (var subSocket = new SubscriberSocket())
        {
            subSocket.Connect("tcp://localhost:5623");
            subSocket.Subscribe("PictureA");
            subSocket.Subscribe("TopicB");
            subSocket.Subscribe("TopicC");
            List<Byte[]> data = new List<byte[]>();

            while (true)
            {
                //阻塞模式
                //string topic = subSocket.ReceiveFrameString();
                //string message = subSocket.ReceiveFrameString();
                //print(topic + "," + message);

                //非阻塞模式，只需要一次接收
                if (subSocket.TryReceiveMultipartBytes(ref data))
                {

                    print($"订阅的主题是：{Encoding.UTF8.GetString(data[0])}，内容长度是:{data[1].Length}");

                }
                else
                {
                    await Task.Delay(10);
                }

            }
        }

    }
    async Task SendToQianShiShengNa(int width,int height)
    {
        INIParser ini = new INIParser();
        ini.Open(Application.streamingAssetsPath + "/COM.ini");
        string fordwardSonarRemoteIP = ini.ReadValue("FordwardSonar", "RemoteIP", "127.0.0.1");
        string fordwardSonarRemotePort = ini.ReadValue("FordwardSonar", "Remoteport_Receive", "8888");
        string fordwardSonarLocalIP = ini.ReadValue("FordwardSonar", "LocalIP", "127.0.0.1");
        string fordwardSonarLocalPort = ini.ReadValue("FordwardSonar", "LocalPort_Send", "8889");
        int Interval =int.Parse(ini.ReadValue("FordwardSonar", "Interval", "0"));
        int WaitTime = int.Parse(ini.ReadValue("FordwardSonar", "WaitTime", "40"));
        ini.Close();

        UdpClient uDPfordwardSonar = new UdpClient(new IPEndPoint(IPAddress.Parse(fordwardSonarLocalIP), int.Parse(fordwardSonarLocalPort)));
        IPEndPoint fordwardSonarRemotePoint = new IPEndPoint(IPAddress.Parse(fordwardSonarRemoteIP), int.Parse(fordwardSonarRemotePort));
        
        byte[] header = Encoding.ASCII.GetBytes("$FSP");//帧头
        byte[] id = new byte[4];//id
        byte[] ShowPicture = new byte[1];//是否显示声纳图像
        byte[] UUV_X = new byte[4];//uuv坐标x
        byte[] UUV_Y = new byte[4];//uuv坐标y
        byte[] UUV_Z = new byte[4];//uuv坐标z
        byte[] UUV_RotationX = new byte[4];
        byte[] UUV_RotationY = new byte[4];
        byte[] UUV_RotationZ = new byte[4];
        byte[] MaoLei_Count  = new byte[4];//目标物数量
        byte[] MaoLei_Coordinate;//目标物坐标，长度为3*目标物数量
        byte[] img_size = new byte[4];
        byte[] img;
        byte[] fullData;

        Texture2D tex = new Texture2D(width, height);
        RenderTexture rt = new RenderTexture(width, height, 0);
        Texture2D screenShot = new Texture2D(width, height, TextureFormat.RGB24, false);
        List<RenderTexture> renderTextures = new List<RenderTexture>();//配合方法三使用
        for (int renderTexturesCount = 0; renderTexturesCount < 100; renderTexturesCount++)
        {
            renderTextures.Add(new RenderTexture(width, height, 0));
        }
        Stopwatch sw1 = Stopwatch.StartNew();//3毫秒间隔
        Stopwatch sw2 = Stopwatch.StartNew();//3毫秒间隔

        while (true)
        {
          loop: GameObject[] QS = GameObject.FindGameObjectsWithTag("QianShiShengNa");
                GameObject[] maolei = GameObject.FindGameObjectsWithTag("MaoLei");
            try
            {
                for (int i = 0; i < QS.Length; i++)
                {
                    
                    for (int enableNum = 0; enableNum < QS.Length; enableNum++)
                    {
                        if (enableNum == i)
                        {
                            QS[enableNum].GetComponent<Camera>().enabled = true;
                        }
                        else
                        {
                            QS[enableNum].GetComponent<Camera>().enabled = false;
                        }
                    }

                    id = BitConverter.GetBytes(int.Parse(QS[i].transform.parent.name.Substring(0, 1)));

                    //前视声纳显示命令
                    //方法一：最原始的方法
                    //ShowPicture = BitConverter.GetBytes(i == UuvNum ? true : false);

                    //方法二：
                    //if (UuvNum == BitConverter.ToInt32(id))
                    //{
                    //    ShowPicture = BitConverter.GetBytes(true);
                    //}
                    //else
                    //{
                    //    if (i == 0)
                    //    {
                    //        ShowPicture = BitConverter.GetBytes(true);
                    //    }
                    //    else
                    //    {
                    //        ShowPicture = BitConverter.GetBytes(false);
                    //    }
                    //}

                    //方法三：比较消耗资源
                    //if (QS.Length == 1)//场景有1个前视声纳
                    //{
                    //    ShowPicture = BitConverter.GetBytes(true);
                    //}
                    //else//场景有多个前视声纳
                    //{
                    //    if (Uuv3DModels[UuvNum].transform.Find("ForwardLookingSonar") != null)//所选编号搭载了前视声纳
                    //    {
                    //        if (Uuv3DModels[UuvNum].transform == QS[i].transform.parent.transform)
                    //        {
                    //            ShowPicture = BitConverter.GetBytes(true);
                    //        }
                    //        else
                    //        {
                    //            ShowPicture = BitConverter.GetBytes(false);
                    //        }
                    //    }
                    //    else //所选编号未搭载前视声纳
                    //    {
                    //        if (i == 0)
                    //        {
                    //            ShowPicture = BitConverter.GetBytes(true);
                    //        }
                    //        else
                    //        {
                    //            ShowPicture = BitConverter.GetBytes(false);
                    //        }
                    //    }
                    //}

                    //方法四：需要测试
                    if (QS.Length == 1)
                    {
                        ShowPicture = BitConverter.GetBytes(true);
                    }
                    else
                    {
                        if ((UuvNum == BitConverter.ToInt32(id)) && (UuvNum == i) && (BitConverter.ToInt32(id) == i))
                        {
                            ShowPicture = BitConverter.GetBytes(true);
                        }
                        else
                        {
                            ShowPicture = BitConverter.GetBytes(false);
                        }
                    }
                    
                    //print($"{BitConverter.ToInt32(id)}号声纳{BitConverter.ToBoolean(ShowPicture)}");

                    UUV_X = BitConverter.GetBytes(QS[i].transform.parent.position.x);
                    UUV_Y = BitConverter.GetBytes(QS[i].transform.parent.position.y);
                    UUV_Z = BitConverter.GetBytes(QS[i].transform.parent.position.z);
                    UUV_RotationX = BitConverter.GetBytes(QS[i].transform.parent.rotation.x);
                    UUV_RotationY = BitConverter.GetBytes(QS[i].transform.parent.rotation.y);
                    UUV_RotationZ = BitConverter.GetBytes(QS[i].transform.parent.rotation.z);
                    MaoLei_Count = BitConverter.GetBytes(maolei.Length);
                    MaoLei_Coordinate = new byte[maolei.Length * 12];
                    for (int j = 0; j < maolei.Length; j++)
                    {
                        BitConverter.GetBytes(maolei[j].transform.position.x).CopyTo(MaoLei_Coordinate, 12 * j + 0);
                        BitConverter.GetBytes(maolei[j].transform.position.y).CopyTo(MaoLei_Coordinate, 12 * j + 4);
                        BitConverter.GetBytes(maolei[j].transform.position.z).CopyTo(MaoLei_Coordinate, 12 * j + 8);
                        //print($"{j}号障碍物位置（{BitConverter.ToSingle(BitConverter.GetBytes(maolei[i].transform.position.x))}，{BitConverter.ToSingle(BitConverter.GetBytes(maolei[i].transform.position.y))}，{BitConverter.ToSingle(BitConverter.GetBytes(maolei[i].transform.position.z))}）");
                    }
                   
                    //方法一：原始方法，显示不出来链条
                    //img = CaptureCamera(QS[i].GetComponent<Camera>(), rt, screenShot, 30);
                    img = CaptureCamera(QS[i].GetComponent<Camera>(), renderTextures[i], screenShot, 30);
                    //方法二：手动新建public的RenderTexture,ColorFormat设置为R8G8B8A8_SRGB,Depth设置为D32_SFLOAT_S8_UINT
                    //QS[i].GetComponent<Camera>().targetTexture = renderTexture;
                    //img = rt2JPG(renderTexture, screenShot);
                    //方法三：为每一个相机配备一个rendertexture,耗时最短，只自测，未联调。如果共用同一个rendertexture,生成的画面不变
                    //QS[i].GetComponent<Camera>().targetTexture = renderTextures[i];
                    //img = rt2JPG(renderTextures[i], screenShot);
                    
                    img_size = BitConverter.GetBytes(img.Length);
                    fullData = new byte[header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length + img_size.Length + img.Length];
                    header.CopyTo(fullData, 0);
                    id.CopyTo(fullData, header.Length);
                    ShowPicture.CopyTo(fullData, header.Length + id.Length);
                    UUV_X.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length);
                    UUV_Y.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length);
                    UUV_Z.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length);
                    UUV_RotationX.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length);
                    UUV_RotationY.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length);
                    UUV_RotationZ.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length);
                    MaoLei_Count.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length);
                    MaoLei_Coordinate.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length);
                    img_size.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length);
                    img.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length + img_size.Length);



                    //Buffer.BlockCopy(header,0,fullData,0,header.Length);
                    //Buffer.BlockCopy(id,0,fullData,header.Length,id.Length);
                    //Buffer.BlockCopy(ShowPicture,0,fullData,header.Length+id.Length,ShowPicture.Length);
                    //Buffer.BlockCopy(UUV_X,0,fullData, header.Length + id.Length + ShowPicture.Length, UUV_X.Length);
                    //Buffer.BlockCopy(UUV_Y,0,fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length, UUV_Y.Length);
                    //Buffer.BlockCopy(UUV_Z, 0, fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length, UUV_Z.Length);
                    //Buffer.BlockCopy(UUV_RotationX, 0, fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length, UUV_RotationX.Length);
                    //Buffer.BlockCopy(UUV_RotationY, 0, fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length, UUV_RotationY.Length); 
                    //Buffer.BlockCopy(UUV_RotationZ, 0, fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length, UUV_RotationZ.Length);
                    //Buffer.BlockCopy(MaoLei_Count, 0, fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length, MaoLei_Count.Length);
                    //Buffer.BlockCopy(MaoLei_Coordinate, 0, fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length, MaoLei_Coordinate.Length);
                    //Buffer.BlockCopy(img_size, 0, fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length, img_size.Length);
                    //Buffer.BlockCopy(img, 0, fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length + img_size.Length, img.Length);


                    if (fullData != null)
                    {
                        int m = fullData.Length / 65507;
                        int a = fullData.Length % 65507;

                        for (int k = 0; k < m; k++)
                        {
                            byte[] data1 = new byte[65507];
                            Array.Copy(fullData, 65507 * k, data1, 0, 65507);
                            uDPfordwardSonar.Send(data1, 65507, fordwardSonarRemotePoint);
                        }

                        byte[] data2 = new byte[a];
                        Array.Copy(fullData, 65507 * m, data2, 0, a);
                        uDPfordwardSonar.Send(data2, data2.Length, fordwardSonarRemotePoint);

                        //print($"前视发送的长度是{fullData.Length}");

                        //本地显示前视声纳图像，已测试通过
                        //rawImage_Qian.texture = ByteToTex2d(JPG3, tex);
                    }

                    //print($"uuv编号{i},   图片长度{BitConverter.ToInt32(img_size)}, {QS.Length}");
                    //string path = Application.streamingAssetsPath + "/" + i.ToString() + "_" + BitConverter.ToBoolean(ShowPicture).ToString() + "_" + new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds().ToString() + ".jpg";
                    //System.IO.File.WriteAllBytes(path, img);

                    await Task.Delay(Interval);//每张图间隔2毫秒
                }
            }
            catch
            {
                goto loop;
            }
            await Task.Delay(WaitTime);
        }
    }//旧框架
    async Task SendToCeSaoShengNa_Picture(int width, int height)
    {
       
        INIParser ini = new INIParser();
        ini.Open(Application.streamingAssetsPath + "/COM.ini");
        string sideSonarRemoteIP = ini.ReadValue("SideSonar", "RemoteIP", "127.0.0.1");
        string sideSonarRemotePort = ini.ReadValue("SideSonar", "Remoteport_Receive", "8085");
        string sideSonarLocalIP = ini.ReadValue("SideSonar", "LocalIP", "127.0.0.1");
        string sideSonarLocalPort = ini.ReadValue("SideSonar", "LocalPort_Send", "8084");
        ini.Close();

        UdpClient uDPSideSonar = new UdpClient(new IPEndPoint(IPAddress.Parse(sideSonarLocalIP), int.Parse(sideSonarLocalPort)));
        IPEndPoint sideSonarRemotePoint = new IPEndPoint(IPAddress.Parse(sideSonarRemoteIP), int.Parse(sideSonarRemotePort));

        byte[] header = new byte[4];
        byte[] uuv_id = new byte[17];
        byte[] time_t = new byte[13];
        byte[] img_size = new byte[4];
        byte[] img = new byte[99962];
        byte[] fullData = new byte[100000];
        Texture2D tex = new Texture2D(width, height);
        RenderTexture rt = new RenderTexture(width, height, 0);
        Texture2D screenShot = new Texture2D(width, height, TextureFormat.RGB24, false);
        List<RenderTexture> renderTextures = new List<RenderTexture>();//配合方法三使用
        for (int renderTexturesCount = 0; renderTexturesCount < 100; renderTexturesCount++)
        {
            renderTextures.Add(new RenderTexture(width, height, 0));
        }

        while (true)
        {

       loop:GameObject[] C = GameObject.FindGameObjectsWithTag("CeSaoShengNa");

            try
            {
                for (int i = 0; i < C.Length; i++)
                {

                    for (int enableNum = 0; enableNum < C.Length; enableNum++)
                    {
                        if (enableNum == i)
                        {
                            C[enableNum].GetComponent<Camera>().enabled = true;
                        }
                        else
                        {
                            C[enableNum].GetComponent<Camera>().enabled = false;
                        }
                    }

                    header = Encoding.ASCII.GetBytes("$SSS");
                    uuv_id = Encoding.ASCII.GetBytes((C[i].transform.parent.name.Substring(0, 2) + "_" + C[i].name).PadLeft(uuv_id.Length, '0'));
                    time_t = Encoding.ASCII.GetBytes(new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds().ToString());
                    img = CaptureCamera(C[i].GetComponent<Camera>(), renderTextures[i], screenShot, 95);
                    img_size = BitConverter.GetBytes(img.Length);

                    header.CopyTo(fullData, 0);
                    uuv_id.CopyTo(fullData, header.Length);
                    time_t.CopyTo(fullData, header.Length + uuv_id.Length);
                    img_size.CopyTo(fullData, header.Length + uuv_id.Length + time_t.Length);
                    img.CopyTo(fullData, header.Length + uuv_id.Length + time_t.Length + img_size.Length);
                    
                    if (fullData != null)
                    {
                        int m = fullData.Length / 65507;
                        int a = fullData.Length % 65507;

                        for (int k = 0; k < m; k++)
                        {
                            byte[] data1 = new byte[65507];
                            Array.Copy(fullData, 65507 * k, data1, 0, 65507);
                            uDPSideSonar.Send(data1, 65507, sideSonarRemotePoint);

                        }

                        byte[] data2 = new byte[a];
                        Array.Copy(fullData, 65507 * m, data2, 0, a);
                        uDPSideSonar.Send(data2, data2.Length, sideSonarRemotePoint);
                        await Task.Delay(2);

                        //保存图片
                        //string path = Application.streamingAssetsPath + "/" + new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds().ToString() + ".jpg";
                        //System.IO.File.WriteAllBytes(path, img);

                        //print($"uuv编号{i},   图片长度{BitConverter.ToInt32(img_size)}, {C.Length}");
                        //print($"侧扫发送的长度是{fullData.Length}");
                        //本地显示侧扫声纳图像，已测试通过
                        //rawImage_Ce.texture = ByteToTex2d(JPG4, tex);

                    }
                }
            }
            catch 
            {
                goto loop;
            }

            await Task.Delay(1000);
        }
    }//旧框架
    async Task SendToCeSaoShengNa_Picture_New(int width, int height)
    {

        INIParser ini = new INIParser();
        ini.Open(Application.streamingAssetsPath + "/COM.ini");
        string sideSonarLocalIP = ini.ReadValue("SideSonar", "LocalIP", "127.0.0.1");
        string sideSonarLocalPort = ini.ReadValue("SideSonar", "LocalPort_Send", "12348");
        ini.Close();
        print($"tcp://{sideSonarLocalIP}:{sideSonarLocalPort}");
        using (PublisherSocket pubSocket = new PublisherSocket($"tcp://{sideSonarLocalIP}:{sideSonarLocalPort}"))
        {
            //包含帧头、锚雷等信息
            /*
            byte[] header = new byte[4];
            byte[] uuv_id = new byte[17];
            byte[] time_t = new byte[13];
            byte[] img_size = new byte[4];
            byte[] img = new byte[99962];
            byte[] fullData = new byte[100000];
            Texture2D tex = new Texture2D(width, height);
            RenderTexture rt = new RenderTexture(width, height, 0);
            Texture2D screenShot = new Texture2D(width, height, TextureFormat.RGB24, false);
            List<RenderTexture> renderTextures = new List<RenderTexture>();//配合方法三使用
            for (int renderTexturesCount = 0; renderTexturesCount < 100; renderTexturesCount++)
            {
                renderTextures.Add(new RenderTexture(width, height, 0));
            }

            while (!cts3.Token.IsCancellationRequested)
            {

            loop: GameObject[] C = GameObject.FindGameObjectsWithTag("CeSaoShengNa");

                try
                {
                    for (int i = 0; i < C.Length; i++)
                    {

                        for (int enableNum = 0; enableNum < C.Length; enableNum++)
                        {
                            if (enableNum == i)
                            {
                                C[enableNum].GetComponent<Camera>().enabled = true;
                            }
                            else
                            {
                                C[enableNum].GetComponent<Camera>().enabled = false;
                            }
                        }

                        header = Encoding.ASCII.GetBytes("$SSS");
                        uuv_id = Encoding.ASCII.GetBytes((C[i].transform.parent.name.Substring(0, 2) + "_" + C[i].name).PadLeft(uuv_id.Length, '0'));
                        time_t = Encoding.ASCII.GetBytes(new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds().ToString());
                        img = CaptureCamera(C[i].GetComponent<Camera>(), renderTextures[i], screenShot, 95);
                        img_size = BitConverter.GetBytes(img.Length);

                        header.CopyTo(fullData, 0);
                        uuv_id.CopyTo(fullData, header.Length);
                        time_t.CopyTo(fullData, header.Length + uuv_id.Length);
                        img_size.CopyTo(fullData, header.Length + uuv_id.Length + time_t.Length);
                        img.CopyTo(fullData, header.Length + uuv_id.Length + time_t.Length + img_size.Length);

                        if (fullData != null)
                        {
                            pubSocket.SendMoreFrame("CameraPicture").SendMultipartBytes(fullData);
                            
                            await Task.Delay(2);

                            //保存图片
                            //string path = Application.streamingAssetsPath + "/" + new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds().ToString() + ".jpg";
                            //System.IO.File.WriteAllBytes(path, img);

                            //print($"uuv编号{i},   图片长度{BitConverter.ToInt32(img_size)}, {C.Length}");
                            //print($"侧扫发送的长度是{fullData.Length}");
                            //本地显示侧扫声纳图像，已测试通过
                            //rawImage_Ce.texture = ByteToTex2d(JPG4, tex);

                        }
                    }
                }
                catch
                {
                    goto loop;
                }

                await Task.Delay(100);
            }
            */


            //只包含图片数据
            byte[] img ;
            Texture2D tex = new Texture2D(width, height);
            RenderTexture rt = new RenderTexture(width, height, 0);
            Texture2D screenShot = new Texture2D(width, height, TextureFormat.RGB24, false);
            List<RenderTexture> renderTextures = new List<RenderTexture>();//配合方法三使用
            for (int renderTexturesCount = 0; renderTexturesCount < 100; renderTexturesCount++)
            {
                renderTextures.Add(new RenderTexture(width, height, 0));
            }

            while (!cts3.Token.IsCancellationRequested)
            {

            loop: GameObject[] C = GameObject.FindGameObjectsWithTag("CeSaoShengNa");

                try
                {
                    for (int i = 0; i < C.Length; i++)
                    {

                        for (int enableNum = 0; enableNum < C.Length; enableNum++)
                        {
                            if (enableNum == i)
                            {
                                C[enableNum].GetComponent<Camera>().enabled = true;
                            }
                            else
                            {
                                C[enableNum].GetComponent<Camera>().enabled = false;
                            }
                        }

                        img = CaptureCamera(C[i].GetComponent<Camera>(), renderTextures[i], screenShot, 95);

                        if (img != null)
                        {
                            pubSocket.SendMoreFrame("CameraPicture").SendMultipartBytes(img);

                            await Task.Delay(2);

                            //保存图片
                            //string path = Application.streamingAssetsPath + "/" + new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds().ToString() + ".jpg";
                            //System.IO.File.WriteAllBytes(path, img);

                            //print($"uuv编号{i},   图片长度{BitConverter.ToInt32(img_size)}, {C.Length}");
                            //print($"侧扫发送的长度是{fullData.Length}");
                            //本地显示侧扫声纳图像，已测试通过
                            //rawImage_Ce.texture = ByteToTex2d(JPG4, tex);

                        }
                    }
                }
                catch
                {
                    goto loop;
                }

                await Task.Delay(100);
            }
        }
      

      

    }//新框架
    async Task SendToCeSaoShengNa_MaoLei()
    {
     
        INIParser ini = new INIParser();
        ini.Open(Application.streamingAssetsPath + "/COM.ini");
        string sideSonarRemoteIP = ini.ReadValue("SideSonar", "RemoteIP", "127.0.0.1");
        string sideSonarRemotePort = ini.ReadValue("SideSonar", "Remoteport_Receive_MaoLei", "8087");//文本文档中需要新增字段
        string sideSonarLocalIP = ini.ReadValue("SideSonar", "LocalIP", "127.0.0.1");
        string sideSonarLocalPort = ini.ReadValue("SideSonar", "LocalPort_Send_MaoLei", "8086");//文本文档中需要新增字段
        ini.Close();
      
        UdpClient uDPSideSonar = new UdpClient(new IPEndPoint(IPAddress.Parse(sideSonarLocalIP), int.Parse(sideSonarLocalPort)));
        IPEndPoint sideSonarRemotePoint = new IPEndPoint(IPAddress.Parse(sideSonarRemoteIP), int.Parse(sideSonarRemotePort));
    
        byte[] header = Encoding.ASCII.GetBytes("$SSS");//帧头
        byte[] MaoLei_Count = new byte[4];//目标物数量
        byte[] MaoLei_Coordinate;//目标物坐标，长度为3*目标物数量
        byte[] fullData;
      
        while (true)
        {
      loop: GameObject[] maolei = GameObject.FindGameObjectsWithTag("MaoLei");

            try
            {
                MaoLei_Count = BitConverter.GetBytes(maolei.Length);
                MaoLei_Coordinate = new byte[12 * maolei.Length];
                for (int i = 0; i < maolei.Length; i++)
                {
                    BitConverter.GetBytes(maolei[i].transform.position.x).CopyTo(MaoLei_Coordinate, 12 * i + 0);
                    BitConverter.GetBytes(maolei[i].transform.position.y).CopyTo(MaoLei_Coordinate, 12 * i + 4);
                    BitConverter.GetBytes(maolei[i].transform.position.z).CopyTo(MaoLei_Coordinate, 12 * i + 8);

                }
                //打印需要发送的数据
                //for (int aa = 0;  aa < maolei.Length; aa++)
                //{
                //    print($"第{aa}号锚雷坐标{BitConverter.ToSingle(MaoLei_Coordinate, 12 * aa + 0)}，{BitConverter.ToSingle(MaoLei_Coordinate, 12 * aa + 4)}，{BitConverter.ToSingle(MaoLei_Coordinate, 12 * aa + 8)}");
                //}
                //print($"{BitConverter.ToSingle(new byte[] { 0x00,0x00,0x7A,0x43})},{BitConverter.ToSingle(new byte[] { 0x00, 0x00, 0x16, 0xC3 })},{BitConverter.ToSingle(new byte[] { 0x00, 0x00, 0x1C, 0x42 })}");
                fullData = new byte[header.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length];
                header.CopyTo(fullData, 0);
                MaoLei_Count.CopyTo(fullData, header.Length);
                MaoLei_Coordinate.CopyTo(fullData, header.Length + MaoLei_Count.Length);

                if (fullData != null)
                {
                    int m = fullData.Length / 65507;
                    int a = fullData.Length % 65507;

                    for (int k = 0; k < m; k++)
                    {
                        byte[] data1 = new byte[65507];
                        Array.Copy(fullData, 65507 * k, data1, 0, 65507);
                        uDPSideSonar.Send(data1, 65507, sideSonarRemotePoint);

                    }

                    byte[] data2 = new byte[a];
                    Array.Copy(fullData, 65507 * m, data2, 0, a);
                    uDPSideSonar.Send(data2, data2.Length, sideSonarRemotePoint);
                }
                //print($"发给侧扫声纳锚雷数据长度：  {fullData.Length}字节");
            }
            catch
            {
            goto loop;
            }

            await Task.Delay(1500);
        }
    }//旧框架
    async Task SendToCeSaoShengNa_MaoLei_New()
    {

        INIParser ini = new INIParser();
        ini.Open(Application.streamingAssetsPath + "/COM.ini");
        string sideSonarLocalIP = ini.ReadValue("SideSonar", "LocalIP", "127.0.0.1");
        string sideSonarLocalPort = ini.ReadValue("SideSonar", "LocalPort_Send_MaoLei", "12349");
        ini.Close();

        using (PublisherSocket pubSocket = new PublisherSocket($"tcp://{sideSonarLocalIP}:{sideSonarLocalPort}"))
        {

            byte[] header = Encoding.ASCII.GetBytes("$SSS");//帧头
            byte[] MaoLei_Count = new byte[4];//目标物数量
            byte[] MaoLei_Coordinate;//目标物坐标，长度为3*目标物数量
            byte[] fullData;

            while (!cts4.Token.IsCancellationRequested)
            {
             loop: GameObject[] maolei = GameObject.FindGameObjectsWithTag("MaoLei");

                try
                {
                    MaoLei_Count = BitConverter.GetBytes(maolei.Length);
                    MaoLei_Coordinate = new byte[12 * maolei.Length];
                    for (int i = 0; i < maolei.Length; i++)
                    {
                        BitConverter.GetBytes(maolei[i].transform.position.x).CopyTo(MaoLei_Coordinate, 12 * i + 0);
                        BitConverter.GetBytes(maolei[i].transform.position.y).CopyTo(MaoLei_Coordinate, 12 * i + 4);
                        BitConverter.GetBytes(maolei[i].transform.position.z).CopyTo(MaoLei_Coordinate, 12 * i + 8);

                    }
                    //打印需要发送的数据
                    //for (int aa = 0;  aa < maolei.Length; aa++)
                    //{
                    //    print($"第{aa}号锚雷坐标{BitConverter.ToSingle(MaoLei_Coordinate, 12 * aa + 0)}，{BitConverter.ToSingle(MaoLei_Coordinate, 12 * aa + 4)}，{BitConverter.ToSingle(MaoLei_Coordinate, 12 * aa + 8)}");
                    //}
                    //print($"{BitConverter.ToSingle(new byte[] { 0x00,0x00,0x7A,0x43})},{BitConverter.ToSingle(new byte[] { 0x00, 0x00, 0x16, 0xC3 })},{BitConverter.ToSingle(new byte[] { 0x00, 0x00, 0x1C, 0x42 })}");
                    fullData = new byte[header.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length];
                    header.CopyTo(fullData, 0);
                    MaoLei_Count.CopyTo(fullData, header.Length);
                    MaoLei_Coordinate.CopyTo(fullData, header.Length + MaoLei_Count.Length);

                    if (fullData != null)
                    {
                        pubSocket.SendMoreFrame("MineCount").SendMultipartBytes(fullData);
                    }
                    //print($"发给侧扫声纳锚雷数据长度：  {fullData.Length}字节");
                }
                catch
                {
                    goto loop;
                }

                await Task.Delay(1500);
            }
        }

        
    }//新框架
    void ReceiveFromQianShiShengNa()
    {
        Task.Factory.StartNew(() =>
        {
            INIParser ini = new INIParser();
            ini.Open(Application.streamingAssetsPath + "/COM.ini");
            string fordwardSonarRemoteIP = ini.ReadValue("FordwardSonar", "RemoteIP", "127.0.0.1");
            string fordwardSonarRemotePort = ini.ReadValue("FordwardSonar", "Remoteport_Send", "8888");
            string fordwardSonarLocalIP = ini.ReadValue("FordwardSonar", "LocalIP", "127.0.0.1");
            string fordwardSonarLocalPort = ini.ReadValue("FordwardSonar", "LocalPort_Receive", "8889");
            ini.Close();

            //项目IP（与另一台电脑通信）
            //UdpClient uDPfordwardSonar = new UdpClient(new IPEndPoint(IPAddress.Parse(fordwardSonarLocalIP), int.Parse(fordwardSonarLocalPort)));
            //IPEndPoint fordwardSonarRemotePoint = new IPEndPoint(IPAddress.Parse(fordwardSonarRemoteIP), int.Parse(fordwardSonarRemotePort));
            //自测IP（与自己电脑通信）
            UdpClient uDPfordwardSonar = new UdpClient(new IPEndPoint(IPAddress.Parse("127.0.0.1"), int.Parse("1234")));
            IPEndPoint fordwardSonarRemotePoint = new IPEndPoint(IPAddress.Parse("127.0.0.1"), int.Parse("7804"));

            byte[] header = new byte[4];//帧头
            byte[] id = new byte[4];//id
            byte[] ShowPicture = new byte[1];//是否显示声纳图像
            byte[] UUV_X = new byte[4];//uuv坐标x
            byte[] UUV_Y = new byte[4];//uuv坐标y
            byte[] UUV_Z = new byte[4];//uuv坐标z
            byte[] UUV_RotationX = new byte[4];
            byte[] UUV_RotationY = new byte[4];
            byte[] UUV_RotationZ = new byte[4];
            byte[] MaoLei_Count = new byte[4];//目标物数量
            byte[] MaoLei_Coordinate;//目标物坐标，长度为3*目标物数量
            byte[] img_size = new byte[4];
            byte[] img;
            byte[] fullData;
            while (true)
            {

                fullData = uDPfordwardSonar.Receive(ref fordwardSonarRemotePoint);

                header = fullData.Skip(0).Take(4).ToArray();
                id = fullData.Skip(header.Length).Take(4).ToArray();
                ShowPicture = fullData.Skip(header.Length +id.Length).Take(1).ToArray();
                UUV_X = fullData.Skip(header.Length + id.Length + ShowPicture.Length).Take(4).ToArray();
                UUV_Y = fullData.Skip(header.Length + id.Length + ShowPicture.Length + UUV_X.Length).Take(4).ToArray();
                UUV_Z = fullData.Skip(header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length).Take(4).ToArray();
                UUV_RotationX = fullData.Skip(header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length).Take(4).ToArray();
                UUV_RotationY = fullData.Skip(header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length).Take(4).ToArray();
                UUV_RotationZ = fullData.Skip(header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length).Take(4).ToArray();
                MaoLei_Count = fullData.Skip(header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length).Take(4).ToArray();
                MaoLei_Coordinate = fullData.Skip(header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length).Take(BitConverter.ToInt32(MaoLei_Count) * 12).ToArray();
                //for (int i = 0; i < BitConverter.ToInt32(MaoLei_Count); i++)
                //{
                //    byte[] x = new byte[4];
                //    byte[] y = new byte[4];
                //    byte[] z = new byte[4];
                //    x = MaoLei_Coordinate.Skip(12 * i + 0).Take(4).ToArray();
                //    y = MaoLei_Coordinate.Skip(12 * i + 4).Take(4).ToArray();
                //    z = MaoLei_Coordinate.Skip(12 * i + 8).Take(4).ToArray();
                //    print($"{i}号目标物坐标({BitConverter.ToSingle(x)},{BitConverter.ToSingle(y)},{BitConverter.ToSingle(z)})");
                //}
                img_size = fullData.Skip(header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length).Take(4).ToArray();
                img = fullData.Skip(header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length + img_size.Length).Take(BitConverter.ToInt32(img_size)).ToArray();
             
                JPG3 = fullData.Skip(header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length + img_size.Length).Take(BitConverter.ToInt32(img_size)).ToArray();
            }
        });
    }
    void ReceiveFromCeSaoShengNa_Picture()
    {
        Task.Factory.StartNew(() =>
        {

            INIParser ini = new INIParser();
            ini.Open(Application.streamingAssetsPath + "/COM.ini");
            string sideSonarRemoteIP = ini.ReadValue("SideSonar", "RemoteIP", "127.0.0.1");
            string sideSonarRemotePort = ini.ReadValue("SideSonar", "Remoteport_Send", "8081");
            string sideSonarLocalIP = ini.ReadValue("SideSonar", "LocalIP", "127.0.0.1");
            string sideSonarLocalPort = ini.ReadValue("SideSonar", "LocalPort_Receive", "8080");
            ini.Close();

            ////项目IP（与另一台电脑通信）
            //UdpClient uDPSideSonar = new UdpClient(new IPEndPoint(IPAddress.Parse(sideSonarLocalIP), int.Parse(sideSonarLocalPort)));
            //IPEndPoint sideSonarRemotePoint = new IPEndPoint(IPAddress.Parse(sideSonarRemoteIP), int.Parse(sideSonarRemotePort));
            ////项目缓冲区
            //byte[] header = new byte[4];
            //byte[] uuv_id = new byte[17];
            //byte[] time_t = new byte[13];
            //byte[] img_size = new byte[5];
            //byte[] img = new byte[99962];
            //byte[] fullData = new byte[100001];
            //while (true)
            //{
            //    //项目接收
            //    int bytesRead = 0;
            //    byte[] data;
            //    while (bytesRead < 100000)
            //    {
            //        data = uDPSideSonar.Receive(ref sideSonarRemotePoint);
            //        data.CopyTo(fullData, bytesRead);
            //        bytesRead += data.Length;
            //        //print($"侧扫收到的长度是{data.Length}");
            //    }
            //    //项目解析
            //    header = fullData.Skip(0).Take(header.Length).ToArray();
            //    uuv_id = fullData.Skip(header.Length).Take(uuv_id.Length).ToArray();
            //    print($"侧扫声纳发来的id为：{Encoding.ASCII.GetString(uuv_id)}");
            //    time_t = fullData.Skip(header.Length + uuv_id.Length).Take(time_t.Length).ToArray();
            //    img_size = fullData.Skip(header.Length + uuv_id.Length + time_t.Length).Take(img_size.Length).ToArray();
            //    img = fullData.Skip(header.Length + uuv_id.Length + time_t.Length + img_size.Length).Take(int.Parse(Encoding.ASCII.GetString(img_size))).ToArray();
            //    JPG4 = fullData.Skip(header.Length + uuv_id.Length + time_t.Length + img_size.Length).Take(int.Parse(Encoding.ASCII.GetString(img_size))).ToArray();
            //}




            //自测IP（与自己电脑通信）
            UdpClient uDPSideSonar = new UdpClient(new IPEndPoint(IPAddress.Parse("127.0.0.1"), int.Parse("9039")));
            IPEndPoint sideSonarRemotePoint = new IPEndPoint(IPAddress.Parse("127.0.0.1"), int.Parse("12348"));
            //自测缓冲区
            byte[] header = new byte[4];
            byte[] uuv_id = new byte[17];
            byte[] time_t = new byte[13];
            byte[] img_size = new byte[4];
            byte[] img = new byte[99962];
            byte[] fullData = new byte[100000];

            while (true)
            {
                //    自测接收
                int bytesRead = 0;
                byte[] data;
                while (bytesRead < 100000)
                {
                    data = uDPSideSonar.Receive(ref sideSonarRemotePoint);
                    data.CopyTo(fullData, bytesRead);
                    bytesRead += data.Length;
                    //print($"侧扫收到的长度是{data.Length}");
                }

                //    自测解析
                header = fullData.Skip(0).Take(header.Length).ToArray();
                uuv_id = fullData.Skip(header.Length).Take(uuv_id.Length).ToArray();
                print(Encoding.ASCII.GetString(uuv_id));
                time_t = fullData.Skip(header.Length + uuv_id.Length).Take(time_t.Length).ToArray();
                img_size = fullData.Skip(header.Length + uuv_id.Length + time_t.Length).Take(img_size.Length).ToArray();
                JPG4 = fullData.Skip(header.Length + uuv_id.Length + time_t.Length + img_size.Length).Take(BitConverter.ToInt32(img_size)).ToArray();
            }
        });
    }
    async Task SendToQianShiShengNaTest()
    {
        INIParser ini = new INIParser();
        ini.Open(Application.streamingAssetsPath + "/COM.ini");
        string fordwardSonarRemoteIP = ini.ReadValue("FordwardSonar", "RemoteIP", "127.0.0.1");
        string fordwardSonarRemotePort = ini.ReadValue("FordwardSonar", "Remoteport_Receive", "8888");
        string fordwardSonarLocalIP = ini.ReadValue("FordwardSonar", "LocalIP", "127.0.0.1");
        string fordwardSonarLocalPort = ini.ReadValue("FordwardSonar", "LocalPort_Send", "8889");
        ini.Close();

        UdpClient uDPfordwardSonar = new UdpClient(new IPEndPoint(IPAddress.Parse(fordwardSonarLocalIP), int.Parse(fordwardSonarLocalPort)));
        IPEndPoint fordwardSonarRemotePoint = new IPEndPoint(IPAddress.Parse(fordwardSonarRemoteIP), int.Parse(fordwardSonarRemotePort));

        Texture2D tex = new Texture2D(512, 256);
        RenderTexture rt = new RenderTexture(512, 256, 0);
        Texture2D screenShot = new Texture2D(512, 256, TextureFormat.RGB24, false);
        dataToQS dataToQS = new dataToQS();
        dataToQS.MaoLei_Coordinate = new byte[1200];
        dataToQS.img = new byte[8000];
        int size = Marshal.SizeOf(dataToQS);
        byte[] fullData = new byte[size];
        IntPtr structPtr = Marshal.AllocHGlobal(size);
      
        while (true)
        {
           
            GameObject[] QS = GameObject.FindGameObjectsWithTag("QianShiShengNa");
            GameObject[] maolei = GameObject.FindGameObjectsWithTag("MaoLei");
            for (int i = 0; i < QS.Length; i++)
            {
               
                dataToQS.header = "$FSP";
                dataToQS.id = int.Parse(QS[i].transform.parent.name.Substring(0, 1));
                dataToQS.UUV_X = QS[i].transform.parent.position.x;
                dataToQS.UUV_Y = QS[i].transform.parent.position.y;
                dataToQS.UUV_Z = QS[i].transform.parent.position.z;
                dataToQS.UUV_rotationx = QS[i].transform.parent.rotation.x;
                dataToQS.UUV_rotationy = QS[i].transform.parent.rotation.y;
                dataToQS.UUV_rotationz = QS[i].transform.parent.rotation.z;
                dataToQS.maoleiCount = maolei.Length;
               

                for (int j = 0; j < maolei.Length; j++)
                {
                    BitConverter.GetBytes(maolei[j].transform.position.x).CopyTo(dataToQS.MaoLei_Coordinate, 12 * j + 0);
                    BitConverter.GetBytes(maolei[j].transform.position.y).CopyTo(dataToQS.MaoLei_Coordinate, 12 * j + 4);
                    BitConverter.GetBytes(maolei[j].transform.position.z).CopyTo(dataToQS.MaoLei_Coordinate, 12 * j + 8);
                    //print($"{j}号障碍物位置（{BitConverter.ToSingle(BitConverter.GetBytes(maolei[i].transform.position.x))}，{BitConverter.ToSingle(BitConverter.GetBytes(maolei[i].transform.position.y))}，{BitConverter.ToSingle(BitConverter.GetBytes(maolei[i].transform.position.z))}）");
                    print(dataToQS.MaoLei_Coordinate[0]);
                }
               
                QS[i].GetComponent<Camera>().targetTexture = renderTexture;
                dataToQS.img = rt2JPG(renderTexture, screenShot);
                dataToQS.imgsize = dataToQS.img.Length;

                Marshal.StructureToPtr(dataToQS, structPtr, true);
                Marshal.Copy(structPtr, fullData, 0, size);
                if (fullData != null)
                {
                    int m = fullData.Length / 65507;
                    int a = fullData.Length % 65507;

                    for (int k = 0; k < m; k++)
                    {
                        byte[] data1 = new byte[65507];
                        Array.Copy(fullData, 65507 * k, data1, 0, 65507);
                        uDPfordwardSonar.Send(data1, 65507, fordwardSonarRemotePoint);
                    }

                    byte[] data2 = new byte[a];
                    Array.Copy(fullData, 65507 * m, data2, 0, a);
                    uDPfordwardSonar.Send(data2, data2.Length, fordwardSonarRemotePoint);
                }
                rawImage_Qian.texture = ByteToTex2d(JPG3, tex);
            }
            await Task.Delay(100);
        }

    }
    void ReceiveFromQianShiShengNaTest()
    {
        Task.Factory.StartNew(() =>
        {
            INIParser ini = new INIParser();
            ini.Open(Application.streamingAssetsPath + "/COM.ini");
            string fordwardSonarRemoteIP = ini.ReadValue("FordwardSonar", "RemoteIP", "127.0.0.1");
            string fordwardSonarRemotePort = ini.ReadValue("FordwardSonar", "Remoteport_Send", "8888");
            string fordwardSonarLocalIP = ini.ReadValue("FordwardSonar", "LocalIP", "127.0.0.1");
            string fordwardSonarLocalPort = ini.ReadValue("FordwardSonar", "LocalPort_Receive", "8889");
            ini.Close();

           //自测IP（与自己电脑通信）
            UdpClient uDPfordwardSonar = new UdpClient(new IPEndPoint(IPAddress.Parse("127.0.0.1"), int.Parse("12345")));
            IPEndPoint fordwardSonarRemotePoint = new IPEndPoint(IPAddress.Parse("127.0.0.1"), int.Parse("7084"));

            byte[] fullData;
            dataToQS dataToQS;
            while (true)
            {
                fullData = uDPfordwardSonar.Receive(ref fordwardSonarRemotePoint);
                dataToQS = (dataToQS)BytesStruct(fullData,typeof(dataToQS));
                JPG3 = dataToQS.img;
            }
        });
    }
    async Task SendToQianShiShengNaNaNa(int width, int height)
    {
        INIParser ini = new INIParser();
        ini.Open(Application.streamingAssetsPath + "/COM.ini");
        string fordwardSonarRemoteIP = ini.ReadValue("FordwardSonar", "RemoteIP", "127.0.0.1");
        string fordwardSonarRemotePort = ini.ReadValue("FordwardSonar", "Remoteport_Receive", "8888");
        string fordwardSonarLocalIP = ini.ReadValue("FordwardSonar", "LocalIP", "127.0.0.1");
        string fordwardSonarLocalPort = ini.ReadValue("FordwardSonar", "LocalPort_Send", "8889");
        int Interval = int.Parse(ini.ReadValue("FordwardSonar", "Interval", "10"));
        int WaitTime = int.Parse(ini.ReadValue("FordwardSonar", "WaitTime", "10"));
        ini.Close();

        UdpClient uDPfordwardSonar = new UdpClient(new IPEndPoint(IPAddress.Parse(fordwardSonarLocalIP), int.Parse(fordwardSonarLocalPort)));
        IPEndPoint fordwardSonarRemotePoint = new IPEndPoint(IPAddress.Parse(fordwardSonarRemoteIP), int.Parse(fordwardSonarRemotePort));

        byte[] header = Encoding.ASCII.GetBytes("$FSP");//帧头
        byte[] id = new byte[4];//id
        byte[] ShowPicture = new byte[1];//是否显示声纳图像
        byte[] UUV_X = new byte[4];//uuv坐标x
        byte[] UUV_Y = new byte[4];//uuv坐标y
        byte[] UUV_Z = new byte[4];//uuv坐标z
        byte[] UUV_RotationX = new byte[4];
        byte[] UUV_RotationY = new byte[4];
        byte[] UUV_RotationZ = new byte[4];
        byte[] MaoLei_Count = new byte[4];//目标物数量
        byte[] MaoLei_Coordinate;//目标物坐标，长度为3*目标物数量
        byte[] img_size = new byte[4];
        byte[] img;
        byte[] fullData;

        Texture2D tex = new Texture2D(width, height);
        RenderTexture rt = new RenderTexture(width, height, 0);
        Texture2D screenShot = new Texture2D(width, height, TextureFormat.RGB24, false);
        List<RenderTexture> renderTextures = new List<RenderTexture>();//配合方法三使用
        for (int renderTexturesCount = 0; renderTexturesCount < 100; renderTexturesCount++)
        {
            renderTextures.Add(new RenderTexture(width, height, 0));
        }
        Stopwatch sw1 = Stopwatch.StartNew();//3毫秒间隔
        Stopwatch sw2 = Stopwatch.StartNew();//3毫秒间隔

        while (true)
        {
        loop: GameObject[] QS = GameObject.FindGameObjectsWithTag("QianShiShengNa");
            GameObject[] maolei = GameObject.FindGameObjectsWithTag("MaoLei");
            try
            {
                 Parallel.For(0, QS.Length, async(i) => {
                     for (int enableNum = 0; enableNum < QS.Length; enableNum++)
                     {
                         if (enableNum == i)
                         {
                             QS[enableNum].GetComponent<Camera>().enabled = true;
                         }
                         else
                         {
                             QS[enableNum].GetComponent<Camera>().enabled = false;
                         }
                     }

                     id = BitConverter.GetBytes(int.Parse(QS[i].transform.parent.name.Substring(0, 1)));
                     ShowPicture = BitConverter.GetBytes(i == UuvNum ? true : false);
                     UUV_X = BitConverter.GetBytes(QS[i].transform.parent.position.x);
                     UUV_Y = BitConverter.GetBytes(QS[i].transform.parent.position.y);
                     UUV_Z = BitConverter.GetBytes(QS[i].transform.parent.position.z);
                     UUV_RotationX = BitConverter.GetBytes(QS[i].transform.parent.rotation.x);
                     UUV_RotationY = BitConverter.GetBytes(QS[i].transform.parent.rotation.y);
                     UUV_RotationZ = BitConverter.GetBytes(QS[i].transform.parent.rotation.z);
                     MaoLei_Count = BitConverter.GetBytes(maolei.Length);
                     MaoLei_Coordinate = new byte[maolei.Length * 12];
                     for (int j = 0; j < maolei.Length; j++)
                     {
                         BitConverter.GetBytes(maolei[j].transform.position.x).CopyTo(MaoLei_Coordinate, 12 * j + 0);
                         BitConverter.GetBytes(maolei[j].transform.position.y).CopyTo(MaoLei_Coordinate, 12 * j + 4);
                         BitConverter.GetBytes(maolei[j].transform.position.z).CopyTo(MaoLei_Coordinate, 12 * j + 8);
                         //print($"{j}号障碍物位置（{BitConverter.ToSingle(BitConverter.GetBytes(maolei[i].transform.position.x))}，{BitConverter.ToSingle(BitConverter.GetBytes(maolei[i].transform.position.y))}，{BitConverter.ToSingle(BitConverter.GetBytes(maolei[i].transform.position.z))}）");
                     }

                     //方法一：原始方法，显示不出来链条
                     //img = CaptureCamera(QS[i].GetComponent<Camera>(), rt, screenShot, 30);
                     img = CaptureCamera(QS[i].GetComponent<Camera>(), renderTextures[i], screenShot, 30);
                     //方法二：手动新建public的RenderTexture,ColorFormat设置为R8G8B8A8_SRGB,Depth设置为D32_SFLOAT_S8_UINT
                     //QS[i].GetComponent<Camera>().targetTexture = renderTexture;
                     //img = rt2JPG(renderTexture, screenShot);
                     //方法三：为每一个相机配备一个rendertexture,耗时最短，只自测，未联调。如果共用同一个rendertexture,生成的画面不变
                     //QS[i].GetComponent<Camera>().targetTexture = renderTextures[i];
                     //img = rt2JPG(renderTextures[i], screenShot);

                     img_size = BitConverter.GetBytes(img.Length);
                     fullData = new byte[header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length + img_size.Length + img.Length];
                     header.CopyTo(fullData, 0);
                     id.CopyTo(fullData, header.Length);
                     ShowPicture.CopyTo(fullData, header.Length + id.Length);
                     UUV_X.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length);
                     UUV_Y.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length);
                     UUV_Z.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length);
                     UUV_RotationX.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length);
                     UUV_RotationY.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length);
                     UUV_RotationZ.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length);
                     MaoLei_Count.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length);
                     MaoLei_Coordinate.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length);
                     img_size.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length);
                     img.CopyTo(fullData, header.Length + id.Length + ShowPicture.Length + UUV_X.Length + UUV_Y.Length + UUV_Z.Length + UUV_RotationX.Length + UUV_RotationY.Length + UUV_RotationZ.Length + MaoLei_Count.Length + MaoLei_Coordinate.Length + img_size.Length);

                     if (fullData != null)
                     {
                         int m = fullData.Length / 65507;
                         int a = fullData.Length % 65507;

                         for (int k = 0; k < m; k++)
                         {
                             byte[] data1 = new byte[65507];
                             Array.Copy(fullData, 65507 * k, data1, 0, 65507);
                             uDPfordwardSonar.Send(data1, 65507, fordwardSonarRemotePoint);
                         }

                         byte[] data2 = new byte[a];
                         Array.Copy(fullData, 65507 * m, data2, 0, a);
                         uDPfordwardSonar.Send(data2, data2.Length, fordwardSonarRemotePoint);

                         //print($"前视发送的长度是{fullData.Length}");

                         //本地显示前视声纳图像，已测试通过
                         //rawImage_Qian.texture = ByteToTex2d(JPG3, tex);
                     }

                     //print($"uuv编号{i},   图片长度{BitConverter.ToInt32(img_size)}, {QS.Length}");
                     //string path = Application.streamingAssetsPath + "/" + new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds().ToString() + ".jpg";
                     //System.IO.File.WriteAllBytes(path, img);

                     //await Task.Delay(Interval);//每张图间隔2毫秒
                 });
             
            }
            catch
            {
                goto loop;
            }

            await Task.Delay(WaitTime);
        }
    }//并行计算
    
    
    private void OnDestroy()
    { 
        NetMQConfig.Cleanup(false);
        /*C++通信资源清理
          * **/
#if (NewFramework)
        {
            cts1.Cancel();
            cts2.Cancel();
            cts3.Cancel();
            cts4.Cancel();
            csharp_quit();
            csharp_uinit();
        }

#endif
    }

    #region 【16】子程序图片转换
    byte[]  CaptureCamera(Camera camera, RenderTexture rt, Texture2D screenShot,int Compressionratio)
    {
        camera.targetTexture = rt;
        camera.Render();//显示链条的去掉这行
        RenderTexture.active = rt;

        screenShot.ReadPixels(new UnityEngine.Rect(0, 0, rt.width, rt.height), 0, 0);
        screenShot.Apply();

        camera.targetTexture = null;//显示链条的去掉这行
        RenderTexture.active = null;

        byte[] bytes = screenShot.EncodeToJPG(Compressionratio);
        return bytes;
    }

     byte[]  rt2JPG(RenderTexture rt, Texture2D screenShot)
    {
        RenderTexture.active = rt;
        screenShot.ReadPixels(new UnityEngine.Rect(0, 0, rt.width, rt.height), 0, 0);
        screenShot.Apply();

        byte[] bytes = screenShot.EncodeToJPG(95);
        RenderTexture.active = null;
        return bytes;
    }
    Texture2D ByteToTex2d(byte[] bytes, Texture2D tex)
    {
        tex.LoadImage(bytes);
        return tex;
    }
    #endregion

    #region【17】子程序经纬度计算
    double xy2Longitude(double referLongitude,double referLatitude,float x,float y)
    {
        const double PI = 3.14159265358979323846;
        double eR = 6371393;
        double latNew = referLatitude + x * (360.000 / (eR * 2 * PI));
        // 纬度切面的半径
        double latR = eR * Math.Cos(latNew * PI / 180.000);
        double lngNew = referLongitude + y * (360.000 / (latR * 2 * PI));
        return lngNew;
    }
    double xy2Latitude(double referLongitude, double referLatitude, float x, float y)
    {
        const double PI = 3.14159265358979323846;
        double eR = 6371393;
        double latNew = referLatitude + x * (360.000 / (eR * 2 * PI));
        // 纬度切面的半径
        double latR = eR * Math.Cos(latNew * PI / 180.000);
        double lngNew = referLongitude + y * (360.000 / (latR * 2 * PI));
        return latNew;
    }
    #endregion
    class UuvError
    {
        public string type { get; set; }
        public int[] errorUuvNum { get; set; }
        public UuvError(string _type, int[] _num )
        {
            type = _type;
            errorUuvNum = _num;
        }
    }

    class UuvState 
    {
        public int i
        {
            get;
            set;
        }
        public float x
        {
            get;
            set;
        }
        public float y
        {
            get;
            set;
        }
        public float z
        {
            get;
            set;
        }
        public float pitch
        {
            get;
            set;
        }
        public float roll
        {
            get;
            set;
        }
        public float yaw
        {
            get;
            set;
        }

        //u
        public float speed_now
        {
            get;
            set;
        }
        
        //q
        public float v_pitch_now
        {
            get;
            set;
        }

        //r
        public float v_yaw_now
        {
            get;
            set;
        }
        public double longitude
        {
            get;
            set;
        }
        public double latitude
        {
            get;
            set;
        }
        public float depth
        {
            get;
            set;
        }
        public int mineCount
        {
            get;
            set;
        }
    }

    //https://www.cnblogs.com/dafanjoy/p/7818126.html
    [StructLayout(layoutKind:LayoutKind.Sequential,CharSet = CharSet.Ansi,Pack =1)]
    struct dataToQS 
    {
        [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 5)]
        public string header;//4个字符 + 1个\0

        public int id;
        public float UUV_X;
        public float UUV_Y;
        public float UUV_Z;
        public float UUV_rotationx;
        public float UUV_rotationy;
        public float UUV_rotationz;
        public int maoleiCount;

        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 1200)]
        public byte[] MaoLei_Coordinate;
        public int imgsize;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8000)]
        public byte[] img;
    }
    public static byte[] StructToBytes(object structObj)
    {
        //得到结构体的大小
        int size = Marshal.SizeOf(structObj);
        //创建byte数组
        byte[] bytes = new byte[size];
        //分配结构体大小的内存空间
        IntPtr structPtr = Marshal.AllocHGlobal(size);
        //将结构体拷到分配好的内存空间
        Marshal.StructureToPtr(structObj, structPtr, false);
        //从内存空间拷到byte数组
        Marshal.Copy(structPtr, bytes, 0, size);
        //释放内存空间
        Marshal.FreeHGlobal(structPtr);
        //返回byte数组
        return bytes;
    }

    private object BytesStruct(byte[] bytes, Type type)
    {
        //DataStruct data = new DataStruct();

        int size = Marshal.SizeOf(type);

        if (size > bytes.Length)
        {
            return null;
        }

        IntPtr structPtr = Marshal.AllocHGlobal(size);
        Marshal.Copy(bytes, 0, structPtr, size);
        object obj = Marshal.PtrToStructure(structPtr, type);
        Marshal.FreeHGlobal(structPtr);
        return obj;
    }
    struct Command
    {
        public int UUVNumber;
        public int Find;
        public int Fire;
        public Command(int uuvNumber,int find ,int fire)
        {
            UUVNumber = uuvNumber;
            Find = find;
            Fire = fire;
        }
    }

    #region【18】引用C++dll
    [DllImport("hdds-1.0.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int csharp_init();

    [DllImport("hdds-1.0.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int csharp_uinit();

    [DllImport("hdds-1.0.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int csharp_quit();

    [DllImport("hdds-1.0.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int csharp_init_publisher(string topic);

    [DllImport("hdds-1.0.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int csharp_publish(string topic, string msg);

    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
    public delegate void CallbackDelegate(string param); //声明委托

    [DllImport("hdds-1.0.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int csharp_init_subscriber(string topic, CallbackDelegate call);


    [DllImport("hdds-1.0.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int csharp_subscribe();
   
    static void CallbackQT(string param)
    {
        print($"订阅到QT消息{param}");
        if (param.Length != 11)
        {
            str_VeryImportantJsonData = param;
#if (Debug)
            {
                string path = Application.streamingAssetsPath + "/日志.txt";
                using (StreamWriter writer = new StreamWriter(path, true))
                {
                    writer.WriteLine($"【解析通信节点原始信息{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")}】    {param}");
                }
            }
#endif
        }
        else
        {

        }
    }

    static void CallbackROS(string param)
    {
        //确保订阅到状态节点消息后时，UUV实体和dic已经生成
        while ((uuvState.Count != Uuv3DModels.Count) || (uuvState.Count == 0))
        {
            Task.Delay(50);
        }
        
        //while (Uuv3DModels.Count == 0)
        //{
        //    Task.Delay(0);
        //}

        print($"订阅到ROS消息{param}");

        string strReceiveData = param;

        JObject json = (JObject)JsonConvert.DeserializeObject(strReceiveData);
        JArray arr = (JArray)json["uuvControl"];
        for (int i = 0; i < arr.Count; i++)
        {
            JObject jou = (JObject)arr[i];

            int num = (int)jou["uuv_id"];
            float u = (float)jou["speed_desired"];
            float q = (float)jou["v_pitch_desired"];
            float r = (float)jou["v_yaw_desired"];
            float find = (float)jou["searching_flag"];
            float fire = (float)jou["attacking_flag"];

            if (!dic.ContainsKey(i))//键不存在时忽略本次消息
            {
                return ;
            }
            float w_limted = (u / dic[i].Item1) * 57.29578f;

            if (q > w_limted)
            {
                q = w_limted;
            }
            if (q < -w_limted)
            {
                q = -w_limted;
            }
            if (r > w_limted)
            {
                r = w_limted;
            }
            if (r < -w_limted)
            {
                r = -w_limted;
            }
            dic[i] = (dic[i].Item1, u, q, r, dic[i].Item5, find, fire);

            if ((dic[i].Item6 == 1) || (dic[i].Item6 == 2) || (dic[i].Item7 == 1))
            {
                queueCommand.Enqueue(new Command(i, (int)dic[i].Item6, (int)dic[i].Item7));
            }

        }
    }
    #endregion
}


using System;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using UnityEngine;

public class UUVLocation : MonoBehaviour
{
    //qinkang新框架
    CancellationTokenSource cts1 = new CancellationTokenSource();//发布json使用
    CancellationTokenSource cts2 = new CancellationTokenSource();//订阅json使用
    

    public Transform uuv;
    public Transform Plane;

    private void Start()
    {
       

    }

    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.A))
        {
            
        }
    }
}
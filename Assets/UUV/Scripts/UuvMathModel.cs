using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using Unity.VisualScripting;
using UnityEngine;

namespace Assets
{
    internal class UuvMathModel
    {
        //将1°转换为弧度
        const float C = (float)Math.PI/180;

        private float _x;
        private float _y;
        private float _z;

        private float _pitch;
        private float _roll;
        private float _yaw;


        public UuvMathModel()
        {
                
        }
        public UuvMathModel(Vector3 Position, Vector3 Attitude )
        {
            this._x = Position.x;
            this._y = Position.y;
            this._z = Position.z;

            //this._pitch = Attitude.x;
            //this._yaw = Attitude.y;
            //this._roll = Attitude.z;

            this._roll = Attitude.x;
            this._pitch = Attitude.y;
            this._yaw = Attitude.z;
        }
      
        //UUV属性:位置、姿态
        public float X
        {
            get { return _x; }
            set { _x = value; }
        }
        public float Y
        {
            get { return _y; }
            set { _y = value; }
        }
        public float Z
        {
            get { return _z; }
            set { _z = value; }
        }

        public float Pitch
        {
            get { return _pitch; }
            set { _pitch = value; }
        }
        public float Roll
        {
            get { return _roll; }
            set { _roll = value; }
        }
        public float Yaw
        {
            get { return _yaw; }
            set { _yaw = value; }
        }



        //角度下计算
        private float X_Rate(float u, float yaw)
        {
            return (float)(u * Math.Cos(yaw * C));
        }

        private float Y_Rate(float u, float yaw)
        {
            return (float)(u * Math.Sin(yaw * C));
        }

        private float Z_Rate(float u, float pitch)
        {
            return (float)(-u * Math.Sin(pitch * C));
        }

        ////弧度下计算
        //private float X_Rate(float u, float yaw)
        //{
        //    return (float)(u * Math.Cos(yaw));
        //}

        //private float Y_Rate(float u, float yaw)
        //{
        //    return (float)(u * Math.Sin(yaw));
        //}

        //private float Z_Rate(float u, float pitch)
        //{
        //    return (float)(-u * Math.Sin(pitch));
        //}

        //DeltaTime应该是一个变量,X, Y, Z, Pitch, Roll, Yaw是惯性系下状态数据，u, q, r为载体坐标系下状态数据
        public float[] CalculateNextState( float u, float q, float r)
        {
            float deltaTime = 0.01f;
            X += X_Rate(u, Yaw) * deltaTime;
            Y += Y_Rate(u, Yaw) * deltaTime;
            Z += Z_Rate(u, Pitch) * deltaTime;

            Pitch += q * deltaTime;
            Roll = 0;
            Yaw += r * deltaTime;

            if (Pitch <-30)
            {
                Pitch = -30;
            }
            if (Pitch > 30)
            {
                Pitch = 30;
            }
            if (Yaw< 0)
            {
                Yaw +=360;
            }
            if (Yaw > 360)
            {
                Yaw -= 360;
            }
            
            return new float[] {X, Y, Z, Pitch, Roll, Yaw, u, q, r};
        }

    }
}

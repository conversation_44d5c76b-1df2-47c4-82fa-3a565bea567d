using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class DestroyWheelTrail : MonoBehaviour
{
    public float time = 3;
    private IEnumerator Start()
    {
        yield return new WaitForSeconds(time);
        Material mat = GetComponent<MeshRenderer>().material;
        while (mat.GetColor("_MainColor").a > 0)
        {
            mat.SetColor("_MainColor", (mat.GetColor("_MainColor") - new Color(0, 0, 0, Time.deltaTime)));
            yield return null;
        }
        Destroy(gameObject);
    }

    // Update is called once per frame
    void Update()
    {
        
    }


}

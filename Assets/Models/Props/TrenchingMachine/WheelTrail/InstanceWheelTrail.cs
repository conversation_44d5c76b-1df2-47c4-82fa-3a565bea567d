using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class InstanceWheelTrail : MonoBehaviour
{
    Transform wheelTrailParent;
    public GameObject trailPrefab;
    public float trailStep;
    Transform leftWheel;
    Transform rightWheel;
    public bool startTrail = false;

    void Start()
    {
        leftWheel = transform.GetChild(0);
        rightWheel = transform.GetChild(1);
        if (startTrail)
        {
            StartTrail();
        }

        GameObject emptyObject = new GameObject("WheelTrailParent");
        emptyObject.transform.position = Vector3.zero;
        wheelTrailParent = emptyObject.transform;
    }


    public void StartTrail()
    {
        StartCoroutine("InstanceLeftTrail");
        StartCoroutine("InstanceRightTrail");
    }

    public void StopTrail()
    {
        StopCoroutine("InstanceLeftTrail");
        StopCoroutine("InstanceRightTrail");
    }

    IEnumerator InstanceLeftTrail()
    {
        Vector3 pos = leftWheel.position;
        while (true)
        {
            while ((leftWheel.position - pos).magnitude >= trailStep)
            {
                pos = (leftWheel.position - pos).normalized * trailStep + pos;
                Instantiate(trailPrefab, pos, leftWheel.rotation, wheelTrailParent);
                //pos = leftWheel.position;
            }
            yield return null;
        }
    }

    IEnumerator InstanceRightTrail()
    {
        Vector3 pos = rightWheel.position;
        while (true)
        {
            while ((rightWheel.position - pos).magnitude >= trailStep)
            {
                pos = (rightWheel.position - pos).normalized * trailStep + pos;
                Instantiate(trailPrefab, pos, rightWheel.rotation, wheelTrailParent);
                //pos = rightWheel.position;
            }
            yield return null;
        }
    }
}

using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using static UnityEngine.GraphicsBuffer;
using static UnityEngine.Rendering.DebugUI;

public class TrenchingMachineAnimator : MonoBehaviour
{
    [Range(0f, 1f)]
    public float frontAni;
    [Range(0f, 1f)]
    public float backAni;
    public bool wheelAni;
    public bool propellerAni;
    public Transform wheelGroup;
    public Transform propellerGroup;
    public Transform trackGroup;

    public List<Color> lightColors;
    public LightColor[] lights;

    public PTZ[] ptzs;

    public bool startDig = false;
    public bool isSand = true;
    public bool instanceRock = true;
    public float effectSpeed = 5;

    public static TrenchingMachineAnimator Instance;

    Animator animator;

    [HideInInspector] public bool isPowerSupplySwitch;//电源开关

    [Serializable]
    public struct LightColor
    {
        public SkinnedMeshRenderer light;
        public Color color;
        [Range(0f, 10f)]
        public float colorIntensity;
    }

    [Serializable]
    public struct PTZ
    {
        public Transform PTZBoneTransform;
        [Range(0f, 360f)]
        public float PTZRotation;
        public bool reverseChildRotate;
        [Range(0f, 90f)]
        public float PTZChildRotation;
        [NonSerialized]
        public Quaternion PTZBoneRotation;
        [NonSerialized]
        public Vector3 PTZBoneRotationEuler;
    }

    private void Awake()
    {
        Instance = this;
        animator = GetComponent<Animator>();
    }

    void Start()
    {
        isPowerSupplySwitch = true;
        StartCoroutine(WheelAnimation());
        StartCoroutine(PropellerAnimation());
        for (int i = 0; i < ptzs.Length; i++)
        {
            ptzs[i].PTZBoneRotation = ptzs[i].PTZBoneTransform.localRotation;
            ptzs[i].PTZBoneRotationEuler = ptzs[i].PTZBoneRotation.eulerAngles;
        }
    }

    Quaternion PTZChildBoneRotation;
    Vector3 PTZChildBoneRotationEuler;
    float factor;
    Color color;

    void Update()
    {
        if (!isPowerSupplySwitch) return;

        animator.SetFloat("Front", frontAni);
        animator.SetFloat("Back", backAni);

        for (int i = 0; i < ptzs.Length; i++)
        {
            ptzs[i].PTZBoneRotation.eulerAngles = ptzs[i].PTZBoneRotationEuler + Vector3.up * ptzs[i].PTZRotation;
            ptzs[i].PTZBoneTransform.localRotation = ptzs[i].PTZBoneRotation;
            PTZChildBoneRotation = Quaternion.identity;
            if (ptzs[i].reverseChildRotate)
            {
                PTZChildBoneRotationEuler = -Vector3.forward * ptzs[i].PTZChildRotation;
            }
            else
            {
                PTZChildBoneRotationEuler = Vector3.forward * ptzs[i].PTZChildRotation;
            }
            PTZChildBoneRotation.eulerAngles = PTZChildBoneRotationEuler;
            ptzs[i].PTZBoneTransform.GetChild(0).localRotation = PTZChildBoneRotation;
        }
    }

    /// <summary>
    /// 设置灯开关
    /// </summary>
    /// <param name="index"></param>
    public void SetLightSwitch(int index, bool ison)
    {
        factor = Mathf.Pow(2, ison ? 2 : 0);
        lights[index].color = lightColors[ison ? 1 : 0];
        color = new Color(lights[index].color.r * factor, lights[index].color.g * factor, lights[index].color.b * factor, 1);
        lights[index].light.material.SetColor("_EmissionColor", color);

        /*foreach (LightColor lc in lights)
        {
            factor = Mathf.Pow(2, lc.colorIntensity);
            color = new Color(lc.color.r * factor, lc.color.g * factor, lc.color.b * factor, 1);
            lc.light.material.SetColor("_EmissionColor", color);
        }*/
    }

    /// <summary>
    /// 电源开关
    /// </summary>
    public void PowerSupplySwitch(bool ison)
    {
        isPowerSupplySwitch = ison;
        startDig = isPowerSupplySwitch;
        for (int i = 0; i < lights.Length; i++)
        {
            SetLightSwitch(i, ison);
        }
    }

    /// <summary>
    /// 挖掘头状态
    /// </summary>
    public void DiggerHeadState(bool ison)
    {
        DiggerHeadAin(ison);
    }

    float frontAni_temp;
    float time_temp;
    async Task DiggerHeadAin(bool ison)
    {
        frontAni_temp = frontAni;
        float target = ison ? 1 : 0;
        while (frontAni_temp != target)
        {
            time_temp += Time.deltaTime;
            frontAni = Mathf.Lerp(frontAni_temp, target, time_temp);
            await Task.Yield();
        }
    }

    List<Transform> wheelbones = new List<Transform>();
    Vector3 rotVec;
    Quaternion rot;
    Vector2 offset;
    SkinnedMeshRenderer[] tracks;
    IEnumerator WheelAnimation()
    {

        for (int i = 0; i < wheelGroup.childCount; i++)
        {
            wheelbones.Add(wheelGroup.GetChild(i));
        }
        rotVec = Vector3.zero;
        rot = Quaternion.identity;

        offset = Vector2.zero;
        tracks = trackGroup.GetComponentsInChildren<SkinnedMeshRenderer>();

        while (true)
        {
            if (wheelAni && isPowerSupplySwitch)
            {
                rotVec += Vector3.left * 360 * Time.deltaTime;
                if (rotVec.x > 360)
                {
                    rotVec -= Vector3.left * 360;
                }
                rot.eulerAngles = rotVec;
                foreach (Transform t in wheelbones)
                {
                    t.localRotation = rot;
                }

                offset += Vector2.up * 0.2f * Time.deltaTime;
                if (offset.y > 1)
                {
                    offset -= Vector2.up;
                }
                foreach (SkinnedMeshRenderer track in tracks)
                {
                    track.sharedMaterial.SetTextureOffset("_BaseMap", offset);
                }
            }
            yield return null;
        }
    }
    List<Transform> propellerbones = new List<Transform>();
    IEnumerator PropellerAnimation()
    {
        for (int i = 0; i < propellerGroup.childCount; i++)
        {
            propellerbones.Add(propellerGroup.GetChild(i).GetChild(0));
        }
        Vector3 rotVec = Vector3.zero;
        Quaternion rot = Quaternion.identity;
        while (true)
        {
            if (propellerAni && isPowerSupplySwitch)
            {
                rotVec += Vector3.up * 720 * Time.deltaTime;
                if (rotVec.y > 360)
                {
                    rotVec -= Vector3.up * 360;
                }
                rot.eulerAngles = rotVec;
                foreach (Transform t in propellerbones)
                {
                    t.localRotation = rot;
                }
            }
            yield return null;
        }
    }
}
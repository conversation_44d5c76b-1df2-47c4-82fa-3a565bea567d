using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CrawlerCarControl : MonoBehaviour
{
    [Range(0f, 1f)]
    public float frontAni;
    [Range(0f, 1f)]
    public float backAni;
    Animator animator;
  
    // Start is called before the first frame update
    void Start()
    {
        animator = GetComponent<Animator>();
    }

    // Update is called once per frame
    void Update()
    {
        animator.SetFloat("Front", frontAni);
        animator.SetFloat("Back", backAni);
    }
}

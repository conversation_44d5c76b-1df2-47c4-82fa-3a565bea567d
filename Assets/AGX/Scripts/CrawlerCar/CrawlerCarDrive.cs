using System;
using System.Collections;
using System.Collections.Generic;
using AGXUnity;
using AGXUnity.Model;
using UnityEngine;

public class CrawlerCarDrive : MonoBehaviour
{
    private readonly float m_maxTrackSprocketSpeed = 3.0f;
    public Constraint LeftHinge;

    public Constraint RightHinge;
    TrenchingMachine trenchingMachine; 
    private void Awake()
    {
        trenchingMachine = GetComponent<TrenchingMachine>();
    }

    private float Get1DAxis(KeyCode negative, KeyCode positive)
    {
        int value = 0;
        if (Input.GetKey(negative))
        {
            value -= 1;
        }
        if(Input.GetKey(positive))
        {
            value += 1;
        }
        return value;
    }

    void Update()
    {
        if (trenchingMachine == null || trenchingMachine.machineControl == MachineControl.CrawlerCar)
        {
            onDrive();
        }
    }

    private void onDrive()
    {
        float ForwardBack = Get1DAxis(KeyCode.DownArrow, KeyCode.UpArrow);
        float LeftRight = Get1DAxis(KeyCode.LeftArrow, KeyCode.RightArrow);
        if (LeftRight != 0 && ForwardBack != 0)
        {
            //speedRatio越大转动越快
            float speedRatio = 2.0f; 
            float baseSpeed = 1.0f / speedRatio; 
            if (LeftRight < 0) 
            {
                //左前进
                SetRightSprocket(ForwardBack * m_maxTrackSprocketSpeed);
                SetLeftSprocket(ForwardBack * m_maxTrackSprocketSpeed * baseSpeed);
            }
            else
            {
                //右前进
                SetRightSprocket(ForwardBack * m_maxTrackSprocketSpeed * baseSpeed);
                SetLeftSprocket(ForwardBack * m_maxTrackSprocketSpeed);
            }
        }
        else
        {
            if (LeftRight != 0)
            {
                //原地左右旋转
                SetRightSprocket(-LeftRight * m_maxTrackSprocketSpeed);
                SetLeftSprocket(LeftRight * m_maxTrackSprocketSpeed);
            }
            else if (ForwardBack != 0)
            {
                //前进后退
                SetRightSprocket(ForwardBack * m_maxTrackSprocketSpeed);
                SetLeftSprocket(ForwardBack * m_maxTrackSprocketSpeed);
            }
            else
            {
                //左前进 右前进 其中一个轮子不动
                // float SteerRight = Input.GetAxis("SteerRight");
                // float SteerLeft = Input.GetAxis("SteerLeft");
                float SteerRight = Get1DAxis(KeyCode.End, KeyCode.Home);
                float SteerLeft = Get1DAxis(KeyCode.PageDown, KeyCode.PageUp);
                SetRightSprocket(SteerRight * m_maxTrackSprocketSpeed);
                SetLeftSprocket(SteerLeft * m_maxTrackSprocketSpeed);
            }
        }
    }
    
    private void SetLeftSprocket(float value)
    {
        SetSpeed(LeftHinge, value);
    }

    private void SetRightSprocket(float value)
    {
        SetSpeed(RightHinge, value);
    }

    private void SetSpeed(Constraint constraint, float speed)
    {
        var motorEnable = !AGXUnity.Utils.Math.EqualsZero(speed);
        var mc = constraint.GetController<TargetSpeedController>();
        var lc = constraint.GetController<LockController>();
        mc.Enable = true;
        lc.Enable = false;
        mc.Speed = speed;
        if (!motorEnable)
        {
            lc.Enable = true;
            lc.Position = constraint.GetCurrentAngle();
        }
    }
}

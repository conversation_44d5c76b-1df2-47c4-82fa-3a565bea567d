
using System;
using AGXUnity;
using AGXUnity.Collide;
using Unity.VisualScripting;
using UnityEngine;

public class LogAgxContact :MonoBehaviour
{
    Shape shape;
    private void Awake()
    {
        shape = GetComponent<Shape>();
        Simulation.Instance.GetInitialized<Simulation>().ContactCallbacks.OnContact(OnContactShape, shape);
    }

    private ScriptComponent target;
    private bool OnContactShape(ref ContactData contactdata)
    {
        ScriptComponent _target = contactdata.Component1 == shape ? contactdata.Component2 : contactdata.Component1;
        if(_target != target)
        {
            target = _target;
            Debug.Log($"Agx Contact {gameObject.name} >>>> {target.name}" );
        }
        return false;
    }
}
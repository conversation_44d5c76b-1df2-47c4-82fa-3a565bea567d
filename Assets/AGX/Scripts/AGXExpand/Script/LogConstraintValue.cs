
using System;
using AGXUnity;
using AGXUnity.Collide;
using Unity.VisualScripting;
using UnityEngine;

public class LogConstraintValue :MonoBehaviour
{
    Constraint constraint;
    private void Start()
    {
        constraint = GetComponent<Constraint>();
        Simulation.Instance.GetInitialized<Simulation>().StepCallbacks.SimulationLast += OnSimulationLast;
    }

    private void OnSimulationLast()
    {
        Debug.Log($"{gameObject.name} constraint value: " + constraint.GetCurrentAngle());
    }
    
}
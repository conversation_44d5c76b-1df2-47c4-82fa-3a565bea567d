using UnityEngine;
using AGXUnity;

public class AgxRightbodyFollowTarget : MonoBehaviour
{
    public GameObject target;
    private RigidBody agxRigidBody;

    void Start()
    {
        agxRigidBody = GetComponent<RigidBody>();
        if (agxRigidBody == null)
        {
            Debug.LogError("未找到 AGX Rigid Body 组件！");
        }
        Simulation.Instance.StepCallbacks.PreStepForward += onPreStepForward;
    }

    void onPreStepForward()
    {
        if (target != null && agxRigidBody != null)
        {
            transform.position = target.transform.position;
            transform.rotation = target.transform.rotation;
            agxRigidBody.SyncNativeTransform();
            // 如果需要同步旋转，可以添加：
        }
    }
}
#if UNITY_EDITOR
using UnityEditor;
#endif
using System;
using System.Linq;
using System.Threading.Tasks;
using AGXUnity;
using AGXUnity.Collide;
using AGXUnity.Model;
using Unity.VisualScripting;
using UnityEngine;

public static class AGXUtil
{
    /// <summary>
    /// AGX下的Vec3转换成U3D下Vector3，一个是左手坐标系，一个是右手坐标系
    /// </summary>
    /// <param name="v"></param>
    /// <returns></returns>
    public static Vector3 Vec3ToVector3(this agx.Vec3 v)
    {
        return new Vector3(-(float)v.x, (float)v.y, (float)v.z);
    }
    
    /// <summary>
    /// U3D下Vector3转换成AGX下的Vec3，一个是左手坐标系，一个是右手坐标系
    /// </summary>
    /// <param name="v"></param>
    /// <returns></returns>
    public static agx.Vec3 Vector3ToVec3(this Vector3 v)
    {
        return new agx.Vec3(-v.x, v.y, v.z);
    }
    
    
    static readonly float arrowScale = 1.5f;
    static readonly float arrowAngle = 30f;
    static readonly Color textColor = Color.cyan;
    static readonly Vector3 labelOffset = new Vector3(0, 0f, 0); 
    /// <summary>
    /// 绘制箭头
    /// </summary>
    /// <param name="position"></param>
    /// <param name="direction"></param>
    /// <param name="color"></param>
    /// <param name="label"></param>
    public static void DrawArrow(Vector3 position, Vector3 direction, Color color, string label)
    {
        if (direction == Vector3.zero) return;
        Gizmos.color = color;
        // 计算基础参数
        float headLength = arrowScale * 0.5f;
        float headAngle = arrowAngle;
        // 绘制主体线段
        Vector3 endPoint = position + direction * arrowScale;
        Gizmos.DrawLine(position, endPoint);
        // 计算箭头方向
        Vector3 right = Quaternion.LookRotation(direction) * Quaternion.Euler(0, 180 + headAngle, 0) * Vector3.forward;
        Vector3 left = Quaternion.LookRotation(direction) * Quaternion.Euler(0, 180 - headAngle, 0) * Vector3.forward;
        // 绘制箭头头部
        Gizmos.DrawLine(endPoint, endPoint + right * headLength);
        Gizmos.DrawLine(endPoint, endPoint + left * headLength);
#if UNITY_EDITOR
        // 设置文字样式
        GUIStyle style = new GUIStyle();
        style.normal.textColor = textColor;
        style.fontSize = 12;
        style.alignment = TextAnchor.MiddleCenter;
        style.contentOffset = new Vector2(0, -15); // 垂直偏移
        // 绘制带背景的文字
        Handles.Label(position + labelOffset, 
            label, 
            style);
#endif
    }

    //设置俩个Shape是否碰撞
    public static void SetAGXCollisionsEnable(Shape shape1, Shape shape2, bool enable,string shape1Tag = "",string shape2Tag = "")
    {
        var collisionGroupsManager =CollisionGroupsManager.Instance.GetInitialized<CollisionGroupsManager>();
        int instanceId1 = shape1.GetInstanceID();
        int instanceId2 = shape2.GetInstanceID();
        int insId = instanceId1 ^ instanceId2;
        if (string.IsNullOrEmpty(shape1Tag))
            shape1Tag = $"Script_{insId}_{shape1.name}";
        if(string.IsNullOrEmpty(shape2Tag))
            shape2Tag = $"Script_{insId}_{shape2.name}";
        var collisionGroups1 =TryAddComponent<CollisionGroups>(shape1.transform);
        collisionGroups1.AddGroup(shape1Tag,false, true);
        var collisionGroups2 =TryAddComponent<CollisionGroups>(shape2.transform);
        collisionGroups2.AddGroup(shape2Tag,false, true);
        collisionGroupsManager.SetEnablePair(shape2Tag, shape1Tag, enable);
    }
    
    
    public static void SetAGXCollisionsEnable(Shape shape1, Cable cable, bool enable,string shape1Tag = "",string shape2Tag = "")
    {
        var collisionGroupsManager =CollisionGroupsManager.Instance.GetInitialized<CollisionGroupsManager>();
        int instanceId1 = shape1.GetInstanceID();
        int instanceId2 = cable.GetInstanceID();
        int insId = instanceId1 ^ instanceId2;
        if (string.IsNullOrEmpty(shape1Tag))
            shape1Tag = $"Script_{insId}_{shape1.name}";
        if(string.IsNullOrEmpty(shape2Tag))
            shape2Tag = $"Script_{insId}_{cable.name}";
        var collisionGroups1 =TryAddComponent<CollisionGroups>(shape1.transform);
        collisionGroups1.AddGroup(shape1Tag,false, true);
        var collisionGroups2 =TryAddComponent<CollisionGroups>(cable.transform);
        collisionGroups2.AddGroup(shape2Tag,false, true);
        collisionGroupsManager.SetEnablePair(shape2Tag, shape1Tag, enable);
    }
    
    public static void SetAGXCollisionsEnable(Shape shape1, DeformableTerrainBase terrain, bool enable,string shape1Tag = "",string shape2Tag = "")
    {
        var collisionGroupsManager =CollisionGroupsManager.Instance.GetInitialized<CollisionGroupsManager>();
        int instanceId1 = shape1.GetInstanceID();
        int instanceId2 = terrain.GetInstanceID();
        int insId = instanceId1 ^ instanceId2;
        if (string.IsNullOrEmpty(shape1Tag))
            shape1Tag = $"Script_{insId}_{shape1.name}";
        if(string.IsNullOrEmpty(shape2Tag))
            shape2Tag = $"Script_{insId}_{terrain.name}";
        var collisionGroups1 =TryAddComponent<CollisionGroups>(shape1.transform);
        collisionGroups1.AddGroup(shape1Tag,false, true);
        var collisionGroups2 =TryAddComponent<CollisionGroups>(terrain.transform);
        collisionGroups2.AddGroup(shape2Tag,false, true);
        collisionGroupsManager.SetEnablePair(shape2Tag, shape1Tag, enable);
    }
    
    public static void SetAGXCollisionsEnable(RigidBody righBody, DeformableTerrainBase terrain, bool enable,string shape1Tag = "",string shape2Tag = "")
    {
        var collisionGroupsManager =CollisionGroupsManager.Instance.GetInitialized<CollisionGroupsManager>();
        int instanceId1 = righBody.GetInstanceID();
        int instanceId2 = terrain.GetInstanceID();
        int insId = instanceId1 ^ instanceId2;
        if (string.IsNullOrEmpty(shape1Tag))
            shape1Tag = $"Script_{insId}_{righBody.name}";
        if(string.IsNullOrEmpty(shape2Tag))
            shape2Tag = $"Script_{insId}_{terrain.name}";
        var collisionGroups1 =TryAddComponent<CollisionGroups>(righBody.transform);
        collisionGroups1.AddGroup(shape1Tag,false, true);
        var collisionGroups2 =TryAddComponent<CollisionGroups>(terrain.transform);
        collisionGroups2.AddGroup(shape2Tag,false, true);
        collisionGroupsManager.SetEnablePair(shape2Tag, shape1Tag, enable);
    }
    
    //设置俩个刚体是否碰撞
    public static void SetAGXCollisionsEnable(RigidBody rb1, RigidBody rb2, bool enable,string shape1Tag = "",string shape2Tag = "")
    {
        var collisionGroupsManager = CollisionGroupsManager.Instance.GetInitialized<CollisionGroupsManager>();
        int instanceId1 = rb1.GetInstanceID();
        int instanceId2 = rb2.GetInstanceID();
        int insId = instanceId1 ^ instanceId2;
        if (string.IsNullOrEmpty(shape1Tag))
            shape1Tag = $"Script_{insId}_{rb1.name}";
        if(string.IsNullOrEmpty(shape2Tag))
            shape2Tag = $"Script_{insId}_{rb2.name}";
        var collisionGroups1 =TryAddComponent<CollisionGroups>(rb1.transform);
        collisionGroups1.AddGroup(shape1Tag,true, true);
        var collisionGroups2 =TryAddComponent<CollisionGroups>(rb2.transform);
        collisionGroups2.AddGroup(shape2Tag,true, true);
        collisionGroupsManager.SetEnablePair(shape2Tag, shape1Tag, enable);
    }
    
    public static void SetAGXCollisionsEnable(RigidBody rb1, Track track, bool enable,string shape1Tag = "",string shape2Tag = "")
    {
        var collisionGroupsManager = CollisionGroupsManager.Instance.GetInitialized<CollisionGroupsManager>();
        int instanceId1 = rb1.GetInstanceID();
        int instanceId2 = track.GetInstanceID();
        int insId = instanceId1 ^ instanceId2;
        if (string.IsNullOrEmpty(shape1Tag))
            shape1Tag = $"Script_{insId}_{rb1.name}";
        if(string.IsNullOrEmpty(shape2Tag))
            shape2Tag = $"Script_{insId}_{track.name}";
        var collisionGroups1 =TryAddComponent<CollisionGroups>(rb1.transform);
        collisionGroups1.AddGroup(shape1Tag,true, true);
        var collisionGroups2 =TryAddComponent<CollisionGroups>(track.transform);
        collisionGroups2.AddGroup(shape2Tag,true, true);
        collisionGroupsManager.SetEnablePair(shape2Tag, shape1Tag, enable);
    }

    public static void SetAGXCollisionsEnable(RigidBody rb1, Cable cable, bool enable,string shape1Tag = "",string shape2Tag = "")
    {
        var collisionGroupsManager = CollisionGroupsManager.Instance.GetInitialized<CollisionGroupsManager>();
        int instanceId1 = rb1.GetInstanceID();
        int instanceId2 = cable.GetInstanceID();
        int insId = instanceId1 ^ instanceId2;
        if (string.IsNullOrEmpty(shape1Tag))
            shape1Tag = $"Script_{insId}_{rb1.name}";
        if(string.IsNullOrEmpty(shape2Tag))
            shape2Tag = $"Script_{insId}_{cable.name}";
        var collisionGroups1 =TryAddComponent<CollisionGroups>(rb1.transform);
        collisionGroups1.AddGroup(shape1Tag,true, true);
        var collisionGroups2 =TryAddComponent<CollisionGroups>(cable.transform);
        collisionGroups2.AddGroup(shape2Tag,true, true);
        collisionGroupsManager.SetEnablePair(shape2Tag, shape1Tag, enable);
    }
    
    public static void SetAGXCollisionsEnable(RigidBody rb1, Wire wire, bool enable,string shape1Tag = "",string shape2Tag = "")
    {
        var collisionGroupsManager = CollisionGroupsManager.Instance.GetInitialized<CollisionGroupsManager>();
        int instanceId1 = rb1.GetInstanceID();
        int instanceId2 = wire.GetInstanceID();
        int insId = instanceId1 ^ instanceId2;
        if (string.IsNullOrEmpty(shape1Tag))
            shape1Tag = $"Script_{insId}_{rb1.name}";
        if(string.IsNullOrEmpty(shape2Tag))
            shape2Tag = $"Script_{insId}_{wire.name}";
        var collisionGroups1 =TryAddComponent<CollisionGroups>(rb1.transform);
        collisionGroups1.AddGroup(shape1Tag,true, true);
        var collisionGroups2 =TryAddComponent<CollisionGroups>(wire.transform);
        collisionGroups2.AddGroup(shape2Tag,true, true);
        collisionGroupsManager.SetEnablePair(shape2Tag, shape1Tag, enable);
    }
    
    public static void SetAGXCollisionsEnable( Shape shape, RigidBody rb, bool enable,string shape1Tag = "",string shape2Tag = "")
    {
        SetAGXCollisionsEnable(rb, shape, enable,shape1Tag, shape2Tag);
    }

    public static void SetAGXCollisionsEnable(RigidBody rb, Shape shape, bool enable,string shape1Tag = "",string shape2Tag = "")
    {
        var collisionGroupsManager = CollisionGroupsManager.Instance.GetInitialized<CollisionGroupsManager>();
        int instanceId1 = rb.GetInstanceID();
        int instanceId2 = shape.GetInstanceID();
        int insId = instanceId1 ^ instanceId2;
        if(string.IsNullOrEmpty(shape1Tag))
            shape1Tag = $"Script_{insId}_{rb.name}";
        if(string.IsNullOrEmpty(shape2Tag))
            shape2Tag = $"Script_{insId}_{shape.name}";
        var collisionGroups1 =TryAddComponent<CollisionGroups>(rb.transform);
        collisionGroups1.AddGroup(shape1Tag,true, true);
        var collisionGroups2 =TryAddComponent<CollisionGroups>(shape.transform);
        collisionGroups2.AddGroup(shape2Tag,false, true);
        collisionGroupsManager.SetEnablePair(shape2Tag, shape1Tag, enable);
    }

    public static void SetAGXCollosionIgnoreAll(RigidBody rb, params CollisionsShapeType[] collisionsShapeTypes)
    {
        int instanceId1 = rb.GetInstanceID();
        string shape1Tag = $"Script_{instanceId1}_{rb.name}";
        string shape2Tag = $"Script_{instanceId1}_{rb.name}_IgnoreAll";

        foreach (var shape in UnityEngine.Object.FindObjectsOfType<Shape>())
        {
            AGXUtil.SetAGXCollisionsEnable(rb, shape, false,shape1Tag,shape2Tag);
        }

        if (!collisionsShapeTypes.Contains(CollisionsShapeType.Terrain))
        {
            foreach (var terrain in UnityEngine.Object.FindObjectsOfType<DeformableTerrainBase>())
            {
                AGXUtil.SetAGXCollisionsEnable(rb, terrain, false,shape1Tag,shape2Tag);
            }
        }

        if (!collisionsShapeTypes.Contains(CollisionsShapeType.Track))
        {
            foreach (var track in UnityEngine.Object.FindObjectsOfType<Track>())
            {
                AGXUtil.SetAGXCollisionsEnable(rb, track, false,shape1Tag,shape2Tag);
            }
        }

        if (!collisionsShapeTypes.Contains(CollisionsShapeType.Cable))
        {
            foreach (var cable in UnityEngine.Object.FindObjectsOfType<Cable>())
            {
                AGXUtil.SetAGXCollisionsEnable(rb, cable, false,shape1Tag,shape2Tag);
            }
        }

        if (!collisionsShapeTypes.Contains(CollisionsShapeType.Wire))
        {
            foreach (var wire in UnityEngine.Object.FindObjectsOfType<Wire>())
            {
                AGXUtil.SetAGXCollisionsEnable(rb, wire, false,shape1Tag,shape2Tag);
            }
        }
    }

    public static void SetAGXCollisionsEnableByShape(RigidBody rb, Shape shape, bool enable,string shape1Tag = "",string shape2Tag = "", params Shape[] IgnoreShape)
    {
        var collisionGroupsManager = CollisionGroupsManager.Instance.GetInitialized<CollisionGroupsManager>();
        int instanceId1 = rb.GetInstanceID();
        int instanceId2 = shape.GetInstanceID();
        int insId = instanceId1 ^ instanceId2;
        if(string.IsNullOrEmpty(shape1Tag))
            shape1Tag = $"Script_{insId}_{rb.name}";
        if(string.IsNullOrEmpty(shape2Tag))
            shape2Tag = $"Script_{insId}_{shape.name}";
        foreach (var rbShape in rb.Shapes)
        {
            if (IgnoreShape.Contains(rbShape))
                continue;
            var collisionGroups1 = TryAddComponent<CollisionGroups>(rbShape.transform);
            collisionGroups1.AddGroup(shape1Tag,false, true);
        }
        var collisionGroups2 =TryAddComponent<CollisionGroups>(shape.transform);
        collisionGroups2.AddGroup(shape2Tag,false, true);
        collisionGroupsManager.SetEnablePair(shape2Tag, shape1Tag, enable);
    }

    private static T TryAddComponent<T>(Transform transform) where T : Component
    {
        T component = transform.transform.GetComponent<T>();
        if(component == null)
        {
            component = transform.transform.AddComponent<T>();
        }
        return component;
    }

}

public enum CollisionsShapeType
{
    Terrain,
    Cable,
    Wire,
    Track
}
    
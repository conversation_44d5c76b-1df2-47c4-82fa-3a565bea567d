using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(ContactMaterialDetail))] // 替换为你的组件类型
public class ContactMaterialDetailEditor : Editor
{
    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        
        SerializedProperty contactMatProp = serializedObject.FindProperty("ContactMaterial");
        SerializedProperty frictionModelProp = serializedObject.FindProperty("FrictionModel");

        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.PropertyField(contactMatProp, new GUIContent("Contact Material"));
        EditorGUILayout.PropertyField(frictionModelProp, new GUIContent("Friction Model"));
        EditorGUILayout.EndVertical();

        serializedObject.ApplyModifiedProperties();
    }
}

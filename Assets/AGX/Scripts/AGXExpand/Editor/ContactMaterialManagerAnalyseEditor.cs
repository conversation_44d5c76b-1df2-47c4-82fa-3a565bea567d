using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using agxCollide;
using AGXUnity;
using NUnit.Framework;
using Unity.VisualScripting;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;
using AGXUnity.Collide;
using AGXUnity.Model;
using Shape = AGXUnity.Collide.Shape;

[CustomEditor(typeof(ContactMaterialManagerAnalyse))]
public class ContactMaterialManagerAnalyseEditor : Editor
{
    private SerializedProperty contactMaterialDetailsProp;
    private ReorderableList reorderableList;
    private ContactMaterialManager contactMaterialManager; 
    private void OnEnable()
    {
        contactMaterialDetailsProp = serializedObject.FindProperty("ContactMaterialDetails");
        contactMaterialManager = target.GetComponent(typeof(ContactMaterialManager)) as ContactMaterialManager;
    }

    public override void OnInspectorGUI()
    {
        if(target == null)
            return;
        // 获取目标组件
        ContactMaterialManagerAnalyse analyse = (ContactMaterialManagerAnalyse)target;
        serializedObject.Update();
        // 绘制 ObjectField
        analyse.CheckMaterial = (ShapeMaterial)EditorGUILayout.ObjectField(
            "Check Material",          // 字段标签
            analyse.CheckMaterial,      // 当前值
            typeof(ShapeMaterial),     // 允许的对象类型
            false                      // 是否允许场景对象（仅资源）
        );

        if (analyse.ContactMaterialDetails != null && analyse.ContactMaterialDetails.Count > 0)
        {
            EditorGUILayout.EndFoldoutHeaderGroup();
            for (int i = 0; i < analyse.ContactMaterialDetails.Count; i++)
            {
                analyse.ContactMaterialDetails[i].IsFoldOut = EditorGUILayout.BeginFoldoutHeaderGroup(analyse.ContactMaterialDetails[i].IsFoldOut, "Details");
                if (analyse.ContactMaterialDetails[i].IsFoldOut)
                {
                    EditorGUILayout.LabelField("TAG", analyse.ContactMaterialDetails[i].TAG);
                    EditorGUILayout.ObjectField("ContactMaterial", analyse.ContactMaterialDetails[i].ContactMaterial, typeof(ContactMaterial), false);
                    EditorGUILayout.ObjectField("FrictionModel", analyse.ContactMaterialDetails[i].FrictionModel, typeof(FrictionModel), false);
                }
                EditorGUILayout.EndFoldoutHeaderGroup();
            }
            //绘制analyse.ContactMaterialDetails
            reorderableList = new ReorderableList(serializedObject, contactMaterialDetailsProp);
            reorderableList.drawHeaderCallback = (Rect rect) =>
            {
                EditorGUI.LabelField(rect, "ContactMaterialDetails");
            };
        }

        // 操作按钮
        if (GUILayout.Button("分析接触材料", GUILayout.Height(30)))
        {
            analyse.ContactMaterialDetails.Clear();
            foreach (var contactMaterial in contactMaterialManager.ContactMaterials)
            {
                ShapeMaterial targetMaterial = null;
                if(contactMaterial.Material1 == analyse.CheckMaterial)
                {
                    targetMaterial = contactMaterial.Material2;
                }
                else if (contactMaterial.Material2 == analyse.CheckMaterial)
                {
                    targetMaterial = contactMaterial.Material1;
                }
                if(targetMaterial != null)
                {
                    analyse.ContactMaterialDetails.Add(new ContactMaterialDetail()
                    {
                        TAG = contactMaterial.name,
                        ContactMaterial = targetMaterial,
                        FrictionModel = contactMaterial.FrictionModel
                    });
                }
            }
        }
        
        analyse.AnalyseMaterial = (ShapeMaterial)EditorGUILayout.ObjectField(
            "Analyse Material",   // 字段标签
            analyse.AnalyseMaterial,    // 当前值
            typeof(ShapeMaterial),      // 允许的对象类型
            false       // 是否允许场景对象（仅资源）
        );
        
        analyse.IsLogFullPath = EditorGUILayout.ToggleLeft(
            new GUIContent("是否打印接触物体全路径"), 
            analyse.IsLogFullPath);
        
        // 操作按钮
        if (GUILayout.Button("获取该材料物体", GUILayout.Height(30)))
        {
            if(analyse.AnalyseMaterial == null)
                return;
            bool isprintLog = false;
            Shape[] shapes =  GameObject.FindObjectsOfType<Shape>();
            foreach (var shape in shapes)
            {
                if(shape.Material == analyse.AnalyseMaterial)
                {
                    isprintLog = true;
                    printLog(analyse.IsLogFullPath, shape.transform);
                }
            }
            DeformableTerrain[] terrains =  GameObject.FindObjectsOfType<DeformableTerrain>();
            foreach (var terrain in terrains)
            {
                if(terrain.Material == analyse.AnalyseMaterial)
                {
                    isprintLog = true;
                    printLog(analyse.IsLogFullPath, terrain.transform);
                }
            }

            Track[] tracks =  GameObject.FindObjectsOfType<Track>();
            foreach (var track in tracks)
            {
                if(track.Material == analyse.AnalyseMaterial)
                {
                    isprintLog = true;
                    printLog(analyse.IsLogFullPath, track.transform);
                }
            }
            if (!isprintLog)
            {
                Debug.Log("未找到该材料的物体");
                
            }
        }
        serializedObject.ApplyModifiedProperties();
    }

    private void printLog(bool IsLogFullPath, Transform shapeTransform)
    {
        if(IsLogFullPath)
        {
            Debug.Log(GetFullPath(shapeTransform.transform));
        }
        else
        {
            Debug.Log(shapeTransform.name);
        }
    }


    /// <summary>
    /// 获取Transform的完整层级路径
    /// </summary>
    static string GetFullPath(Transform transform)
    {
        if (transform == null) return "null";
        
        // 使用StringBuilder优化性能
        System.Text.StringBuilder path = new System.Text.StringBuilder();
        Transform current = transform;
        
        while (current != null)
        {
            if (path.Length > 0)
            {
                path.Insert(0, '/');
            }
            path.Insert(0, current.name);
            current = current.parent;
        }
        
        return path.ToString();
    }
}
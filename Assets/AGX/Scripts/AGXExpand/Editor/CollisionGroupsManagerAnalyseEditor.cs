using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using AGXUnity;
using NUnit.Framework;
using Unity.VisualScripting;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;

[CustomEditor(typeof(CollisionGroupsManagerAnalyse))]
public class CollisionGroupsManagerAnalyseEditor : Editor
{
    private SerializedProperty transformsProp;
    private ReorderableList reorderableList;
    private CollisionGroupsManager collisionGroupsManager; 
    private CollisionGroups[] allCollisionGroups;
    private void OnEnable()
    {
        allCollisionGroups =  GameObject.FindObjectsOfType<CollisionGroups>();
        
        transformsProp = serializedObject.FindProperty("AnalyseTargets");
        collisionGroupsManager = target.GetComponent(typeof(CollisionGroupsManager)) as CollisionGroupsManager;
        // 创建可排序列表
        reorderableList = new ReorderableList(serializedObject, transformsProp)
        {
            draggable = true,
            displayAdd = true,
            displayRemove = true,
            drawHeaderCallback = rect => 
                EditorGUI.LabelField(rect, "Target Transforms"),
            
            drawElementCallback = (rect, index, active, focused) => 
            {
                var element = transformsProp.GetArrayElementAtIndex(index);
                EditorGUI.ObjectField(
                    new Rect(rect.x, rect.y + 2, rect.width, EditorGUIUtility.singleLineHeight),
                    element,
                    typeof(Transform),
                    GUIContent.none
                );
            },
            
            elementHeight = EditorGUIUtility.singleLineHeight + 4
        };
    }

    public override void OnInspectorGUI()
    {
        if(target == null)
            return;
        // 获取目标组件
        CollisionGroupsManagerAnalyse analyse = (CollisionGroupsManagerAnalyse)target;
        serializedObject.Update();

        analyse.IsLogFullPath = EditorGUILayout.ToggleLeft(
            new GUIContent("是否打印接触物体全路径"), 
            analyse.IsLogFullPath);
        analyse.IsShowPairInfo= EditorGUILayout.ToggleLeft(
            new GUIContent("是否显示Pair信息"), 
            analyse.IsShowPairInfo);
        
        // 绘制可排序列表
        reorderableList.DoLayoutList();
        
        EditorGUILayout.Space(15);
        
        // 操作按钮
        if (GUILayout.Button("Execute Analyse", GUILayout.Height(30)))
        {
            foreach (var _target in analyse.AnalyseTargets)
            {
                analyseTarget(analyse, _target);
            }
        }
        serializedObject.ApplyModifiedProperties();
    }

    private void analyseTarget(CollisionGroupsManagerAnalyse analyse, Transform analyseTarget)
    {
        CollisionGroups collisionGroups = analyseTarget.GetComponent<CollisionGroups>();
        if (collisionGroups == null)
            return;

        StringBuilder sb = new StringBuilder();
        sb.AppendLine(analyseTarget.name);
        List<string> findTargetTags = new List<string>();
        foreach (var group in collisionGroups.Groups)
        {
            sb.AppendLine($"<color=#FFFF00>{group.Tag}</color>:");
            findTargetTags.Clear();
            CollisionGroupEntry collisionTargetEntry = null;
            foreach (var pair in collisionGroupsManager.DisabledPairs)
            {
                if (pair.First.Tag.Equals(group.Tag))
                {
                    collisionTargetEntry = pair.Second;
                }else if (pair.Second.Tag.Equals(group.Tag))
                {
                    collisionTargetEntry = pair.First;
                }

                if (collisionTargetEntry != null && !findTargetTags.Contains(collisionTargetEntry.Tag))
                { 
                    findTargetTags.Add(collisionTargetEntry.Tag);
                   var findTargets= getCollisionTargets(collisionTargetEntry);
                   if (findTargets.Count > 0)
                   {
                      
                       foreach (var _target in findTargets)
                       {
                           sb.AppendLine("\t" +(analyse.IsLogFullPath ? GetFullPath(_target) : _target.name) 
                                         + $"\t<color=#00FF00>{collisionTargetEntry.Tag}</color>");
                       }
                   }
                }
            }
        }
        Debug.Log(sb.ToString());
    }

    private List<Transform> getCollisionTargets(CollisionGroupEntry collisionTargetEntry)
    {
        List<Transform> colTargets = new List<Transform>();
        foreach (var _target in allCollisionGroups)
        {
            foreach (var _group in _target.Groups)
            {
                if (_group.Tag.Equals(collisionTargetEntry.Tag))
                {
                    colTargets.Add(_target.transform);
                    break;
                }
            }
        }
        return  colTargets;
    }
    /// <summary>
    /// 获取Transform的完整层级路径
    /// </summary>
    static string GetFullPath(Transform transform)
    {
        if (transform == null) return "null";
        
        // 使用StringBuilder优化性能
        System.Text.StringBuilder path = new System.Text.StringBuilder();
        Transform current = transform;
        
        while (current != null)
        {
            if (path.Length > 0)
            {
                path.Insert(0, '/');
            }
            path.Insert(0, current.name);
            current = current.parent;
        }
        
        return path.ToString();
    }
    
}
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using AGXUnity;
using AGXUnity.Collide;
using System;
using AGXUnity.Rendering;
// <summary>
// Unity下Mesh处理成agx下执行的模型(场景中的GameObject)
// 针对AGX扩展开发
// 注意:
//     生成的AGX模型的样子是与碰撞体的大小跟位置一致，非原本的模型Mesh
// AGX模型结构：
//    Root(根节点)
//        -RigidBody1(模块1)
//	        - Body1_Mesh1
//	        - Body1_Mesh2
//        - RigidBody2(模块2)
//	        - Body2_Mesh1
//	        - Body2_Mesh2
//	        - Body2_Mesh3
//        - RigidBody3(模块3)
//	        - Body3_Mesh1
//        - RigidBody其它
//        - Constraint1(RigidBody1与RigidBody2)
//        - Constraint2(RigidBody3与RigidBody3)
//        - Constraint其它
// 模型转换操作流程
//  1、把unity下的树形结构的GameObject转换成线性结构 编辑器指令<AGXUnity/AGXConvert/TreeToLinear>
//  2、根据相应的模型分析整理制作模块1、模块2、模块3... <AGXUnity/AGXConvert/CombineModule>
//         *提示：当个模型作为一个模块也需要点击生成模块 
//  3、按情况给模块下面的mesh添加MeshCollider,检查模型mesh可读，或者增加BoxCollider等Collider
//  4、点击该进行Agx碰撞体转换 编辑器指令 <AGXUnity/AGXConvert/MeshConvert>
// </summary>
public static class AGXExpand
{
    #region Model
    /// <summary>
    /// 把unity下的模型的树形结构改变成线性结构
    /// </summary>
    [MenuItem("GameObject/AGXUnity/AGXConvert/TreeToLinear", priority = 100)]
    public static void AGXMeshConvert_TreeToLinear()
    {
        if (CheckRedo())
            return;
        foreach (var go in Selection.gameObjects)
        {
            GameObject linearGoRoot = new GameObject("TreeToLinear_" + go.name);
            linearGoRoot.transform.SetParent(go.transform.parent);
            GameObject convertGo = GameObject.Instantiate(go, linearGoRoot.transform);
            convertGo.name = go.name;
            handleAGXMeshConvert_TreeToLinear(convertGo.transform, linearGoRoot.transform);
        }
    }
    private static void handleAGXMeshConvert_TreeToLinear(Transform go, Transform root)
    {
        go.SetParent(root);
        Debug.Log("SetParent:" + go.name);
        while (go.childCount > 0)
        {
            handleAGXMeshConvert_TreeToLinear(go.transform.GetChild(0), root);
        }
    }
    #endregion

    #region CombineModule
    [MenuItem("GameObject/AGXUnity/AGXConvert/CombineModule", priority = 101)]
    public static void AGXMeshConvert_CombineModule()
    {
        if (CheckRedo())
            return;
        if (Selection.gameObjects.Length == 0)
            return;
        GameObject combine = new GameObject("CombineModule");
        combine.AddComponent<UnityEngine.Rigidbody>();
        combine.transform.SetParent(Selection.activeGameObject.transform.parent);
        combine.transform.SetSiblingIndex(Selection.activeGameObject.transform.GetSiblingIndex());
        combine.transform.localPosition = Vector3.zero;
        combine.transform.localRotation = Quaternion.identity;
        Undo.RegisterCreatedObjectUndo(combine, "combine");
        foreach (var go in Selection.gameObjects)
        {
            Undo.SetTransformParent(go.transform, combine.transform,"setParent");
        }
    }
    #endregion

    #region MeshConvert
    [MenuItem("GameObject/AGXUnity/AGXConvert/MeshConvert", priority = 102)]
    public static void AGXMeshConvert_MeshConvert()
    {
        if (CheckRedo())
            return;
        bool isDestroyRoot;
        foreach (var go in Selection.gameObjects)
        {
            isDestroyRoot = false;
            Collider[] cols = go.GetComponentsInChildren<Collider>(true);
            for (int i = 0; i < cols.Length; i++)
            {
                if (AGXMeshConvert_MeshConvert_Handle(cols[i].gameObject))
                {
                    if (cols[i].gameObject == go)
                        isDestroyRoot = true;
                    Undo.DestroyObjectImmediate(cols[i].gameObject);
                }
            }

            if (!isDestroyRoot)
            { 
                UnityEngine.Rigidbody[] rigidbodies = go.GetComponentsInChildren<UnityEngine.Rigidbody>(true);
                for (int i = 0; i < rigidbodies.Length; i++)
                {
                    if(AGXMeshConvert_MeshConvert_Handle(rigidbodies[i].gameObject)) 
                    {
                        Undo.DestroyObjectImmediate(cols[i].gameObject);
                    }
                }
            }
            
        }
    }

    private static bool AGXMeshConvert_MeshConvert_Handle(GameObject go)
    {
        GameObject agxGo;
        GameObject convertGo;
        Collider collider = go.GetComponent<Collider>();
        if (collider == null)
        {
            Rigidbody rigidbody = go.GetComponent<UnityEngine.Rigidbody>();
            if (rigidbody != null)
            {
                Undo.DestroyObjectImmediate(rigidbody);
                Undo.AddComponent<AGXUnity.RigidBody>(go);
            }
            return false;
        }
    
        int subindex = go.transform.GetSiblingIndex();
        bool isAppendMeshVisual = false;
        MeshFilter mf = go.GetComponent<MeshFilter>();
        if (collider is BoxCollider)
        {
            if (mf != null && !mf.sharedMesh.name.Equals("Cube"))
		    {
                isAppendMeshVisual = true;
		    }
            if (go.GetComponent<Rigidbody>() != null)
            {
                (convertGo, agxGo) = CreateRigidBody<Box>(go, isAppendMeshVisual);
            }
            else
            {
                agxGo = CreateShape<Box>(go, isAppendMeshVisual);
                convertGo = agxGo;
            }
            SetBox(agxGo.GetComponent<Box>(), go);
        }
        else if (collider is SphereCollider)
        {
            if (mf != null && !mf.sharedMesh.name.Equals("Sphere"))
            {
                isAppendMeshVisual = true;
            }
            if (go.GetComponent<Rigidbody>() != null)
            {
                (convertGo, agxGo) = CreateRigidBody<Sphere>(go, isAppendMeshVisual);
            }
            else
            {
                convertGo = agxGo = CreateShape<Sphere>(go, isAppendMeshVisual);
            }
            SetSphere(agxGo.GetComponent<Sphere>(), go);
        }
        else if (collider is CapsuleCollider)
        {
            bool isCylinder = true;
            if (mf != null && mf.sharedMesh.name.Equals("Capsule"))
            {
                isCylinder = false;
            }
            if (isCylinder)
            {
                if (mf != null && !mf.sharedMesh.name.Equals("Cylinder"))
                {
                    isAppendMeshVisual = true;
                }
                if (go.GetComponent<Rigidbody>() != null)
                {
                    (convertGo, agxGo) = CreateRigidBody<Cylinder>(go, isAppendMeshVisual);
                }
                else
                {
                    convertGo = agxGo = CreateShape<Cylinder>(go, isAppendMeshVisual);
                }
                SetCylinder(agxGo.GetComponent<Cylinder>(), go);
            }
            else
            {
                if (mf != null && !mf.sharedMesh.name.Equals("Capsule"))
                {
                    isAppendMeshVisual = true;
                }
                if (go.GetComponent<Rigidbody>() != null)
                {
                    (convertGo, agxGo) = CreateRigidBody<Capsule>(go, isAppendMeshVisual);
                }
                else
                {
                    convertGo = agxGo = CreateShape<Capsule>(go, isAppendMeshVisual);
                }
                SetCapsule(agxGo.GetComponent<Capsule>(), go);
            }
        }
        else if (collider is MeshCollider)
        {
            if (((MeshCollider)collider).sharedMesh == null)
            {
                Debug.LogError($"{collider.gameObject.name}的MeshCollider没有设置网格");
                return false;
            }
            if (!((MeshCollider)collider).sharedMesh.isReadable)
            {
                Debug.LogError($"{collider.gameObject.name}的mesh网格需要设置成可读写");
                return false;
            }
            if (go.GetComponent<Rigidbody>() != null)
            {
                (convertGo, agxGo) = CreateRigidBody<AGXUnity.Collide.Mesh>(go, isAppendMeshVisual);
            }
            else
            {
                convertGo = agxGo = CreateShape<AGXUnity.Collide.Mesh>(go, isAppendMeshVisual);
            }
            AGXUnity.Collide.Mesh mesh = agxGo.GetComponent<AGXUnity.Collide.Mesh>();
            SetMesh(mesh, go);
            //AGXUnityEditor.Tools.ShapeVisualCreateTool.CanCreateVisual(mesh);
        }
        else
        {
            Debug.LogError("未处理Collider:" + collider.GetType());
            return false;
        }
        convertGo.SetActive(go.activeSelf);
        convertGo.transform.SetSiblingIndex(subindex + 1);
        if (isAppendMeshVisual)
        {
            GameObject meshVisual = new GameObject(go.name + "_MeshVisual");
            meshVisual.transform.SetParent(go.transform.parent);
            meshVisual.transform.position = go.transform.position;
            meshVisual.transform.localScale = go.transform.localScale;
            meshVisual.transform.localRotation = go.transform.localRotation;
            meshVisual.AddComponent<MeshFilter>().sharedMesh = mf.sharedMesh;
            meshVisual.transform.SetParent(convertGo.transform);
            MeshRenderer mr = go.GetComponent<MeshRenderer>();
            if (mr != null)
            {
                MeshRenderer mrVisual = meshVisual.AddComponent<MeshRenderer>();
                mrVisual.sharedMaterials = mr.sharedMaterials;
            }
            meshVisual.transform.SetSiblingIndex(subindex + 2);
            Undo.RegisterCreatedObjectUndo(meshVisual, "meshVisual");
        }
        Debug.Log("生成AGX模型:" + convertGo.name);
        return true;
    }

    private static bool CheckConverted(GameObject go)
    {
        if (go.GetComponent<Box>() != null
            || go.GetComponent<Sphere>() != null
            || go.GetComponent<Capsule>() != null
            || go.GetComponent<Cylinder>() != null
            || go.GetComponent<HollowCylinder>() != null
            || go.GetComponent<Cone>() != null
            || go.GetComponent<HollowCone>() != null
            || go.GetComponent<AGXUnity.Collide.Mesh>() != null
            )
        {
            Debug.Log($"{go.name}已经转换成AgxMesh，无需转换");
            return true;
        }
        return false;
    }

	private static void SetBox(Box shape, GameObject go)
    {
        BoxCollider collider = go.GetComponent<BoxCollider>();
        shape.transform.position = go.transform.TransformPoint(collider.center);
        shape.transform.rotation = go.transform.rotation;
        shape.transform.localScale = Vector3.one;
        Vector3 globalScale = go.transform.GetGlobalScale();
        shape.HalfExtents = Vector3.Scale(globalScale, new Vector3(0.5f * collider.size.x, 0.5f * collider.size.y, 0.5f * collider.size.z));
    }

    private static void SetSphere(Sphere shape, GameObject go)
    {
        SphereCollider collider = go.GetComponent<SphereCollider>();
        shape.transform.position = go.transform.TransformPoint(collider.center);
        shape.transform.rotation = go.transform.rotation;
        shape.transform.localScale = Vector3.one;
        Vector3 globalScale = go.transform.GetGlobalScale();
        float scale = Mathf.Max(globalScale.x, globalScale.y, globalScale.z);
        shape.Radius = collider.radius * scale;
    }

    private static void SetCapsule(Capsule shape, GameObject go)
    {
        CapsuleCollider collider = go.GetComponent<CapsuleCollider>();
        shape.transform.position = go.transform.TransformPoint(collider.center);
        shape.transform.rotation = go.transform.rotation;
        shape.transform.localScale = Vector3.one;
        Vector3 globalScale = go.transform.GetGlobalScale();
        float scale = Mathf.Max(globalScale.x, globalScale.z);
        shape.Radius = collider.radius * scale;
        shape.Height = collider.height * globalScale.y - shape.Radius * 2.0f;
    }
    private static void SetCylinder(Cylinder shape, GameObject go)
    {
        CapsuleCollider collider = go.GetComponent<CapsuleCollider>();
        shape.transform.position = go.transform.TransformPoint(collider.center);
        shape.transform.rotation = go.transform.rotation;
        shape.transform.localScale = Vector3.one;
        Vector3 globalScale = go.transform.GetGlobalScale();
        float scale = Mathf.Max(globalScale.x, globalScale.z);
        shape.Radius = collider.radius * scale;
        shape.Height = collider.height * globalScale.y;
    }

    private static void SetMesh(AGXUnity.Collide.Mesh shape, GameObject go)
    {
        shape.transform.position = go.transform.position;
        shape.transform.rotation = go.transform.rotation;
        shape.transform.localScale = go.transform.localScale;
    }


    private static GameObject CreateShape<T>(GameObject gameObject, bool isAppendMeshVisual)
  where T : Shape
    {
        var go = Factory.Create<T>();
        if (go == null)
            return null;
      
        var parent = gameObject.transform.parent;
        if (parent != null)
            go.transform.SetParent(parent.transform, false);
        go.transform.localScale = Vector3.one;
        T shape = go.GetComponent<T>();
        if (shape is AGXUnity.Collide.Mesh)
        {
            MeshCollider collider = gameObject.GetComponent<MeshCollider>();
            (shape as AGXUnity.Collide.Mesh).SetSourceObject(collider.sharedMesh);
        }
        AGXUnity.Rendering.ShapeVisual.Create(shape);
        go.name = gameObject.name;
        if (go.transform.childCount > 0)
        { 
            Transform visual = go.transform.GetChild(0);
            visual.name = gameObject.name + "_Visual";
            MeshRenderer mr = gameObject.GetComponent<MeshRenderer>();
            if (mr != null && !isAppendMeshVisual)
            {
                if (visual.GetComponent<MeshRenderer>() != null)
                {
					visual.GetComponent<MeshRenderer>().sharedMaterials = mr.sharedMaterials;
					//visual.GetComponent<MeshRenderer>().sharedMaterial = ShapeVisual.DefaultMaterial;
				}
                else
                {
                    MeshRenderer[] mrs = visual.GetComponentsInChildren<MeshRenderer>(true);
                    for (int i = 0; i < mrs.Length; i++)
                    {
						mrs[i].sharedMaterials = mr.sharedMaterials;
						//mrs[i].sharedMaterial = ShapeVisual.DefaultMaterial;
					}
                }
            }
            //visual.gameObject.SetActive(!isAppendMeshVisual);
            if (isAppendMeshVisual)
            {
                //await Task.Delay(10);
                //visual.gameObject.SetActive(false);
            }
			visual.gameObject.SetActive(true);
		}
        Undo.RegisterCreatedObjectUndo(go, "shape");
        return go;
    }

    private static (GameObject, GameObject) CreateRigidBody<T>(GameObject gameObject, bool isAppendMeshVisual) where T : Shape
    {
        GameObject shape = CreateShape<T>(gameObject, isAppendMeshVisual);
        return (CreateRigidBody(gameObject, shape), shape);
    }

    private static GameObject CreateRigidBody(GameObject gameObject, GameObject child = null)
    {
        var parent = gameObject.transform.parent;
        var go = child != null ?
                   Factory.Create<RigidBody>(child) :
                   Factory.Create<RigidBody>();
        if (go == null)
            return null;

        if (parent != null)
            go.transform.SetParent(parent.transform, false);
        go.name = gameObject.name;
        Undo.RegisterCreatedObjectUndo(go, "Rigid body");

        return go;
    }

    #endregion


    static float lastDoTime;
    private static bool CheckRedo()
    {
        if (Time.time - lastDoTime > 0.5f)
        {
            lastDoTime = Time.time;
            return false;
        }
        return true;
    }

    public static Vector3 GetGlobalScale(this Transform transform)
    {
        Vector3 globalScale = transform.localScale;
        Transform parent = transform.parent;
        while (parent != null)
        {
            globalScale = Vector3.Scale(globalScale, parent.localScale);
            parent = parent.parent;
        }
        return globalScale;
    }

}


using UnityEditor;
using UnityEngine;

public static class SkinnedMeshRendererToMesh
{
    [MenuItem("GameObject/AGXUnity/ConvertSkinnedMeshRenderer2Mesh", priority = 102)]
    public static void ConvertSkinnedMeshRenderer2Mesh()
    {
        if (CheckRedo())
            return;
        foreach (var go in Selection.gameObjects)
        {
            var smr = go.GetComponent<SkinnedMeshRenderer>();
            if (smr != null)
            {
                Material mat = smr.sharedMaterial;
                Mesh mash = smr.sharedMesh;
                Undo.DestroyObjectImmediate(smr);
                MeshFilter mf = Undo.AddComponent<MeshFilter>(go);
                mf.sharedMesh = mash;
                MeshRenderer mr = Undo.AddComponent<MeshRenderer>(go);
                mr.sharedMaterial = mat;
            }
        }
    }
    
    static float lastDoTime;
    private static bool CheckRedo()
    {
        if (Time.time - lastDoTime > 0.5f)
        {
            lastDoTime = Time.time;
            return false;
        }
        return true;
    }
}
using UnityEngine;

/// <summary>
/// 按键控制移动
/// </summary>
public class MovementController : MonoBeh<PERSON><PERSON>
{
    [Header("移动参数")]
    public float moveAcceleration = 10f;    // 移动加速度
    public float moveDeceleration = 15f;    // 移动减速度
    public float maxMoveSpeed = 5f;         // 最大移动速度

    [Header("旋转参数")]
    public float rotateAcceleration = 90f;  // 旋转加速度（度/秒²）
    public float rotateDeceleration = 120f; // 旋转减速度
    public float maxRotateSpeed = 180f;     // 最大旋转速度（度/秒）

    private Vector3 _velocity;              // 三维移动速度（x:左右，y:上下，z:前后）
    private float _rotationVelocity;       // 旋转速度（绕Y轴）

    void Update()
    {
        float deltaTime = Time.deltaTime;
        
        // 获取输入
        float moveForwardback = Input.GetAxis("Vertical");    // 前后 (W/S)
        float moveRotate = Input.GetAxis("Horizontal");// 左右 (A/D)
        float moveUpDown = Input.GetAxis("updown");       // 旋转 (Q/E 或其他自定义轴)

        // 处理各轴向移动
        // HandleMovementAxis(moveRotate, ref _velocity.x, deltaTime);
        HandleMovementAxis(moveForwardback, ref _velocity.z, deltaTime);
        HandleMovementAxis(moveUpDown, ref _velocity.y, deltaTime);

        // 应用移动
        transform.Translate(_velocity * deltaTime);
        // 处理旋转
        HandleRotationAxis(moveRotate, deltaTime);
        // 应用旋转
        transform.Rotate(0, _rotationVelocity * deltaTime, 0);
    }

    // 处理移动轴向
    void HandleMovementAxis(float input, ref float currentSpeed, float deltaTime)
    {
        float targetSpeed = input * maxMoveSpeed;
        float acceleration = input != 0 ? moveAcceleration : moveDeceleration;
        
        currentSpeed = Mathf.MoveTowards(currentSpeed, targetSpeed, acceleration * deltaTime);
    }

    // 处理旋转轴向
    void HandleRotationAxis(float input, float deltaTime)
    {
        float targetSpeed = input * maxRotateSpeed;
        float acceleration = input != 0 ? rotateAcceleration : rotateDeceleration;
        
        _rotationVelocity = Mathf.MoveTowards(_rotationVelocity, targetSpeed, acceleration * deltaTime);
    }
    
}

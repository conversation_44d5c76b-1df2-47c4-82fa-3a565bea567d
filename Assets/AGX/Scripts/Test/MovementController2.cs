using UnityEngine;

/// <summary>
/// 移动到目标位置
/// </summary>
public class AutoMovementController2 : MonoBehaviour
{
    [Header("移动参数")]
    public float moveAcceleration = 10f;
    public float moveDeceleration = 15f;
    public float maxMoveSpeed = 5f;

    [Header("旋转参数")]
    public float fixedRotateSpeed = 90f; // 固定旋转速度（度/秒）

    [Header("目标设置")]
    public Transform target;
    public float stoppingDistance = 0.5f;
    public float slowdownRadius = 3f;
    public float heightOffset = 0.3f;

    private Vector3 _velocity;

    void Update()
    {
        if (target == null) return;

        float deltaTime = Time.deltaTime;
        Vector3 toTarget = target.position - transform.position;
        float totalDistance = toTarget.magnitude;

        // 到达目标时停止
        if (totalDistance <= stoppingDistance)
        {
            _velocity = Vector3.zero;
            return;
        }

        // 分解三维运动
        Vector3 horizontalDir = new Vector3(toTarget.x, 0, toTarget.z).normalized;
        float verticalDistance = toTarget.y;
        float horizontalDistance = new Vector3(toTarget.x, 0, toTarget.z).magnitude;

        // 计算本地坐标系方向
        Vector3 localDir = transform.InverseTransformDirection(horizontalDir);

        // 自动生成输入控制
        float moveInput = CalculateHorizontalInput(localDir.z, horizontalDistance);
        float verticalInput = CalculateVerticalInput(verticalDistance);

        // 处理移动
        HandleMovementAxis(moveInput, ref _velocity.z, deltaTime);
        HandleMovementAxis(verticalInput, ref _velocity.y, deltaTime);

        // 固定速度旋转
        HandleFixedRotation(localDir.x, deltaTime);

        // 应用运动
        transform.Translate(_velocity * deltaTime);
    }

    float CalculateHorizontalInput(float localForward, float horizontalDistance)
    {
        float slowdown = Mathf.Clamp01(horizontalDistance / slowdownRadius);
        return Mathf.Sign(localForward) * slowdown;
    }

    float CalculateVerticalInput(float verticalDistance)
    {
        if (Mathf.Abs(verticalDistance) < heightOffset) return 0f;
        float slowdown = Mathf.Clamp01(Mathf.Abs(verticalDistance) / slowdownRadius);
        return Mathf.Sign(verticalDistance) * slowdown;
    }

    void HandleMovementAxis(float input, ref float currentSpeed, float deltaTime)
    {
        float targetSpeed = input * maxMoveSpeed;
        float acceleration = Mathf.Abs(input) > 0.01f ? moveAcceleration : moveDeceleration;
        currentSpeed = Mathf.MoveTowards(currentSpeed, targetSpeed, acceleration * deltaTime);
    }

    void HandleFixedRotation(float localRight, float deltaTime)
    {
        // 直接使用固定旋转速度
        float rotateDirection = Mathf.Sign(localRight);
        transform.Rotate(0, rotateDirection * fixedRotateSpeed * deltaTime, 0);
    }
}

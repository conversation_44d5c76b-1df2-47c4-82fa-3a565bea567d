# ROV自适应朝向控制功能使用指南

## 功能概述

ROV自适应朝向控制功能可以让ROV在移动到目标位置的过程中，根据当前朝向与目标位置的角度差自动选择最优的旋转策略：

- **大角度差（>45°）**：快速旋转模式，优先旋转到正确朝向
- **中角度差（15°-45°）**：自适应旋转模式，边移动边旋转
- **小角度差（<15°）**：精细调整模式，缓慢调整朝向
- **已对准（<2°）**：停止旋转调整

## 主要参数

### 角度阈值参数
- `FastRotationAngleThreshold`：快速旋转角度阈值（默认15°）
- `AdaptiveRotationAngleThreshold`：自适应旋转角度阈值（默认45°）
- `HeadingToleranceAngle`：朝向容差角度（默认2°）

### 速度控制参数
- `FastRotationSpeedMultiplier`：快速旋转时的速度倍数（默认3.0x）
- `AdaptiveRotationSpeedMultiplier`：自适应旋转时的速度倍数（默认1.5x）

### 行为控制参数
- `EnableAdaptiveHeading`：启用自适应朝向控制
- `EnableMoveWhileRotating`：启用边移动边旋转

## 使用方法

### 1. 基本使用

```csharp
// 获取ROV控制器
RovController rovController = GetComponent<RovController>();

// 设置朝向目标位置（会自动启用朝向控制）
Vector3 targetPosition = new Vector3(10, 0, 10);
rovController.SetTargetPositionWithHeading(targetPosition, true);
```

### 2. 配置参数

```csharp
// 配置自适应朝向参数
rovController.SetAdaptiveHeadingParameters(
    enable: true,                    // 启用朝向控制
    fastRotationThreshold: 15f,      // 快速旋转阈值
    adaptiveRotationThreshold: 45f,  // 自适应旋转阈值
    fastSpeedMultiplier: 3.0f,       // 快速旋转速度倍数
    adaptiveSpeedMultiplier: 1.5f,   // 自适应旋转速度倍数
    toleranceAngle: 2f,              // 朝向容差
    enableMoveWhileRotating: true    // 边移动边旋转
);
```

### 3. 手动控制朝向

```csharp
// 只启用朝向控制，不移动位置
Vector3 lookAtPosition = new Vector3(5, 0, 5);
rovController.SetHeadingControl(true, lookAtPosition);

// 禁用朝向控制
rovController.SetHeadingControl(false);
```

### 4. 获取状态信息

```csharp
// 获取当前朝向误差
float headingError = rovController.GetCurrentHeadingError();

// 获取当前朝向控制模式
AdaptiveHeadingMode mode = rovController.GetCurrentHeadingMode();

// 检查是否到达目标
bool isAtTarget = rovController.IsAtTarget();
```

## 控制模式说明

### OnTarget（已对准）
- 朝向误差 ≤ 2°
- 不进行旋转调整
- 专注于位置控制

### FineAdjustment（精细调整）
- 朝向误差 2° - 15°
- 缓慢调整朝向
- 同时进行位置和旋转控制

### AdaptiveRotation（自适应旋转）
- 朝向误差 15° - 45°
- 边移动边旋转
- 旋转速度适中，确保能在移动过程中完成朝向调整

### FastRotation（快速旋转）
- 朝向误差 > 45°
- 优先快速旋转
- 可选择是否同时移动（由EnableMoveWhileRotating控制）

## 测试工具

使用`AdaptiveHeadingTester`组件可以方便地测试朝向控制功能：

1. 将`AdaptiveHeadingTester`组件添加到场景中
2. 设置`rovController`引用
3. 配置测试目标点`testTargets`
4. 运行场景并使用以下快捷键：
   - `1`：测试快速旋转
   - `2`：测试自适应旋转
   - `3`：测试精细调整
   - `Esc`：停止测试

## 最佳实践

1. **角度阈值设置**：
   - 快速旋转阈值不宜过小，避免频繁切换模式
   - 自适应旋转阈值应该给ROV足够的时间完成旋转

2. **速度倍数设置**：
   - 快速旋转速度不宜过高，避免超调
   - 自适应旋转速度应该与移动速度匹配

3. **边移动边旋转**：
   - 对于远距离目标，建议启用边移动边旋转
   - 对于精确定位任务，可以禁用以优先完成旋转

4. **容差角度**：
   - 根据任务精度要求调整容差角度
   - 过小的容差可能导致持续微调

## 调试技巧

1. 启用日志记录查看详细控制信息
2. 使用测试工具观察不同模式的行为
3. 调整参数时逐步进行，观察效果
4. 利用Gizmos可视化ROV朝向和目标方向

using UnityEngine;

/// <summary>
/// 自适应朝向控制测试器
/// 用于测试ROV的自适应朝向控制功能
/// </summary>
public class AdaptiveHeadingTester : MonoBehaviour
{
    [Header("测试配置")]
    public RovController rovController;
    public Transform[] testTargets;  // 测试目标点
    public bool autoTest = false;    // 自动测试模式
    public float testInterval = 10f; // 测试间隔时间
    
    [Header("朝向控制参数")]
    [Range(5f, 30f)]
    public float fastRotationThreshold = 15f;
    [Range(30f, 90f)]
    public float adaptiveRotationThreshold = 45f;
    [Range(1f, 5f)]
    public float fastRotationSpeed = 3.0f;
    [Range(1f, 3f)]
    public float adaptiveRotationSpeed = 1.5f;
    [Range(1f, 10f)]
    public float toleranceAngle = 2f;
    public bool enableMoveWhileRotating = true;
    
    [Header("调试信息")]
    public bool showDebugInfo = true;
    public bool showGizmos = true;
    
    private int currentTargetIndex = 0;
    private float lastTestTime = 0f;
    private bool isTesting = false;
    
    void Start()
    {
        if (rovController == null)
        {
            rovController = FindObjectOfType<RovController>();
        }
        
        if (rovController == null)
        {
            Debug.LogError("未找到RovController组件！");
            return;
        }
        
        // 应用初始参数
        ApplyHeadingParameters();
        
        Debug.Log("自适应朝向控制测试器已初始化");
    }
    
    void Update()
    {
        if (rovController == null) return;
        
        // 实时更新参数
        if (Application.isPlaying)
        {
            ApplyHeadingParameters();
        }
        
        // 自动测试逻辑
        if (autoTest && testTargets != null && testTargets.Length > 0)
        {
            if (Time.time - lastTestTime > testInterval)
            {
                StartNextTest();
            }
        }
        
        // 显示调试信息
        if (showDebugInfo)
        {
            DisplayDebugInfo();
        }
        
        // 键盘控制
        HandleKeyboardInput();
    }
    
    void OnGUI()
    {
        if (!showDebugInfo) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 400, 300));
        GUILayout.Label("=== 自适应朝向控制测试 ===", GUI.skin.box);
        
        if (rovController != null)
        {
            GUILayout.Label($"当前朝向误差: {rovController.GetCurrentHeadingError():F1}°");
            GUILayout.Label($"朝向控制模式: {rovController.GetCurrentHeadingMode()}");
            GUILayout.Label($"到目标距离: {rovController.GetDistanceToTarget():F2}m");
            GUILayout.Label($"是否到达目标: {(rovController.IsAtTarget() ? "是" : "否")}");
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("测试快速旋转 (大角度)"))
        {
            TestFastRotation();
        }
        
        if (GUILayout.Button("测试自适应旋转 (中角度)"))
        {
            TestAdaptiveRotation();
        }
        
        if (GUILayout.Button("测试精细调整 (小角度)"))
        {
            TestFineAdjustment();
        }
        
        if (GUILayout.Button("停止测试"))
        {
            StopTest();
        }
        
        GUILayout.EndArea();
    }
    
    void OnDrawGizmos()
    {
        if (!showGizmos || rovController == null) return;
        
        // 绘制ROV当前位置和朝向
        Vector3 rovPos = rovController.transform.position;
        Vector3 rovForward = rovController.transform.forward;
        
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(rovPos, 0.5f);
        Gizmos.DrawRay(rovPos, rovForward * 2f);
        
        // 绘制目标点
        if (testTargets != null)
        {
            for (int i = 0; i < testTargets.Length; i++)
            {
                if (testTargets[i] != null)
                {
                    Gizmos.color = i == currentTargetIndex ? Color.red : Color.green;
                    Gizmos.DrawWireSphere(testTargets[i].position, 0.3f);
                    
                    // 绘制到目标的方向线
                    if (i == currentTargetIndex)
                    {
                        Gizmos.color = Color.yellow;
                        Gizmos.DrawLine(rovPos, testTargets[i].position);
                    }
                }
            }
        }
    }
    
    private void ApplyHeadingParameters()
    {
        if (rovController != null)
        {
            rovController.SetAdaptiveHeadingParameters(
                true,
                fastRotationThreshold,
                adaptiveRotationThreshold,
                fastRotationSpeed,
                adaptiveRotationSpeed,
                toleranceAngle,
                enableMoveWhileRotating
            );
        }
    }
    
    private void DisplayDebugInfo()
    {
        if (rovController == null) return;
        
        // 每秒更新一次调试信息
        if (Time.frameCount % 60 == 0)
        {
            float headingError = rovController.GetCurrentHeadingError();
            AdaptiveHeadingMode mode = rovController.GetCurrentHeadingMode();
            
            Debug.Log($"朝向控制状态 - 误差: {headingError:F1}°, 模式: {mode}");
        }
    }
    
    private void HandleKeyboardInput()
    {
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            TestFastRotation();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            TestAdaptiveRotation();
        }
        else if (Input.GetKeyDown(KeyCode.Alpha3))
        {
            TestFineAdjustment();
        }
        else if (Input.GetKeyDown(KeyCode.Escape))
        {
            StopTest();
        }
    }
    
    private void StartNextTest()
    {
        if (testTargets == null || testTargets.Length == 0) return;
        
        currentTargetIndex = (currentTargetIndex + 1) % testTargets.Length;
        Transform target = testTargets[currentTargetIndex];
        
        if (target != null)
        {
            rovController.TestAdaptiveHeading(target.position);
            lastTestTime = Time.time;
            isTesting = true;
            
            Debug.Log($"开始测试目标 {currentTargetIndex + 1}: {target.position:F2}");
        }
    }
    
    public void TestFastRotation()
    {
        Vector3 rovPos = rovController.transform.position;
        Vector3 targetPos = rovPos + rovController.transform.right * 10f; // 90度方向
        
        rovController.TestAdaptiveHeading(targetPos);
        Debug.Log("测试快速旋转模式 - 目标在90度方向");
    }
    
    public void TestAdaptiveRotation()
    {
        Vector3 rovPos = rovController.transform.position;
        Vector3 targetPos = rovPos + (rovController.transform.forward + rovController.transform.right).normalized * 10f; // 45度方向
        
        rovController.TestAdaptiveHeading(targetPos);
        Debug.Log("测试自适应旋转模式 - 目标在45度方向");
    }
    
    public void TestFineAdjustment()
    {
        Vector3 rovPos = rovController.transform.position;
        Vector3 targetPos = rovPos + rovController.transform.forward * 10f + rovController.transform.right * 1f; // 小角度偏移
        
        rovController.TestAdaptiveHeading(targetPos);
        Debug.Log("测试精细调整模式 - 目标在小角度偏移方向");
    }
    
    public void StopTest()
    {
        if (rovController != null)
        {
            rovController.StopMovement();
            rovController.SetHeadingControl(false);
        }
        
        isTesting = false;
        Debug.Log("停止朝向控制测试");
    }
}

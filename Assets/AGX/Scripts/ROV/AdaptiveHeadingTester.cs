using UnityEngine;

/// <summary>
/// 自适应朝向控制测试器
/// 专注于测试ROV的快速旋转功能
/// </summary>
public class AdaptiveHeadingTester : MonoBehaviour
{
    [Header("测试配置")]
    public RovController rovController;

    [Header("旋转测试参数")]
    [Range(0.1f, 5.0f)]
    public float rotationStrength = 2.0f;
    [Range(5f, 30f)]
    public float fastRotationThreshold = 15f;
    [Range(30f, 90f)]
    public float adaptiveRotationThreshold = 45f;
    [Range(1f, 5f)]
    public float fastRotationSpeed = 3.0f;
    [Range(1f, 3f)]
    public float adaptiveRotationSpeed = 1.5f;
    [Range(1f, 10f)]
    public float toleranceAngle = 2f;

    [Header("调试信息")]
    public bool showDebugInfo = true;
    public bool showGizmos = true;
    
    void Start()
    {
        if (rovController == null)
        {
            rovController = FindObjectOfType<RovController>();
        }

        if (rovController == null)
        {
            Debug.LogError("未找到RovController组件！");
            return;
        }

        // 应用初始参数
        ApplyHeadingParameters();

        Debug.Log("自适应朝向控制测试器已初始化");
        Debug.Log("按键说明：");
        Debug.Log("Q - 测试快速旋转 (90度)");
        Debug.Log("W - 测试自适应旋转 (45度)");
        Debug.Log("E - 测试精细调整 (10度)");
        Debug.Log("Space - 停止测试");
    }

    void Update()
    {
        if (rovController == null) return;

        // 实时更新参数
        if (Application.isPlaying)
        {
            ApplyHeadingParameters();
        }

        // 显示调试信息
        if (showDebugInfo)
        {
            DisplayDebugInfo();
        }

        // 键盘控制
        HandleKeyboardInput();
    }
    
    void OnGUI()
    {
        if (!showDebugInfo) return;

        GUILayout.BeginArea(new Rect(10, 10, 400, 250));
        GUILayout.Label("=== 快速旋转测试 ===", GUI.skin.box);

        GUILayout.Label($"旋转强度: {rotationStrength:F1}x");

        if (rovController != null)
        {
            GUILayout.Label($"当前朝向误差: {rovController.GetCurrentHeadingError():F1}°");
            GUILayout.Label($"朝向控制模式: {rovController.GetCurrentHeadingMode()}");
            GUILayout.Label($"到目标距离: {rovController.GetDistanceToTarget():F2}m");
        }

        GUILayout.Space(10);

        if (GUILayout.Button("测试快速旋转 (90度)"))
        {
            TestFastRotation();
        }

        if (GUILayout.Button("测试自适应旋转 (45度)"))
        {
            TestAdaptiveRotation();
        }

        if (GUILayout.Button("测试精细调整 (10度)"))
        {
            TestFineAdjustment();
        }

        if (GUILayout.Button("停止测试"))
        {
            StopTest();
        }

        GUILayout.EndArea();
    }
    
    void OnDrawGizmos()
    {
        if (!showGizmos || rovController == null) return;

        // 绘制ROV当前位置和朝向
        Vector3 rovPos = rovController.transform.position;
        Vector3 rovForward = rovController.transform.forward;

        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(rovPos, 0.5f);
        Gizmos.DrawRay(rovPos, rovForward * 3f);

        // 如果有朝向目标，绘制目标方向
        if (rovController.GetCurrentHeadingMode() != AdaptiveHeadingMode.OnTarget)
        {
            // 绘制一个简单的目标方向指示
            Gizmos.color = Color.red;
            Vector3 rightDirection = rovController.transform.right;
            Gizmos.DrawRay(rovPos, rightDirection * 2f);
        }
    }
    
    private void ApplyHeadingParameters()
    {
        if (rovController != null)
        {
            rovController.SetAdaptiveHeadingParameters(
                enable: true,
                fastRotationThreshold: fastRotationThreshold,
                adaptiveRotationThreshold: adaptiveRotationThreshold,
                fastSpeedMultiplier: fastRotationSpeed,
                adaptiveSpeedMultiplier: adaptiveRotationSpeed,
                toleranceAngle: toleranceAngle,
                enableMoveWhileRotating: true,
                rotationStrength: rotationStrength
            );
        }
    }
    
    private void DisplayDebugInfo()
    {
        if (rovController == null) return;
        
        // 每秒更新一次调试信息
        if (Time.frameCount % 60 == 0)
        {
            float headingError = rovController.GetCurrentHeadingError();
            AdaptiveHeadingMode mode = rovController.GetCurrentHeadingMode();
            
            Debug.Log($"朝向控制状态 - 误差: {headingError:F1}°, 模式: {mode}");
        }
    }
    
    private void HandleKeyboardInput()
    {
        if (Input.GetKeyDown(KeyCode.Q))
        {
            TestFastRotation();
        }
        else if (Input.GetKeyDown(KeyCode.W))
        {
            TestAdaptiveRotation();
        }
        else if (Input.GetKeyDown(KeyCode.E))
        {
            TestFineAdjustment();
        }
        else if (Input.GetKeyDown(KeyCode.Space))
        {
            StopTest();
        }
    }
    
    public void TestFastRotation()
    {
        Vector3 rovPos = rovController.transform.position;
        Vector3 targetPos = rovPos + rovController.transform.right * 10f; // 90度方向

        rovController.SetTargetPositionWithHeading(targetPos, true);
        Debug.Log("测试快速旋转模式 - 目标在90度方向");
        Debug.Log($"旋转强度: {rotationStrength:F1}x");
    }

    public void TestAdaptiveRotation()
    {
        Vector3 rovPos = rovController.transform.position;
        Vector3 targetPos = rovPos + (rovController.transform.forward + rovController.transform.right).normalized * 10f; // 45度方向

        rovController.SetTargetPositionWithHeading(targetPos, true);
        Debug.Log("测试自适应旋转模式 - 目标在45度方向");
        Debug.Log($"旋转强度: {rotationStrength:F1}x");
    }

    public void TestFineAdjustment()
    {
        Vector3 rovPos = rovController.transform.position;
        Vector3 targetPos = rovPos + rovController.transform.forward * 10f + rovController.transform.right * 1f; // 小角度偏移

        rovController.SetTargetPositionWithHeading(targetPos, true);
        Debug.Log("测试精细调整模式 - 目标在小角度偏移方向");
        Debug.Log($"旋转强度: {rotationStrength:F1}x");
    }
    
    public void StopTest()
    {
        if (rovController != null)
        {
            rovController.StopMovement();
            rovController.SetHeadingControl(false);
        }

        Debug.Log("停止朝向控制测试");
    }
}

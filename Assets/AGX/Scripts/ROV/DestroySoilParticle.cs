
using System;
using System.Collections;
using AGXUnity;
using AGXUnity.Collide;
using AGXUnity.Model;
using UnityEngine;


public class DestroySoilParticle : MonoBehaviour
{
    private float time;
    public bool isDestorySoilParticle = true;
    DeformableTerrainBase terrainBase
    {
        get
        {
            return GetComponent<TrenchingMachineGrpController>().terrainBase;
        }
    }

    private void Awake()
    {
        if (isDestorySoilParticle)
        {
            Simulation.Instance.StepCallbacks.SimulationPost += OnSimulationPost;
        }
    }
    
    private void OnSimulationPost()
    {
        var coarseParticles = terrainBase.GetParticles();
        if ( coarseParticles == null )
            return;
        coarseParticles.begin();
        int m_numCoarseParticles = (int)coarseParticles.size();
        var ps = Simulation.Instance.Native.getParticleSystem();
        for (uint i = 0; i < m_numCoarseParticles; ++i)
        {
             var ptr = coarseParticles.at(i);
            ps.destroyParticle(ptr);
        }
    }

}
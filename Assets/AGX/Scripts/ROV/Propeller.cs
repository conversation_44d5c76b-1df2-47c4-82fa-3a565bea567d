using agx;
using UnityEngine;
using RigidBody = AGXUnity.RigidBody;

public enum PropellerPos
{
    Forward_Right = 0,
    Forward_Left = 1,
    Backward_Left = 2, 
    Backward_Right = 3,
    Up_Right = 4,
    Up_Left = 5,
}

public  class Propeller
{
    public readonly Vec3 local_position;
    public readonly Vec3 local_direction;
    public readonly RigidBody rov;
    public Propeller(Vec3 position, Vec3 direction, RigidBody rigidBody)
    {
        local_position = position;
        local_direction = direction;
        rov = rigidBody;
    }
    
    public Propeller(Vector3 position, Vector3 direction, RigidBody rigidBody)
    {
        local_position = position.Vector3ToVec3();
        local_direction = direction.Vector3ToVec3();
        rov = rigidBody;
    }

    public void apply_force(float f)
    {
        var world_dir = rov.Native.getFrame().transformVectorToWorld(local_direction);
        world_dir.normalize();
        var force = world_dir * f;
        rov.Native.addForceAtLocalPosition(force, local_position);
    }
}
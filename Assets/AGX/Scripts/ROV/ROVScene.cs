using System.Linq;
using AGXUnity.Collide;
using UnityEngine;
using RangeReal = AGXUnity.RangeReal;
using RigidBody = AGXUnity.RigidBody;
using Simulation = AGXUnity.Simulation;
using Wire = AGXUnity.Wire;

public class ROVScene : MonoBehaviour
{
    public RigidBody tms;
    public Wire umbilicalWire;
    public Wire tetherCable;
    ROV Rov;
    public void SetRovAndTms(ROV rov)
    {
        Rov = rov;
        SetTMS();
        Simulation.Instance.StepCallbacks.SimulationPre +=  SetWinchForceRange;
    }

    private void SetTMS()
    {
        var Winch = tetherCable.Route.First().Winch;
        Winch.Native.setAutoFeed(true);
        Winch.PulledInLength = 400.0f;
        umbilicalWire.Native.getFirstNode().getHighResolutionRange()
            .set(0, 50, 1.0f);
        umbilicalWire.Native.setResolutionPerUnitLength(0.01f);
    }

    private void SetWinchForceRange()
    {
        double mass = tetherCable.Native.getMass();
        var Winch = tetherCable.Route.First().Winch;
        double length = tetherCable.Native.getCurrentLength(false);
        var volume = Mathf.PI * tetherCable.Native.getRadius() * tetherCable.Native.getRadius() * length;
        //计算浸没质量（1000 是水的密度 kg/m³）
        var mass_immersed = mass - volume * 1000;
        //设置绞车力范围为浸没质量的10倍
        float forceRange = (float)(10 * mass_immersed);
        Winch.ForceRange = new RangeReal(forceRange);
    }

    public void disable_hydrodynamics(Shape water, Box rovBox)
    {
        string tag1 =  $"Script_disable_hydrodynamics_{water.name}";
        string tag2 = tag1 + "ROV";
        //所有的ROV刚体包括机械手跟身子
        foreach (var rovRigbodyName in Rov.bodies.Keys.ToArray())
        {
            if (rovRigbodyName == ROV.ROVName)
            {
                AGXUtil.SetAGXCollisionsEnableByShape(Rov.bodies[rovRigbodyName], water, false,tag1, tag2, rovBox);
            }
            else
            {
                // 排除"系绳连接点"这个特殊部件
                if (rovRigbodyName != "TetherAttachment")
                {
                    AGXUtil.SetAGXCollisionsEnable(Rov.bodies[rovRigbodyName], water, false, tag1, tag2);
                }
            }
        }
    }

    public void create_hydrodynamics_geometry(Shape water, Box rovBox)
    {
        string tags1 =  $"Script_{rovBox.GetInstanceID()}_{rovBox.name}";
        string tags2 =  tags1 + "_Ignore";
        //让rovBox不与任何物体碰撞
        foreach (var shape in FindObjectsOfType<Shape>())
        {
            if (shape != water)
            {
                AGXUtil.SetAGXCollisionsEnable(rovBox, shape, false,tags1,tags2);
            }
        }

        float rov_mass = Rov.rovRigidBody.MassProperties.Mass.Value;
        float box_mass = (float)rovBox.Native.getVolume() * 1000;
        // (total_mass - rov_mass)除了ROV本地其它的重量
        //setMass设置的ROV重量使ROV处于平衡状态
        float setMass = box_mass - (Rov.mass - rov_mass);
        Debug.Log($"rov_mass {rov_mass} total_mass{Rov.mass} box_mass{box_mass} setMass---: " +setMass);
        Rov.rovRigidBody.MassProperties.Mass.Value = setMass;
    }
}


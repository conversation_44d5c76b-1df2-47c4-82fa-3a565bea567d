using UnityEngine;
using System.Reflection;

/// <summary>
/// ROV移动测试器 - 用于测试6DOF控制系统是否正常工作
/// </summary>
public class ROVMovementTester : MonoBehaviour
{
    [Header("测试设置")]
    public RovController rovController;
    public Transform testTarget;
    
    [Header("测试参数")]
    public Vector3 testMoveDistance = new Vector3(2, 1, 3);

    [Header("旋转测试参数")]
    public Vector3[] presetRotations = new Vector3[]
    {
        new Vector3(0, 0, 0),      // 正北
        new Vector3(0, 90, 0),     // 正东
        new Vector3(0, 180, 0),    // 正南
        new Vector3(0, 270, 0),    // 正西
        new Vector3(15, 45, 0),    // 俯仰+偏航
        new Vector3(-15, 135, 0)   // 仰视+偏航
    };

    [Header("纯旋转优化参数")]
    public float rotationSpeedMultiplier = 2.0f;
    public float rotationGainMultiplier = 1.5f;

    [Header("精度增强参数")]
    public bool enablePrecisionMode = true;
    public float precisionThreshold = 0.1f;
    public float precisionGainBoost = 1.5f;
    public float precisionMinSpeed = 0.08f;

    [Header("自适应控制参数")]
    public bool enableAdaptiveControl = true;
    public float distanceTrendSensitivity = 0.001f;
    public float adaptiveGainMultiplier = 2.0f;
    public float adaptiveSpeedMultiplier = 1.5f;
    public float lowSpeedThreshold = 0.005f;

    [Header("强制回归参数")]
    public bool enableForceReturn = true;
    public float forceReturnThreshold = 0.05f;
    public float forceReturnMultiplier = 3.0f;

    [Header("精确移动测试")]
    public Vector3 precisionTestTarget = new Vector3(29.534f, 9.527f, 3.340f);
    public bool enablePrecisionTest = true;
    [SerializeField] private float currentDistanceToTarget;
    [SerializeField] private bool isAtTarget;
    [SerializeField] private float maxOvershoot = 0f;
    [SerializeField] private Vector3 closestApproach;
    [SerializeField] private float minDistance = float.MaxValue;

    private int currentRotationIndex = 0;
    private bool precisionTestActive = false;
    private float testStartTime;
    private Vector3 testStartPosition;
    
    void Start()
    {
        if (rovController == null)
        {
            rovController = FindObjectOfType<RovController>();
        }
        
        if (rovController == null)
        {
            Debug.LogError("未找到RovController！");
            return;
        }
        
        Debug.Log("ROV移动测试器已启动");
        Debug.Log("按键说明:");
        Debug.Log("T - 测试相对移动");
        Debug.Log("Y - 移动到目标位置");
        Debug.Log("R - 测试目标旋转");
        Debug.Log("U - 移动到目标位置和旋转");
        Debug.Log("P - 切换分阶段控制");
        Debug.Log("数字键1-6 - 预设旋转角度测试");
        Debug.Log("M - 应用纯旋转优化参数");
        Debug.Log("N - 应用精度增强参数");
        Debug.Log("B - 应用自适应控制参数");
        Debug.Log("V - 应用强制回归参数");
        Debug.Log("L - 保存日志文件到桌面");
        Debug.Log("I - 停止移动");
        Debug.Log("O - 显示当前状态");
        Debug.Log("Z - 精确移动测试（优化版本）");
    }
    
    void Update()
    {
        if (rovController == null) return;
        
        // T键：测试相对移动
        if (Input.GetKeyDown(KeyCode.T))
        {
            TestRelativeMove();
        }
        
        // Y键：移动到目标位置
        if (Input.GetKeyDown(KeyCode.Y) && testTarget != null)
        {
            TestTargetPosition();
        }

        // R键：测试目标旋转
        if (Input.GetKeyDown(KeyCode.R) && testTarget != null)
        {
            TestTargetRotation();
        }

        // U键：移动到目标位置和旋转
        if (Input.GetKeyDown(KeyCode.U) && testTarget != null)
        {
            TestTargetPositionAndRotation();
        }

        // P键：切换分阶段控制
        if (Input.GetKeyDown(KeyCode.P))
        {
            ToggleSequentialControl();
        }
        
        // I键：停止移动
        if (Input.GetKeyDown(KeyCode.I))
        {
            rovController.StopMovement();
        }
        
        // O键：显示状态
        if (Input.GetKeyDown(KeyCode.O))
        {
            ShowStatus();
        }

        // 数字键1-6：预设旋转测试
        if (Input.GetKeyDown(KeyCode.Alpha1)) TestPresetRotation(0);
        if (Input.GetKeyDown(KeyCode.Alpha2)) TestPresetRotation(1);
        if (Input.GetKeyDown(KeyCode.Alpha3)) TestPresetRotation(2);
        if (Input.GetKeyDown(KeyCode.Alpha4)) TestPresetRotation(3);
        if (Input.GetKeyDown(KeyCode.Alpha5)) TestPresetRotation(4);
        if (Input.GetKeyDown(KeyCode.Alpha6)) TestPresetRotation(5);

        // M键：应用纯旋转优化参数
        if (Input.GetKeyDown(KeyCode.M))
        {
            ApplyRotationOptimization();
        }

        // N键：应用精度增强参数
        if (Input.GetKeyDown(KeyCode.N))
        {
            ApplyPrecisionEnhancement();
        }

        // B键：应用自适应控制参数
        if (Input.GetKeyDown(KeyCode.B))
        {
            ApplyAdaptiveControl();
        }

        // V键：应用强制回归参数
        if (Input.GetKeyDown(KeyCode.V))
        {
            ApplyForceReturn();
        }

        // L键：保存日志文件
        if (Input.GetKeyDown(KeyCode.L))
        {
            SaveLogFile();
        }

        // Z键：精确移动测试
        if (Input.GetKeyDown(KeyCode.Z))
        {
            StartPrecisionMoveTest();
        }

        // 更新精确移动测试状态
        if (precisionTestActive)
        {
            UpdatePrecisionTest();
        }
    }
    
    private void TestRelativeMove()
    {
        Debug.Log("=== 测试相对移动 ===");
        rovController.TestMove(testMoveDistance);
    }
    
    private void TestTargetPosition()
    {
        Debug.Log("=== 测试目标位置移动 ===");

        rovController.SetTargetPosition(testTarget.position);

        Debug.Log($"已设置目标位置: {testTarget.position}");
    }

    private void TestTargetRotation()
    {
        Debug.Log("=== 测试目标旋转移动 ===");

        rovController.SetTargetRotation(testTarget.eulerAngles);

        Debug.Log($"已设置目标旋转: {testTarget.eulerAngles}");
    }

    private void TestTargetPositionAndRotation()
    {
        Debug.Log("=== 测试目标位置和旋转移动 ===");

        rovController.SetTargetPositionAndRotation(testTarget.position, testTarget.eulerAngles);

        Debug.Log($"已设置目标位置和旋转 - 位置: {testTarget.position}, 旋转: {testTarget.eulerAngles}");
    }

    private void ToggleSequentialControl()
    {
        bool currentState = rovController.EnableSequentialControl;
        rovController.SetSequentialControl(!currentState);

        Debug.Log($"=== 分阶段控制已{(!currentState ? "启用" : "禁用")} ===");
        Debug.Log($"位置优先距离: {rovController.PositionPriorityDistance:F2}m");
        Debug.Log($"旋转开始距离: {rovController.RotationStartDistance:F2}m");
    }

    private void TestPresetRotation(int index)
    {
        if (index >= 0 && index < presetRotations.Length)
        {
            Vector3 targetRotation = presetRotations[index];
            rovController.SetTargetRotation(targetRotation);

            string rotationName = GetRotationName(index);
            Debug.Log($"=== 预设旋转测试 {index + 1}: {rotationName} ===");
            Debug.Log($"目标旋转: {targetRotation}");

            currentRotationIndex = index;
        }
    }

    private string GetRotationName(int index)
    {
        string[] names = { "正北", "正东", "正南", "正西", "俯仰+偏航", "仰视+偏航" };
        return index < names.Length ? names[index] : $"预设{index + 1}";
    }

    private void ApplyRotationOptimization()
    {
        Debug.Log("=== 应用纯旋转优化参数 ===");

        rovController.SetRotationOnlyParameters(rotationSpeedMultiplier, rotationGainMultiplier);

        Debug.Log($"速度倍数: {rotationSpeedMultiplier:F1}x");
        Debug.Log($"增益倍数: {rotationGainMultiplier:F1}x");
        Debug.Log("纯旋转控制现在会更快响应！");
    }

    private void ApplyPrecisionEnhancement()
    {
        Debug.Log("=== 应用精度增强参数 ===");

        rovController.SetPrecisionModeParameters(
            enablePrecisionMode,
            precisionThreshold,
            precisionGainBoost,
            precisionMinSpeed
        );

        Debug.Log($"精度模式: {(enablePrecisionMode ? "启用" : "禁用")}");
        Debug.Log($"精度阈值: {precisionThreshold:F2}m");
        Debug.Log($"增益提升: {precisionGainBoost:F1}x");
        Debug.Log($"最小速度: {precisionMinSpeed:F2}");
        Debug.Log("现在应该能够达到更高的位置精度！");
    }

    private void ApplyAdaptiveControl()
    {
        Debug.Log("=== 应用自适应控制参数 ===");

        rovController.SetAdaptiveControlParameters(
            enableAdaptiveControl,
            distanceTrendSensitivity,
            adaptiveGainMultiplier,
            adaptiveSpeedMultiplier,
            lowSpeedThreshold
        );

        Debug.Log($"自适应控制: {(enableAdaptiveControl ? "启用" : "禁用")}");
        Debug.Log($"趋势敏感度: {distanceTrendSensitivity:F4}");
        Debug.Log($"增益倍数: {adaptiveGainMultiplier:F1}x");
        Debug.Log($"速度倍数: {adaptiveSpeedMultiplier:F1}x");
        Debug.Log($"低速阈值: {lowSpeedThreshold:F3}m/s");
        Debug.Log("现在会根据距离变化趋势自动调节控制力，包括低速情况！");
    }

    private void ApplyForceReturn()
    {
        Debug.Log("=== 应用强制回归参数 ===");

        rovController.SetForceReturnParameters(
            enableForceReturn,
            forceReturnThreshold,
            forceReturnMultiplier
        );

        Debug.Log($"强制回归: {(enableForceReturn ? "启用" : "禁用")}");
        Debug.Log($"触发阈值: {forceReturnThreshold:F2}m");
        Debug.Log($"控制倍数: {forceReturnMultiplier:F1}x");
        Debug.Log("当ROV超过目标位置时会强制施加反向力回归！");
    }

    private void SaveLogFile()
    {
        Debug.Log("=== 保存日志文件 ===");

        rovController.SaveLogFile();

        Debug.Log("日志文件已保存到桌面，可以提供给开发者分析！");
    }

    private void StartPrecisionMoveTest()
    {
        if (!enablePrecisionTest)
        {
            Debug.Log("精确移动测试已禁用");
            return;
        }

        Debug.Log("=== 开始精确移动测试（优化版本） ===");

        // 重置测试状态
        precisionTestActive = true;
        testStartTime = Time.time;
        testStartPosition = rovController.Rov.RovTransform.position;
        maxOvershoot = 0f;
        minDistance = float.MaxValue;
        closestApproach = testStartPosition;

        // 使用优化的精确移动方法
        rovController.MoveToTargetPrecise(precisionTestTarget);

        Debug.Log($"起始位置: {testStartPosition:F3}");
        Debug.Log($"目标位置: {precisionTestTarget:F3}");
        Debug.Log($"初始距离: {Vector3.Distance(testStartPosition, precisionTestTarget):F3}m");
        Debug.Log("监控中... 按I键停止测试");
    }

    private void UpdatePrecisionTest()
    {
        if (rovController?.Rov == null) return;

        Vector3 currentPos = rovController.Rov.RovTransform.position;
        currentDistanceToTarget = Vector3.Distance(currentPos, precisionTestTarget);
        isAtTarget = rovController.IsAtTarget();

        // 跟踪最接近的位置
        if (currentDistanceToTarget < minDistance)
        {
            minDistance = currentDistanceToTarget;
            closestApproach = currentPos;
        }

        // 计算超调量
        if (minDistance < 0.1f && currentDistanceToTarget > minDistance)
        {
            float overshoot = currentDistanceToTarget - minDistance;
            if (overshoot > maxOvershoot)
            {
                maxOvershoot = overshoot;
            }
        }

        // 检查测试完成条件
        if (isAtTarget)
        {
            CompletePrecisionTest(true);
        }
        else if (Time.time - testStartTime > 60f) // 60秒超时
        {
            CompletePrecisionTest(false);
        }
    }

    private void CompletePrecisionTest(bool success)
    {
        precisionTestActive = false;
        float testDuration = Time.time - testStartTime;
        Vector3 finalPos = rovController.Rov.RovTransform.position;

        Debug.Log("=== 精确移动测试完成 ===");
        Debug.Log($"测试结果: {(success ? "成功到达" : "超时失败")}");
        Debug.Log($"测试时长: {testDuration:F1}秒");
        Debug.Log($"最终位置: {finalPos:F3}");
        Debug.Log($"最终距离: {currentDistanceToTarget:F4}m");
        Debug.Log($"最接近位置: {closestApproach:F3}");
        Debug.Log($"最小距离: {minDistance:F4}m");
        Debug.Log($"最大超调: {maxOvershoot:F4}m");

        // 评估测试质量
        string quality = EvaluateTestQuality();
        Debug.Log($"移动质量评估: {quality}");
    }

    private string EvaluateTestQuality()
    {
        if (maxOvershoot < 0.01f && minDistance < 0.02f)
            return "优秀 - 无超调，高精度";
        else if (maxOvershoot < 0.05f && minDistance < 0.05f)
            return "良好 - 轻微超调，精度良好";
        else if (maxOvershoot < 0.1f && minDistance < 0.1f)
            return "一般 - 有超调，精度一般";
        else
            return "需要改进 - 超调明显或精度不足";
    }
    
    private void ShowStatus()
    {
        Debug.Log("=== 当前状态 ===");
        
        if (rovController.Rov != null)
        {
            Vector3 currentPos = rovController.Rov.RovTransform.position;
            Vector3 currentRot = rovController.Rov.RovTransform.eulerAngles;
            
            Debug.Log($"当前位置: {currentPos:F2}");
            Debug.Log($"当前旋转: {currentRot:F1}°");
        }
        
        Debug.Log($"6DOF位置目标: {(rovController.targetPosition.HasValue ? rovController.targetPosition.Value.ToString("F2") : "无")}");
        Debug.Log($"6DOF旋转目标: {(rovController.targetRotation.HasValue ? rovController.targetRotation.Value.ToString("F1") : "无")}");
        Debug.Log($"分阶段控制: {(rovController.EnableSequentialControl ? "启用" : "禁用")}");
        
        string controlAxes = "";
        if (rovController.controlPositionX) controlAxes += "X ";
        if (rovController.controlPositionY) controlAxes += "Y ";
        if (rovController.controlPositionZ) controlAxes += "Z ";
        if (rovController.controlRotationX) controlAxes += "RotX ";
        if (rovController.controlRotationY) controlAxes += "RotY ";
        if (rovController.controlRotationZ) controlAxes += "RotZ ";
        
        Debug.Log($"激活的控制轴: {(string.IsNullOrEmpty(controlAxes) ? "无" : controlAxes)}");
    }
    
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 200, 450, 400));
        GUILayout.Label("ROV移动测试", GUI.skin.box);
        
        GUILayout.Label("测试按键:");
        GUILayout.Label("T - 测试相对移动");
        GUILayout.Label("Y - 移动到目标位置");
        GUILayout.Label("R - 测试目标旋转");
        GUILayout.Label("U - 移动到目标位置和旋转");
        GUILayout.Label("P - 切换分阶段控制");
        GUILayout.Label("1-6 - 预设旋转测试");
        GUILayout.Label("M - 应用纯旋转优化");
        GUILayout.Label("N - 应用精度增强");
        GUILayout.Label("B - 应用自适应控制");
        GUILayout.Label("V - 应用强制回归");
        GUILayout.Label("L - 保存日志文件");
        GUILayout.Label("I - 停止移动");
        GUILayout.Label("O - 显示状态");
        GUILayout.Label("Z - 精确移动测试");
        
        GUILayout.Space(10);

        if (rovController != null && rovController.Rov != null)
        {
            Vector3 pos = rovController.Rov.RovTransform.position;
            Vector3 rot = rovController.Rov.RovTransform.eulerAngles;
            GUILayout.Label($"当前位置: ({pos.x:F1}, {pos.y:F1}, {pos.z:F1})");
            GUILayout.Label($"当前旋转: ({rot.x:F1}°, {rot.y:F1}°, {rot.z:F1}°)");

            if (rovController.targetRotation.HasValue)
            {
                Vector3 targetRot = rovController.targetRotation.Value;
                GUILayout.Label($"目标旋转: ({targetRot.x:F1}°, {targetRot.y:F1}°, {targetRot.z:F1}°)");
            }

            GUILayout.Label($"分阶段控制: {(rovController.EnableSequentialControl ? "开" : "关")}");

            // 显示纯旋转优化状态
            bool isRotationOnly = !rovController.targetPosition.HasValue && rovController.targetRotation.HasValue;
            if (isRotationOnly)
            {
                GUILayout.Label($"纯旋转模式: 速度{rovController.RotationOnlySpeedMultiplier:F1}x, 增益{rovController.RotationOnlyGainMultiplier:F1}x", GUI.skin.box);
            }

            // 显示精度增强状态
            if (rovController.EnablePrecisionMode)
            {
                Vector3 currentPos = rovController.Rov.RovTransform.position;
                if (rovController.targetPosition.HasValue)
                {
                    float distance = Vector3.Distance(currentPos, rovController.targetPosition.Value);
                    if (distance < rovController.PrecisionModeThreshold)
                    {
                        GUILayout.Label($"精度增强模式: 距离{distance:F3}m, 增益提升{rovController.PrecisionGainBoost:F1}x", GUI.skin.box);
                    }
                }
            }

            // 显示自适应控制状态
            if (rovController.EnableAdaptiveControl && rovController.targetPosition.HasValue)
            {
                Vector3 currentPos = rovController.Rov.RovTransform.position;
                float distance = Vector3.Distance(currentPos, rovController.targetPosition.Value);
                if (distance < rovController.ArriveDistance)
                {
                    // 通过反射获取私有字段（仅用于调试显示）
                    var directionField = typeof(RovController).GetField("directionConsistency",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (directionField != null)
                    {
                        float directionConsistency = (float)directionField.GetValue(rovController);
                        string directionStr = directionConsistency > 0.1f ? "朝向目标" :
                                             directionConsistency < -0.1f ? "远离目标" : "方向不明";
                        GUILayout.Label($"自适应控制: {directionStr} ({directionConsistency:F2})", GUI.skin.box);
                    }
                }
            }
        }

        // 显示精确移动测试状态
        if (precisionTestActive)
        {
            GUILayout.Label($"精确移动测试进行中...", GUI.skin.box);
            GUILayout.Label($"目标: {precisionTestTarget:F2}");
            GUILayout.Label($"当前距离: {currentDistanceToTarget:F4}m");
            GUILayout.Label($"最小距离: {minDistance:F4}m");
            GUILayout.Label($"最大超调: {maxOvershoot:F4}m");
            GUILayout.Label($"测试时长: {(Time.time - testStartTime):F1}s");
        }
        else if (enablePrecisionTest)
        {
            GUILayout.Label($"精确移动测试目标: {precisionTestTarget:F2}");
        }

        GUILayout.Space(5);
        GUILayout.Label("预设旋转:");
        for (int i = 0; i < presetRotations.Length && i < 6; i++)
        {
            Vector3 rot = presetRotations[i];
            string name = GetRotationName(i);
            string current = (i == currentRotationIndex) ? " [当前]" : "";
            GUILayout.Label($"{i + 1}: {name} ({rot.x:F0}°,{rot.y:F0}°,{rot.z:F0}°){current}");
        }

        GUILayout.EndArea();
    }
}

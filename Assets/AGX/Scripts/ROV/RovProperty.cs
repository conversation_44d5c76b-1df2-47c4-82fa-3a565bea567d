
public class RovProperty
{
    //前进
    public static float Forward_MoveAcceleration = 1f;    // 移动加速度
    public static float Forward_MoveDeceleration = 2f;    // 移动减速度
    public static float Forward_MaxMoveSpeed = 2f;        // 最大移动速度
    
    //侧向
    public static float Horizontal_MoveAcceleration = 0.5f;    
    public static float Horizontal_MoveDeceleration = 1.0f;    
    public static float Horizontal_MaxMoveSpeed = 1.0f;
    
    //垂直
    public static float Vertical_MoveAcceleration = 0.5f;    
    public static float Vertical_MoveDeceleration = 1.0f;    
    public static float Vertical_MaxMoveSpeed = 1.0f;

    public static float MoveAcceleration(ROVMoveType type)
    {
        switch (type)
        {
            case ROVMoveType.Forward:
                return Forward_MoveAcceleration;
            case ROVMoveType.Horizontal:
                return Horizontal_MoveAcceleration;
            case ROVMoveType.Vertical:
                return Vertical_MoveAcceleration;
        }
        return 0;
    }
    
    public static float MoveDeceleration(ROVMoveType type)
    {
        switch (type)
        {
            case ROVMoveType.Forward:
                return  Forward_MoveDeceleration;
            case ROVMoveType.Horizontal:
                return  Horizontal_MoveDeceleration;
            case ROVMoveType.Vertical:
                return  Vertical_MoveDeceleration;
        }
        return 0;
    }
    
    public static float MaxMoveSpeed(ROVMoveType type)
    {
        switch (type)
        {
            case ROVMoveType.Forward:
                return  Forward_MaxMoveSpeed;
            case ROVMoveType.Horizontal:
                return  Horizontal_MaxMoveSpeed;
            case ROVMoveType.Vertical:
                return  Vertical_MaxMoveSpeed;
        }
        return 0;
    }
}

public enum ROVMoveType
{
    Forward,
    Horizontal,
    Vertical,
}
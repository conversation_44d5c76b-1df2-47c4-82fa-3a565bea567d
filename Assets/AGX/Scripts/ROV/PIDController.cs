using UnityEngine;
using UnityEngine.Rendering;

public class PIDController
{
    public float Kp = 3f, Ki = 0.02f, Kd = 0.8f;  // 优化默认参数以减少超调
    public float integral = 0f;
    public float lastError = 0f;

    public PIDController()
    {
    }

    public PIDController(float kp,float ki, float kd)
    {
        Kp = kp;
        Ki = ki;
        Kd = kd;
    }

    // 积分限制参数 - 优化以防止积分饱和
    public float integralLimit = 20f;              // 大幅降低积分限制以防止超调
    public float integralSeparationThreshold = 2f; // 降低积分分离阈值，更早停止积分累积
    public float integralDecayRate = 0.95f;        // 积分衰减率，防止积分项过大

    public float Calculate(float target, float current, float deltaTime)
    {
        float error = target - current;

        // 积分分离：只有在误差较小时才累积积分
        if (Mathf.Abs(error) < integralSeparationThreshold)
        {
            integral += error * deltaTime;
            // 积分限幅
            integral = Mathf.Clamp(integral, -integralLimit, integralLimit);
        }
        else
        {
            // 误差过大时，更快速地减少积分项
            integral *= integralDecayRate;
        }

        // 防止积分项在误差变号时继续累积（防积分饱和）
        if ((error > 0 && integral < 0) || (error < 0 && integral > 0))
        {
            integral *= 0.8f; // 误差变号时快速衰减积分项
        }

        float derivative = (error - lastError) / deltaTime;
        lastError = error;

        float output = Kp * error + Ki * integral + Kd * derivative;

        // 输出限制以防止过大的控制信号
        output = Mathf.Clamp(output, -50f, 50f);

        return output;
    }
}
using System.Collections;
using AGXUnity;
using AGXUnity.Collide;
using AGXUnity.Model;
using agxWire;
using UnityEngine;
using UnityEngine.Serialization;
using Wire = AGXUnity.Wire;

public class UnderWaterRov : MonoBehaviour
{
    public GameObject ROVMachine;
    public ROVScene rovScene;
    public Shape water;
    public Box ROVBox;
    public Wire tetherCable;
    public Wire umbilicalWire;
    public RovController RovController;
    ROV Rov;
    
    
    public Transform Propeller_ForwardRight;
    public Transform Propeller_ForwardLeft;
    public Transform Propeller_BackwardLeft;
    public Transform Propeller_BackwardRight;
    public Transform Propeller_UpRight;
    public Transform Propeller_UpLeft;
    private void Awake()
    {
        ROVBox.CollisionsEnabled = false;
        Simulation.Instance.GetInitialized<Simulation>().Gravity = Vector3.zero;
    }

    private void Start()
    {
        StartCoroutine(CreateRovWithTms());
    }

    private IEnumerator CreateRovWithTms()
    {
        yield return new WaitForFixedUpdate();
        Rov = new ROV(ROVMachine);
        RovController.SetROV(Rov);
        AddPropellers();
        rovScene.SetRovAndTms(Rov);
        #region 设置ROVBox只与水碰撞
        //除了ROVBox外，所有的ROV刚体包括机械手跟身子禁止与水碰撞
        rovScene.disable_hydrodynamics(water,ROVBox);
        //让rovBox不与除了水之外的任何物体碰撞
        rovScene.create_hydrodynamics_geometry(water,ROVBox);
        #endregion
        ROVBox.CollisionsEnabled = true;
        WireController.instance().addState(WireController.State.ALL_ENABLE);
        WireController.instance().setEnableCollisions(tetherCable.Native, tetherCable.Native, true);
        WireController.instance().setEnableCollisions(tetherCable.Native, umbilicalWire.Native, true);
        Debug.Log("umbilicalWire:" +umbilicalWire.Native.getCurrentLength());
        Simulation.Instance.GetInitialized<Simulation>().Gravity = new Vector3(0, -9.8f, 0);
    }

    private void AddPropellers()
    {
        Rov.AddPropeller(PropellerPos.Forward_Right,Propeller_ForwardRight);
        Rov.AddPropeller(PropellerPos.Forward_Left,Propeller_ForwardLeft);
        Rov.AddPropeller(PropellerPos.Backward_Left,Propeller_BackwardLeft);
        Rov.AddPropeller(PropellerPos.Backward_Right, Propeller_BackwardRight);
        Rov.AddPropeller(PropellerPos.Up_Right,Propeller_UpRight);
        Rov.AddPropeller(PropellerPos.Up_Left,Propeller_UpLeft);
    }
    
    private void OnDrawGizmos()
    {
        if (!showForces) return;
        if (Rov == null) return;
        for(int i = 0; i < Rov.propellers.Count;i++) 
        {
            var propeller = Rov.propellers[(PropellerPos)i];
            // 转换到世界坐标系
            Vector3 worldPosition = propeller.rov.transform.TransformPoint(propeller.local_position.Vec3ToVector3());
            Vector3 worldForce = propeller.rov.transform.TransformDirection(propeller.local_direction.Vec3ToVector3());
            AGXUtil.DrawArrow(worldPosition, worldForce, forceColor, i.ToString());    
        }
    }
    
    [Header("Gizmo Settings")]
    public bool showForces = true;
    public Color forceColor = Color.red;
}
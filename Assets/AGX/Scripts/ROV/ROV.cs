using System.Collections.Generic;
using System.Text;
using agx;
using AGXUnity.Model;
using agxWire;
using Unity.VisualScripting;
using UnityEngine;
using Constraint = agx.Constraint;
using RigidBody = AGXUnity.RigidBody;

public class ROV
{
    private float force = 2000;
    //不包含履带的重量
    public float mass = 0;
    public RigidBody rovRigidBody;
    public const string ROVName = "ROV";
    public Transform RovTransform => rovRigidBody.transform;
    public Dictionary<string,RigidBody> bodies = new Dictionary<string, RigidBody>();
    public Dictionary<PropellerPos, Propeller> propellers = new Dictionary<PropellerPos, Propeller>();
    //重力调节系统
    public RigidBody MassAdjust;
    public float MassAdjustDefaultMass;
    public ROV(GameObject rov)
    {
        StringBuilder sb = new StringBuilder();
        sb.AppendLine("Bodies:");
        foreach (var rigidBody in rov.GetComponentsInChildren<RigidBody>())
        {
            bodies.Add(rigidBody.name, rigidBody);
            sb.AppendLine(" -" + rigidBody.name);
            mass += rigidBody.MassProperties.Mass.Value;
        }
        rovRigidBody =   bodies[ROVName];
        MassAdjust = bodies["MassAdjust"];
        MassAdjustDefaultMass = MassAdjust.MassProperties.Mass.Value;
        Debug.Log(sb.ToString());
        Debug.Log($"rov mass: {mass}");
        WireController.instance().setEnableDynamicWireContacts(bodies[ROVName].Native, true);
    }
    
    public void AddPropeller(PropellerPos propellerPos, Transform propeller)
    {
        Transform rov = rovRigidBody.transform;
        propellers.Add(propellerPos, new Propeller(
            rov.InverseTransformPoint( propeller.position),
                rov.InverseTransformDirection(propeller.forward),
                rovRigidBody ));
    }
    
    /// <summary>
    /// 左右旋转
    /// </summary>
    /// <param name="axis"></param>
    
    public void Rotate_Dynamic(float axis)
    {
        propellers[PropellerPos.Forward_Right].apply_force(force * axis);
        propellers[PropellerPos.Forward_Left].apply_force(-force * axis);
        propellers[PropellerPos.Backward_Left].apply_force(force * axis);
        propellers[PropellerPos.Backward_Right].apply_force(-force * axis);
    }

    /// <summary>
    /// 上下
    /// </summary>
    /// <param name="axis"></param>
    public void UpDown_Dynamic(float axis)
    {
        propellers[PropellerPos.Up_Right].apply_force(force * axis);
        propellers[PropellerPos.Up_Left].apply_force(force * axis);
    }

    
    /// <summary>
    /// 前后
    /// </summary>
    /// <param name="axis"></param>
    public void ForwardBack_Dynamic(float axis)
    {
        propellers[PropellerPos.Forward_Right].apply_force(-force * axis);
        propellers[PropellerPos.Forward_Left].apply_force(-force * axis);
        propellers[PropellerPos.Backward_Left].apply_force(force * axis);
        propellers[PropellerPos.Backward_Right].apply_force(force * axis);
    }

    public void LeftRight_Dynamic(float axis)
    {
        propellers[PropellerPos.Forward_Right].apply_force(force * axis);
        propellers[PropellerPos.Forward_Left].apply_force(-force * axis);
        propellers[PropellerPos.Backward_Left].apply_force(-force * axis);
        propellers[PropellerPos.Backward_Right].apply_force(force * axis);
    }
    
    /// <summary>
    /// 调节配重
    /// </summary>
    /// <param name="currentMassWeight">
    /// 基于当前的配重调节
    /// 比如当前配重为10000，系数是-0.5~0.5,那么调节后的重量是5000~15000
    /// 低于10000时，会上浮，高于时会下降
    /// </param>
    public void onMassAdjust(float currentMassWeight)
    {   
        float calculatedMass = (currentMassWeight + 1f) * MassAdjustDefaultMass;
        MassAdjust.MassProperties.Mass.Value = calculatedMass;
    }

    private Transform KinematicMoveObj;
    public void OnEnterKinematicMove()
    {
        // rovRigidBody.MotionControl = agx.RigidBody.MotionControl.KINEMATICS;
        if (KinematicMoveObj == null)
        {
            GameObject obj = new GameObject("KinematicMoveObj");
            KinematicMoveObj = obj.transform;
        }
        KinematicMoveObj.position = rovRigidBody.transform.position;
        KinematicMoveObj.rotation = rovRigidBody.transform.rotation;
    }

    public void OnExitKinematicMove()
    {
        // rovRigidBody.MotionControl = agx.RigidBody.MotionControl.DYNAMICS;
    }

   

    PIDController torquePid = new PIDController(5.0f, 0.1f,0.05f);
    //前倾后仰
    public void AddTorque(float torqueForce)
    {
        // 计算当前Pitch角度
        float pitchAngle = rovRigidBody.transform.eulerAngles.x;
        if (pitchAngle > 180f) pitchAngle -= 360f;
        if (pitchAngle < -180f) pitchAngle += 360f;
        float correctionTorque = torquePid.Calculate(0f, pitchAngle, Time.fixedDeltaTime);
        // Debug.Log("correctionTorque:" + correctionTorque);
        rovRigidBody.Native.addTorque(new agx.Vec3(correctionTorque * torqueForce, 0, 0));
    }
    
}

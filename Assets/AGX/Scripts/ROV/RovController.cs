using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.Serialization;
using AGXUnity;

public enum ControlPhase
{
    PositionOnly,       // 只控制位置
    PositionPrimary,    // 位置为主，旋转为辅
    Balanced,           // 位置和旋转平衡控制
    RotationPrimary,    // 旋转为主，位置为辅
    RotationOnly        // 只控制旋转
}

public enum AdaptiveHeadingMode
{
    OnTarget,           // 已朝向目标
    FineAdjustment,     // 精细调整
    AdaptiveRotation,   // 自适应旋转（边移动边旋转）
    FastRotation        // 快速旋转（优先旋转）
}

public class RovController : MonoBehaviour
{
    public ROV Rov { get;private set; }

    public void SetROV(ROV rov)
    {
        Rov = rov;
    }

    private void Start()
    {
        Simulation.Instance.StepCallbacks.PreStepForward += onPreStepForward;

        // 初始化日志文件
        InitializeLogFile();
    }

    private void OnDestroy()
    {
        // 关闭日志文件
        CloseLogFile();
    }

    private float moveForwardback;
    private float moveRotate;
    private float moveLeftRight;
    private float moveUpDown;

    private void updateKeyValue()
    {
        //UpArrow DownArrow
        moveForwardback = Input.GetAxis("Vertical");
        //LeftArrow RightArrow
        if(Input.GetKey( KeyCode.LeftShift ))
        {
            moveRotate = Input.GetAxis("Horizontal");
            moveLeftRight = 0;
        }
        else
        {
            moveLeftRight= Input.GetAxis("Horizontal");
            moveRotate = 0;     
        }
        //PageUp PageDown
        moveUpDown = Input.GetAxis("updown");   
    }

    #region 动力学移动
    private int index;

    // 6DOF PID 控制器 - 优化参数以减少超调
    [Header("6DOF PID控制器")]
    private PIDController pidPositionX = new PIDController(3.0f, 0.02f, 0.8f);  // 位置X (左右) - 降低Kp，减少Ki，增加Kd
    private PIDController pidPositionY = new PIDController(3.0f, 0.02f, 0.8f);  // 位置Y (上下)
    private PIDController pidPositionZ = new PIDController(3.0f, 0.02f, 0.8f);  // 位置Z (前后)
    private PIDController pidRotationX = new PIDController(2.0f, 0.01f, 0.5f);  // 旋转X (俯仰)
    private PIDController pidRotationY = new PIDController(2.0f, 0.01f, 0.5f);  // 旋转Y (偏航)
    private PIDController pidRotationZ = new PIDController(2.0f, 0.01f, 0.5f);  // 旋转Z (翻滚)

    // 允许的最大 PID 输出（将根据该值映射到轴输入 -1~1）
    private const float MaxPidOutput = 8f;  // 降低最大输出以减少超调

    // 距离阈值配置 - 优化阈值以提高精度
    // 距离阈值配置 - 优化阈值以提高精度和速度
    [Header("PID控制参数")]
    public float ArriveDistance = 0.08f;         // 最终到达距离 - 减小以提前进入精细控制
    public float SlowDownDistance = 1.5f;        // 开始减速的距离 - 减小以更快接近
    public float PrecisionDistance = 0.3f;       // 精细控制距离 - 减小以更快切换
    public float FinalStopDistance = 0.01f;      // 最终停止距离 - 设为目标精度

    // 速度控制参数 - 优化速度控制以减少调整时间
    public float MaxApproachSpeed = 2.0f;        // 最大接近速度 - 提高以更快接近
    public float MinControlSpeed = 0.08f;        // 最小控制速度 - 提高以减少调整时间
    public float FinalStopSpeed = 0.02f;         // 最终停止速度阈值 - 适当提高

    // 防震荡参数 - 平衡阻尼和响应速度
    [Header("防震荡控制")]
    public float VelocityDampingFactor = 3.0f;   // 速度阻尼系数 - 适当降低以提高响应
    public float MaxVelocityInArriveZone = 0.05f; // 目标区域内最大允许速度 - 提高以减少调整时间
    
    // 分阶段控制参数
    [Header("分阶段控制")]
    public float PositionPriorityDistance = 0.5f;  // 位置优先控制距离
    public float RotationStartDistance = 0.3f;     // 开始旋转控制的距离
    public bool EnableSequentialControl = true;    // 启用分阶段控制

    // 纯旋转控制参数
    [Header("纯旋转控制优化")]
    public float RotationOnlySpeedMultiplier = 2.0f;  // 纯旋转时的速度倍数
    public float RotationOnlyGainMultiplier = 1.5f;   // 纯旋转时的增益倍数

    // 精度增强参数 - 优化精度模式
    [Header("精度增强控制")]
    public bool EnablePrecisionMode = true;           
    public float PrecisionModeThreshold = 0.05f;       // 精度模式阈值 - 减小以更早启用
    public float PrecisionGainBoost = 2.0f;           // 精度模式增益 - 提高以更快收敛
    public float PrecisionMinSpeed = 0.12f;           // 精度模式最小速度 - 提高以减少调整时间
    
    // 自适应控制参数
    [Header("自适应控制")]
    public bool EnableAdaptiveControl = true;         // 启用自适应控制
    public float DistanceTrendSensitivity = 0.001f;   // 距离变化趋势敏感度
    public float AdaptiveGainMultiplier = 2.0f;       // 距离增大时的增益倍数
    public float AdaptiveSpeedMultiplier = 1.5f;      // 距离增大时的速度倍数
    public float LowSpeedThreshold = 0.005f;          // 低速阈值，低于此值使用简化判断

    // 强制回归控制 - 更积极的回归策略
    [Header("强制回归控制")]
    public bool EnableForceReturn = true;
    public float ForceReturnThreshold = 0.03f;        // 强制回归距离阈值 - 减小以更早触发
    public float ForceReturnMultiplier = 4.0f;        // 强制回归倍数 - 提高以更快回归

    // 自适应朝向控制参数
    [Header("自适应朝向控制")]
    public bool EnableAdaptiveHeading = true;         // 启用自适应朝向控制
    public float FastRotationAngleThreshold = 15f;   // 快速旋转角度阈值（度）
    public float AdaptiveRotationAngleThreshold = 45f; // 自适应旋转角度阈值（度）
    public float FastRotationSpeedMultiplier = 3.0f; // 快速旋转时的速度倍数
    public float AdaptiveRotationSpeedMultiplier = 1.5f; // 自适应旋转时的速度倍数
    public float HeadingToleranceAngle = 2f;          // 朝向容差角度（度）
    public bool EnableMoveWhileRotating = true;      // 启用边移动边旋转
    public float HeadingRotationStrength = 2.0f;     // 朝向旋转强度倍数
    
    // 日志记录
    [Header("日志记录")]
    public bool EnableFileLogging = true;             // 启用文件日志
    const string LogFileName = "ROV_Movement_Log.txt";   // 日志文件名（含扩展名）
    public bool EnableRotationLogging = true;        // 启用旋转相关日志

    // 6DOF控制配置
    [Header("6DOF控制配置")]
    [Tooltip("控制哪些轴向，可以选择性启用")]
    public bool controlPositionX = true;   // 控制位置X (左右)
    public bool controlPositionY = true;   // 控制位置Y (上下)
    public bool controlPositionZ = true;   // 控制位置Z (前后)
    public bool controlRotationX = false;  // 控制旋转X (俯仰)
    public bool controlRotationY = false;  // 控制旋转Y (偏航)
    public bool controlRotationZ = false;  // 控制旋转Z (翻滚)

    [Header("目标设置")]
    [Tooltip("目标位置，如果不设置则使用MoveTarget的位置")]
    public Vector3? targetPosition = null;
    [Tooltip("目标旋转（欧拉角），如果不设置则使用MoveTarget的旋转")]
    public Vector3? targetRotation = null;

    // 记录上一帧的位置和旋转用于计算速度和角速度
    private Vector3 lastPosition;
    private Vector3 currentVelocity;
    private Vector3 lastRotation;
    private Vector3 currentAngularVelocity;

    // 自适应控制状态跟踪
    private float lastDistance = -1f;                 // 上一帧的距离
    private Vector3 lastPositionError = Vector3.zero; // 上一帧的位置误差向量
    private float distanceTrend = 0f;                 // 距离变化趋势（正值=远离，负值=接近）
    private float directionConsistency = 0f;          // 方向一致性（正值=朝向目标，负值=远离目标）
    private bool isInArriveZone = false;              // 是否在到达区域内
    private float arriveZoneEntryTime = 0f;           // 进入到达区域的时间

    // 停滞检测
    private float stagnationCheckTime = 0f;           // 停滞检测时间
    private float lastStagnationDistance = -1f;      // 上次检测停滞时的距离
    private bool isStagnant = false;                  // 是否处于停滞状态

    // 自适应朝向控制状态
    private float currentHeadingError = 0f;           // 当前朝向误差（度）
    private bool isInFastRotationMode = false;        // 是否处于快速旋转模式
    private bool shouldFaceTarget = false;            // 是否应该朝向目标
    private Vector3? headingTargetPosition = null;    // 朝向目标位置

    // 日志记录相关
    private StreamWriter logWriter;                   // 日志文件写入器
    private string logFilePath;                       // 日志文件路径
    private bool logFileInitialized = false;          // 日志文件是否已初始化
    private void FixedUpdate()
    {
        if(Rov == null)
            return;
        updateKeyValue();
        onMassAdjust();
        index++;
        // if(index % 2 == 1)//降低施加力的频率
        //     return;
        if (targetPosition.HasValue || targetRotation.HasValue)
        {
            MoveDynamic6DOF();
        }
        else
        {
            MoveDynamic();
        }
    }

    // MoveTarget已移除，只使用Set6DOFTarget方式
 
    public float TorqueForce = 2000;
    private void MoveDynamic()
    {
        Rov.UpDown_Dynamic(RovProperty.MaxMoveSpeed(ROVMoveType.Vertical) * moveUpDown);
        Rov.ForwardBack_Dynamic(-RovProperty.MaxMoveSpeed(ROVMoveType.Forward) * moveForwardback);
        Rov.LeftRight_Dynamic(-RovProperty.MaxMoveSpeed( ROVMoveType.Horizontal) * moveLeftRight);
        Rov.Rotate_Dynamic(-0.5f * moveRotate);
        // if (moveForwardback != 0)
        {
            Rov.AddTorque(TorqueForce);
        }

    }

    bool isArrive = false;
    
    // ==================== 6DOF PID控制 ====================
    private void MoveDynamic6DOF()
    {
        // 1. 获取当前状态
        Vector3 currentPosition = Rov.RovTransform.position;
        Vector3 currentRotation = Rov.RovTransform.eulerAngles;

        // 计算速度和角速度
        if (lastPosition != Vector3.zero)
        {
            currentVelocity = (currentPosition - lastPosition) / Time.fixedDeltaTime;
            currentAngularVelocity = (currentRotation - lastRotation) / Time.fixedDeltaTime;

            // 处理角度跳跃（-180到180度的跳跃）
            for (int i = 0; i < 3; i++)
            {
                if (currentAngularVelocity[i] > 180f) currentAngularVelocity[i] -= 360f;
                if (currentAngularVelocity[i] < -180f) currentAngularVelocity[i] += 360f;
            }
        }
        lastPosition = currentPosition;
        lastRotation = currentRotation;

        // 2. 确定目标值（只使用6DOF目标）
        Vector3 targetPos = targetPosition ?? currentPosition;
        Vector3 targetRot = targetRotation ?? currentRotation;

        // 3. 计算位置和旋转误差
        Vector3 positionError = targetPos - currentPosition;
        Vector3 rotationError = CalculateRotationError(targetRot, currentRotation);

        float totalDistance = positionError.magnitude;
        float totalRotationError = rotationError.magnitude;

        // 4. 更新距离趋势跟踪（使用位置误差向量）
        UpdateDistanceTrend(totalDistance, positionError);

        // 5. 检测停滞状态
        CheckStagnation(totalDistance);

        // 4. 检查是否到达目标（但继续控制以保持位置）
        bool positionReached = totalDistance < FinalStopDistance && currentVelocity.magnitude < FinalStopSpeed;
        bool rotationReached = totalRotationError < 5f && currentAngularVelocity.magnitude < 10f; // 5度误差，10度/秒角速度

        if (positionReached && rotationReached)
        {
            isArrive = true;
            if (index % 60 == 0)
            {
                Debug.Log($"6DOF已到达目标！位置误差: {totalDistance:F4}m, 旋转误差: {totalRotationError:F2}°");
            }
        }
        else
        {
            isArrive = false;
        }

        // 5. 计算动态控制参数
        float speedFactor = CalculateSpeedFactor(totalDistance);
        float pidGainFactor = CalculatePIDGainFactor(totalDistance);

        // 6. 自适应朝向控制逻辑
        AdaptiveHeadingMode headingMode = AdaptiveHeadingMode.OnTarget;
        float headingRotationOutput = 0f;
        bool shouldPrioritizeRotation = false;

        if (EnableAdaptiveHeading && shouldFaceTarget && headingTargetPosition.HasValue)
        {
            // 计算朝向误差
            currentHeadingError = CalculateHeadingError(headingTargetPosition.Value);
            headingMode = DetermineHeadingMode(currentHeadingError);

            // 根据朝向模式决定控制策略
            switch (headingMode)
            {
                case AdaptiveHeadingMode.FastRotation:
                    shouldPrioritizeRotation = true;
                    isInFastRotationMode = true;
                    headingRotationOutput = CalculateHeadingRotationOutput(currentHeadingError, FastRotationSpeedMultiplier);
                    LogRotationEvent("快速旋转模式", $"误差: {currentHeadingError:F1}°, 输出: {headingRotationOutput:F3}");
                    break;

                case AdaptiveHeadingMode.AdaptiveRotation:
                    isInFastRotationMode = false;
                    headingRotationOutput = CalculateHeadingRotationOutput(currentHeadingError, AdaptiveRotationSpeedMultiplier);
                    LogRotationEvent("自适应旋转模式", $"误差: {currentHeadingError:F1}°, 输出: {headingRotationOutput:F3}");
                    break;

                case AdaptiveHeadingMode.FineAdjustment:
                    isInFastRotationMode = false;
                    headingRotationOutput = CalculateHeadingRotationOutput(currentHeadingError, 1.0f);
                    LogRotationEvent("精细调整模式", $"误差: {currentHeadingError:F1}°, 输出: {headingRotationOutput:F3}");
                    break;

                case AdaptiveHeadingMode.OnTarget:
                    if (isInFastRotationMode) // 从其他模式切换到OnTarget时记录
                    {
                        LogRotationEvent("到达目标朝向", $"误差: {currentHeadingError:F1}°");
                    }
                    isInFastRotationMode = false;
                    headingRotationOutput = 0f;
                    break;
            }
        }

        // 7. 分阶段控制策略（考虑朝向控制）
        ControlPhase currentPhase = DetermineControlPhase(totalDistance, totalRotationError);

        float outputX = 0, outputY = 0, outputZ = 0;
        float outputRotX = 0, outputRotY = 0, outputRotZ = 0;

        // 根据控制阶段和朝向控制决定哪些轴参与控制
        bool enablePositionControl = ShouldEnablePositionControl(currentPhase, totalDistance) &&
                                   (!shouldPrioritizeRotation || EnableMoveWhileRotating);
        bool enableRotationControl = ShouldEnableRotationControl(currentPhase, totalDistance);

        // 位置控制（应用精度增强、自适应控制和强制回归）
        if (enablePositionControl)
        {
            float precisionGainFactor = ApplyPrecisionMode(pidGainFactor, totalDistance);
            float adaptiveGainFactor = ApplyAdaptiveControl(precisionGainFactor, totalDistance);
            float finalGainFactor = ApplyForceReturn(adaptiveGainFactor, totalDistance, positionError);

            if (controlPositionX)
                outputX = CalculateImprovedPIDOutput(pidPositionX, positionError.x, -currentVelocity.x, finalGainFactor);
            if (controlPositionY)
                outputY = CalculateImprovedPIDOutput(pidPositionY, positionError.y, -currentVelocity.y, finalGainFactor);
            if (controlPositionZ)
                outputZ = CalculateImprovedPIDOutput(pidPositionZ, positionError.z, -currentVelocity.z, finalGainFactor);
        }

        // 旋转控制（根据距离调整增益，集成朝向控制）
        if (enableRotationControl)
        {
            float rotationGainFactor = CalculateRotationGainFactor(totalDistance, pidGainFactor);

            if (controlRotationX)
                outputRotX = CalculateImprovedPIDOutput(pidRotationX, rotationError.x, -currentAngularVelocity.x, rotationGainFactor);
            if (controlRotationY)
            {
                // 如果启用了朝向控制，优先使用朝向控制输出
                if (EnableAdaptiveHeading && shouldFaceTarget && headingTargetPosition.HasValue)
                {
                    // 直接使用朝向控制输出，不再乘以MaxPidOutput（避免过度缩放）
                    outputRotY = headingRotationOutput * MaxPidOutput * 0.5f; // 适度缩放以匹配PID输出范围
                }
                else
                {
                    outputRotY = CalculateImprovedPIDOutput(pidRotationY, rotationError.y, -currentAngularVelocity.y, rotationGainFactor);
                }
            }
            if (controlRotationZ)
                outputRotZ = CalculateImprovedPIDOutput(pidRotationZ, rotationError.z, -currentAngularVelocity.z, rotationGainFactor);
        }

        // 7. 转换到ROV局部坐标系（用于推进器控制）
        Vector3 worldForce = new Vector3(outputX, outputY, outputZ);
        Vector3 localForce = Rov.RovTransform.InverseTransformDirection(worldForce);

        // 计算旋转速度因子（纯旋转时更快）
        float rotationSpeedFactor = (!targetPosition.HasValue && targetRotation.HasValue) ?
            speedFactor * RotationOnlySpeedMultiplier : speedFactor;

        // 应用自适应速度控制
        float adaptiveSpeedFactor = ApplyAdaptiveSpeedControl(speedFactor, totalDistance);
        rotationSpeedFactor = ApplyAdaptiveSpeedControl(rotationSpeedFactor, totalDistance);

        // 应用速度限制和防震荡检查（使用自适应速度因子）
        float axisX = Mathf.Clamp(localForce.x / MaxPidOutput, -1f, 1f) * adaptiveSpeedFactor;      // 左右
        float axisY = Mathf.Clamp(localForce.y / MaxPidOutput, -1f, 1f) * adaptiveSpeedFactor;      // 上下
        float axisZ = Mathf.Clamp(localForce.z / MaxPidOutput, -1f, 1f) * adaptiveSpeedFactor;      // 前后

        // 旋转输出（纯旋转时使用更高的速度因子）
        float axisRotX = Mathf.Clamp(outputRotX / MaxPidOutput, -1f, 1f) * rotationSpeedFactor;     // 俯仰
        float axisRotY = Mathf.Clamp(outputRotY / MaxPidOutput, -1f, 1f) * rotationSpeedFactor;     // 偏航
        float axisRotZ = Mathf.Clamp(outputRotZ / MaxPidOutput, -1f, 1f) * rotationSpeedFactor;     // 翻滚

        // 在目标区域内进行额外的速度检查，防止震荡
        if (totalDistance < ArriveDistance)
        {
            if (currentVelocity.magnitude > MaxVelocityInArriveZone)
            {
                float velocityReduction = 0.6f;
                axisX *= velocityReduction;
                axisY *= velocityReduction;
                axisZ *= velocityReduction;
                axisRotX *= velocityReduction;
                axisRotY *= velocityReduction;
                axisRotZ *= velocityReduction;
            }
        }

        // 8. 执行控制
        // 位置控制 - 分别处理每个轴
        float finalAxisX = controlPositionX ? axisX : 0f;
        float finalAxisY = controlPositionY ? axisY : 0f;
        float finalAxisZ = controlPositionZ ? axisZ : 0f;
        float finalAxisRotY = controlRotationY ? axisRotY : 0f;

        // 应用控制输出
        Rov.LeftRight_Dynamic(RovProperty.MaxMoveSpeed(ROVMoveType.Horizontal) * finalAxisX);
        Rov.UpDown_Dynamic(-RovProperty.MaxMoveSpeed(ROVMoveType.Vertical) * finalAxisY);
        Rov.ForwardBack_Dynamic(RovProperty.MaxMoveSpeed(ROVMoveType.Forward) * finalAxisZ);

        // 旋转控制（根据朝向控制状态调整旋转强度）
        float rotationMultiplier = EnableAdaptiveHeading && shouldFaceTarget ? HeadingRotationStrength : 0.5f;
        Rov.Rotate_Dynamic(finalAxisRotY * rotationMultiplier);

        // 姿态稳定（总是启用以保持稳定）
        Rov.AddTorque(TorqueForce);

        // 10. 详细调试信息（每10帧输出一次以便实时监控）
        if (index % 10 == 0)
        {
            string controlPhase = GetControlPhase(totalDistance);
            string activeAxes = GetActiveAxesInfo();
            string currentPhaseStr = currentPhase.ToString();
            bool isRotationOnly = !targetPosition.HasValue && targetRotation.HasValue;

            // 构建日志信息（包含朝向控制信息）
            string logInfo = BuildLogInfo(index, targetPos, currentPosition, positionError, totalDistance,
                currentVelocity, controlPhase, currentPhaseStr, enablePositionControl, enableRotationControl,
                outputX, outputY, outputZ, speedFactor, adaptiveSpeedFactor,
                finalAxisX, finalAxisY, finalAxisZ, finalAxisRotY, isRotationOnly,
                headingMode, currentHeadingError, headingRotationOutput);

            // 输出到控制台
            Debug.Log(logInfo);

            // 写入日志文件
            WriteToLogFile(logInfo);
        }
    }

    private bool IsLockROV;

    public Vector3? LockRovPos;
    public Vector3? LockRovRotate;
    //锁定ROV
    private void LockRov(Vector3 position)
    {
        IsLockROV = true;
        LockRovPos = position;
    }

    //停止锁定ROV
    private void UnLockRov()
    {
        IsLockROV = false;
    }

    /// <summary>
    /// 根据距离计算速度因子，距离越近速度越慢，支持目标范围内的精细调整 - 优化以减少超调
    /// </summary>
    private float CalculateSpeedFactor(float distance)
    {
        if (distance > SlowDownDistance)
        {
            return 0.8f; // 远距离时也适当降低速度以减少冲击
        }
        else if (distance > PrecisionDistance)
        {
            // 线性减速阶段 - 更早开始减速
            return Mathf.Lerp(0.2f, 0.8f, (distance - PrecisionDistance) / (SlowDownDistance - PrecisionDistance));
        }
        else if (distance > ArriveDistance)
        {
            // 精细控制阶段 - 进一步降低速度
            return Mathf.Lerp(0.08f, 0.2f, (distance - ArriveDistance) / (PrecisionDistance - ArriveDistance));
        }
        else
        {
            // 目标范围内的超精细调整阶段 - 大幅降低速度以防止超调
            float minFactor = 0.03f;  // 大幅降低最小速度因子以防止超调
            float maxFactor = 0.08f;  // 降低边界速度因子

            // 在ArriveDistance范围内，使用更激进的减速曲线
            float normalizedDistance = distance / ArriveDistance; // 0-1范围

            // 使用更激进的减速曲线，防止超调
            float smoothFactor = Mathf.Pow(normalizedDistance, 0.8f); // 更激进的减速曲线
            float speedFactor = Mathf.Lerp(minFactor, maxFactor, smoothFactor);

            return speedFactor;
        }
    }

    /// <summary>
    /// 根据距离计算PID增益因子，距离越近增益越小以避免震荡，但保持精细控制能力
    /// </summary>
    private float CalculatePIDGainFactor(float distance)
    {
        if (distance > SlowDownDistance)
        {
            return 1.0f; // 远距离时正常增益
        }
        else if (distance > PrecisionDistance)
        {
            // 逐渐降低增益
            return Mathf.Lerp(0.6f, 1.0f, (distance - PrecisionDistance) / (SlowDownDistance - PrecisionDistance));
        }
        else if (distance > ArriveDistance)
        {
            // 精细控制阶段，提高增益以确保能够接近目标
            return Mathf.Lerp(0.8f, 1.2f, (distance - ArriveDistance) / (PrecisionDistance - ArriveDistance));
        }
        else
        {
            // 目标范围内，大幅提高增益以确保能够到达目标
            float minGain = 1.0f;  // 大幅提高最小增益
            float maxGain = 1.5f;  // 大幅提高边界增益

            float normalizedDistance = distance / ArriveDistance; // 0-1范围

            // 使用线性插值，保持高增益
            return Mathf.Lerp(minGain, maxGain, normalizedDistance);
        }
    }

    /// <summary>
    /// 改进的PID计算，包含强化的速度反馈和动态增益 - 优化以减少超调
    /// </summary>
    private float CalculateImprovedPIDOutput(PIDController pid, float positionError, float velocityError, float gainFactor)
    {
        // 临时调整PID参数
        float originalKp = pid.Kp;
        float originalKi = pid.Ki;
        float originalKd = pid.Kd;

        // 应用增益因子 - 进一步减少积分项以防止超调
        pid.Kp = originalKp * gainFactor;
        pid.Ki = originalKi * gainFactor * 0.1f; // 大幅减少积分项以防止超调
        pid.Kd = originalKd * gainFactor;

        // 计算位置PID输出
        float positionOutput = pid.Calculate(0, positionError, Time.fixedDeltaTime);

        // 强化速度阻尼，特别是在目标区域内 - 增强阻尼效果
        float distance = Mathf.Abs(positionError);
        float dampingMultiplier = distance < ArriveDistance ? VelocityDampingFactor * 3.0f : VelocityDampingFactor;
        float velocityDamping = velocityError * dampingMultiplier * gainFactor;

        // 在目标区域内大幅限制最大输出，防止过冲
        float totalOutput = positionOutput + velocityDamping;
        if (distance < ArriveDistance)
        {
            float maxOutputInArriveZone = 2.0f; // 大幅降低目标区域内的最大输出限制以防止超调
            totalOutput = Mathf.Clamp(totalOutput, -maxOutputInArriveZone, maxOutputInArriveZone);
        }

        // 添加死区控制，在极小误差时停止输出
        if (distance < FinalStopDistance && Mathf.Abs(velocityError) < FinalStopSpeed)
        {
            totalOutput *= 0.1f; // 在死区内大幅减少输出
        }

        // 恢复原始PID参数
        pid.Kp = originalKp;
        pid.Ki = originalKi;
        pid.Kd = originalKd;

        return totalOutput;
    }

    /// <summary>
    /// 计算旋转误差，处理角度跳跃问题
    /// </summary>
    private Vector3 CalculateRotationError(Vector3 targetRotation, Vector3 currentRotation)
    {
        Vector3 error = targetRotation - currentRotation;

        // 处理角度跳跃（选择最短路径）
        for (int i = 0; i < 3; i++)
        {
            while (error[i] > 180f) error[i] -= 360f;
            while (error[i] < -180f) error[i] += 360f;
        }

        return error;
    }

    /// <summary>
    /// 计算ROV当前朝向与目标位置的角度差
    /// </summary>
    /// <param name="targetPosition">目标位置</param>
    /// <returns>角度差（度），正值表示需要顺时针旋转</returns>
    private float CalculateHeadingError(Vector3 targetPosition)
    {
        Vector3 currentPosition = Rov.RovTransform.position;
        Vector3 currentForward = Rov.RovTransform.forward;

        // 计算从当前位置到目标位置的方向向量
        Vector3 directionToTarget = (targetPosition - currentPosition).normalized;

        // 只考虑水平面上的朝向（忽略Y轴）
        Vector3 currentForwardHorizontal = new Vector3(currentForward.x, 0, currentForward.z).normalized;
        Vector3 directionToTargetHorizontal = new Vector3(directionToTarget.x, 0, directionToTarget.z).normalized;

        // 计算角度差
        float angle = Vector3.SignedAngle(currentForwardHorizontal, directionToTargetHorizontal, Vector3.up);

        return angle;
    }

    /// <summary>
    /// 根据朝向误差确定旋转控制策略
    /// </summary>
    /// <param name="headingError">朝向误差（度）</param>
    /// <returns>旋转控制策略</returns>
    private AdaptiveHeadingMode DetermineHeadingMode(float headingError)
    {
        float absError = Mathf.Abs(headingError);

        if (absError <= HeadingToleranceAngle)
        {
            return AdaptiveHeadingMode.OnTarget;
        }
        else if (absError >= AdaptiveRotationAngleThreshold)
        {
            return AdaptiveHeadingMode.FastRotation;
        }
        else if (absError >= FastRotationAngleThreshold)
        {
            return AdaptiveHeadingMode.AdaptiveRotation;
        }
        else
        {
            return AdaptiveHeadingMode.FineAdjustment;
        }
    }

    /// <summary>
    /// 计算朝向旋转输出
    /// </summary>
    /// <param name="headingError">朝向误差（度）</param>
    /// <param name="speedMultiplier">速度倍数</param>
    /// <returns>旋转输出值</returns>
    private float CalculateHeadingRotationOutput(float headingError, float speedMultiplier)
    {
        // 使用更合理的角度归一化，让小角度也有足够的输出
        float normalizedError = headingError / 90f; // 归一化到90度范围，让输出更明显

        // 应用速度倍数
        float output = normalizedError * speedMultiplier;

        // 限制输出范围，但允许更大的输出值
        output = Mathf.Clamp(output, -2f, 2f); // 增加输出范围到-2到2

        return output;
    }

    /// <summary>
    /// 获取当前激活的控制轴信息
    /// </summary>
    private string GetActiveAxesInfo()
    {
        List<string> activeAxes = new List<string>();
        if (controlPositionX) activeAxes.Add("PosX");
        if (controlPositionY) activeAxes.Add("PosY");
        if (controlPositionZ) activeAxes.Add("PosZ");
        if (controlRotationX) activeAxes.Add("RotX");
        if (controlRotationY) activeAxes.Add("RotY");
        if (controlRotationZ) activeAxes.Add("RotZ");

        return activeAxes.Count > 0 ? string.Join(", ", activeAxes) : "无";
    }

    /// <summary>
    /// 重置PID控制状态，在设置新目标时调用
    /// </summary>
    private void ResetPIDState()
    {
        isArrive = false;
        lastPosition = Vector3.zero;
        lastRotation = Vector3.zero;
        currentVelocity = Vector3.zero;
        currentAngularVelocity = Vector3.zero;

        // 重置自适应控制状态
        lastDistance = -1f;
        lastPositionError = Vector3.zero;
        distanceTrend = 0f;
        directionConsistency = 0f;
        isInArriveZone = false;
        arriveZoneEntryTime = 0f;

        // 重置停滞检测状态
        stagnationCheckTime = Time.time;
        lastStagnationDistance = -1f;
        isStagnant = false;

        // 重置朝向控制状态
        currentHeadingError = 0f;
        isInFastRotationMode = false;

        // 重置所有PID控制器的积分项
        pidPositionX.integral = pidPositionX.lastError = 0f;
        pidPositionY.integral = pidPositionY.lastError = 0f;
        pidPositionZ.integral = pidPositionZ.lastError = 0f;
        pidRotationX.integral = pidRotationX.lastError = 0f;
        pidRotationY.integral = pidRotationY.lastError = 0f;
        pidRotationZ.integral = pidRotationZ.lastError = 0f;

        Debug.Log("6DOF PID状态已重置，开始向新目标移动");
    }

    /// <summary>
    /// 公共方法：手动重置PID状态
    /// </summary>
    public void ResetPIDManually()
    {
        ResetPIDState();
    }

    /// <summary>
    /// 设置所有轴的PID参数
    /// </summary>
    public void SetPIDParameters(float kp, float ki, float kd)
    {
        pidPositionX.Kp = pidPositionY.Kp = pidPositionZ.Kp = kp;
        pidPositionX.Ki = pidPositionY.Ki = pidPositionZ.Ki = ki;
        pidPositionX.Kd = pidPositionY.Kd = pidPositionZ.Kd = kd;

        pidRotationX.Kp = pidRotationY.Kp = pidRotationZ.Kp = kp;
        pidRotationX.Ki = pidRotationY.Ki = pidRotationZ.Ki = ki;
        pidRotationX.Kd = pidRotationY.Kd = pidRotationZ.Kd = kd;

        Debug.Log($"6DOF PID参数已更新: Kp={kp}, Ki={ki}, Kd={kd}");
    }

    /// <summary>
    /// 设置6DOF目标位置和旋转
    /// </summary>
    public void Set6DOFTarget(Vector3? position = null, Vector3? rotation = null)
    {
        targetPosition = position;
        targetRotation = rotation;

        // 只有在设置了有效目标时才重置PID状态
        if (position.HasValue || rotation.HasValue)
        {
            ResetPIDState();
            Debug.Log($"6DOF目标已设置 - 位置: {position}, 旋转: {rotation}");
        }
        else
        {
            Debug.Log("6DOF目标已清除");
        }
    }

    /// <summary>
    /// 设置控制轴配置
    /// </summary>
    public void SetControlAxes(bool posX = true, bool posY = true, bool posZ = true,
                              bool rotX = false, bool rotY = false, bool rotZ = false)
    {
        controlPositionX = posX;
        controlPositionY = posY;
        controlPositionZ = posZ;
        controlRotationX = rotX;
        controlRotationY = rotY;
        controlRotationZ = rotZ;

        Debug.Log($"控制轴已设置: Pos({posX},{posY},{posZ}) Rot({rotX},{rotY},{rotZ})");
    }

    /// <summary>
    /// 设置目标位置（只控制位置，不控制旋转）
    /// </summary>
    public void SetTargetPosition(Vector3 position)
    {
        SetControlAxes(posX: true, posY: true, posZ: true, rotX: false, rotY: false, rotZ: false);
        Set6DOFTarget(position: position, rotation: null);
    }

    /// <summary>
    /// 设置目标旋转（只控制旋转，不控制位置）
    /// </summary>
    public void SetTargetRotation(Vector3 rotation)
    {
        SetControlAxes(posX: false, posY: false, posZ: false, rotX: false, rotY: true, rotZ: false);
        Set6DOFTarget(position: null, rotation: rotation);
    }

    /// <summary>
    /// 设置目标位置和旋转（完整6DOF控制）
    /// </summary>
    public void SetTargetPositionAndRotation(Vector3 position, Vector3 rotation)
    {
        SetControlAxes(posX: true, posY: true, posZ: true, rotX: false, rotY: true, rotZ: false);
        Set6DOFTarget(position: position, rotation: rotation);
    }

    /// <summary>
    /// 设置朝向目标位置（启用自适应朝向控制）
    /// </summary>
    /// <param name="position">目标位置</param>
    /// <param name="enableHeadingControl">是否启用朝向控制</param>
    public void SetTargetPositionWithHeading(Vector3 position, bool enableHeadingControl = true)
    {
        SetControlAxes(posX: true, posY: true, posZ: true, rotX: false, rotY: true, rotZ: false);
        Set6DOFTarget(position: position, rotation: null);

        // 设置朝向控制
        shouldFaceTarget = enableHeadingControl;
        headingTargetPosition = enableHeadingControl ? position : null;

        // 记录朝向目标设置日志
        if (enableHeadingControl)
        {
            Vector3 currentPos = Rov.RovTransform.position;
            float initialHeadingError = CalculateHeadingError(position);
            LogRotationEvent("设置朝向目标",
                $"目标: {position:F2}, 当前: {currentPos:F2}, 初始误差: {initialHeadingError:F1}°");
        }

        Debug.Log($"设置朝向目标位置: {position:F3}, 朝向控制: {(enableHeadingControl ? "启用" : "禁用")}");
    }

    /// <summary>
    /// 启用/禁用朝向目标控制
    /// </summary>
    /// <param name="enable">是否启用</param>
    /// <param name="targetPos">朝向目标位置（可选）</param>
    public void SetHeadingControl(bool enable, Vector3? targetPos = null)
    {
        shouldFaceTarget = enable;
        if (enable && targetPos.HasValue)
        {
            headingTargetPosition = targetPos.Value;
            float initialHeadingError = CalculateHeadingError(targetPos.Value);
            LogRotationEvent("启用朝向控制", $"目标: {targetPos.Value:F2}, 初始误差: {initialHeadingError:F1}°");
        }
        else if (!enable)
        {
            LogRotationEvent("禁用朝向控制", $"最终误差: {currentHeadingError:F1}°");
            headingTargetPosition = null;
        }

        Debug.Log($"朝向控制: {(enable ? "启用" : "禁用")}, 目标: {headingTargetPosition}");
    }

    /// <summary>
    /// 移动到相对位置
    /// </summary>
    public void MoveRelative(Vector3 relativePosition)
    {
        Vector3 currentPos = Rov.RovTransform.position;
        Vector3 newTarget = currentPos + relativePosition;
        SetTargetPosition(newTarget);
        Debug.Log($"相对移动: 从 {currentPos:F2} 到 {newTarget:F2}");
    }

    /// <summary>
    /// 停止移动（清除所有目标）
    /// </summary>
    public void StopMovement()
    {
        Set6DOFTarget(position: null, rotation: null);
        Debug.Log("已停止移动");
    }

    /// <summary>
    /// 启用/禁用分阶段控制
    /// </summary>
    public void SetSequentialControl(bool enable)
    {
        EnableSequentialControl = enable;
        Debug.Log($"分阶段控制: {(enable ? "启用" : "禁用")}");
    }

    /// <summary>
    /// 设置分阶段控制参数
    /// </summary>
    public void SetSequentialControlParameters(float positionPriorityDistance = 0.5f, float rotationStartDistance = 0.3f)
    {
        PositionPriorityDistance = positionPriorityDistance;
        RotationStartDistance = rotationStartDistance;
        Debug.Log($"分阶段控制参数已更新: 位置优先距离={positionPriorityDistance:F2}m, 旋转开始距离={rotationStartDistance:F2}m");
    }

    /// <summary>
    /// 设置纯旋转控制优化参数
    /// </summary>
    public void SetRotationOnlyParameters(float speedMultiplier = 2.0f, float gainMultiplier = 1.5f)
    {
        RotationOnlySpeedMultiplier = speedMultiplier;
        RotationOnlyGainMultiplier = gainMultiplier;
        Debug.Log($"纯旋转优化参数已更新: 速度倍数={speedMultiplier:F1}x, 增益倍数={gainMultiplier:F1}x");
    }

    /// <summary>
    /// 应用精度增强模式
    /// </summary>
    private float ApplyPrecisionMode(float basePidGainFactor, float distance)
    {
        if (!EnablePrecisionMode || distance > PrecisionModeThreshold)
        {
            return basePidGainFactor;
        }

        // 在精度模式下，距离越小增益越大
        float precisionFactor = 1.0f + (PrecisionGainBoost - 1.0f) * (1.0f - distance / PrecisionModeThreshold);
        return basePidGainFactor * precisionFactor;
    }

    /// <summary>
    /// 更新距离变化趋势（使用位置误差向量的方向信息）
    /// </summary>
    private void UpdateDistanceTrend(float currentDistance, Vector3 currentPositionError)
    {
        // 检查是否进入/离开到达区域
        bool wasInArriveZone = isInArriveZone;
        isInArriveZone = currentDistance < ArriveDistance;

        if (isInArriveZone && !wasInArriveZone)
        {
            arriveZoneEntryTime = Time.time;
            Debug.Log($"进入到达区域，距离: {currentDistance:F4}m");
        }

        // 计算距离变化趋势
        if (lastDistance >= 0f)
        {
            float distanceChange = currentDistance - lastDistance;
            // 使用指数移动平均来平滑趋势
            distanceTrend = distanceTrend * 0.8f + distanceChange * 0.2f;
        }

        // 计算方向一致性（更重要的指标）
        if (lastPositionError != Vector3.zero && currentPositionError != Vector3.zero)
        {
            // 计算位置误差向量的变化
            Vector3 errorChange = currentPositionError - lastPositionError;

            // 如果误差向量在减小，说明在接近目标（方向一致性为正）
            // 如果误差向量在增大，说明在远离目标（方向一致性为负）
            float errorMagnitudeChange = currentPositionError.magnitude - lastPositionError.magnitude;

            // 计算速度方向与误差方向的关系（降低速度阈值以适应精细控制）
            if (currentVelocity.magnitude > 0.002f && currentPositionError.magnitude > 0.001f)
            {
                // 速度方向与位置误差方向的点积
                // 正值表示朝向目标移动，负值表示远离目标移动
                float velocityErrorDot = Vector3.Dot(currentVelocity.normalized, -currentPositionError.normalized);

                // 结合误差变化和速度方向来判断方向一致性
                float directionFactor = velocityErrorDot * (1.0f - errorMagnitudeChange / currentPositionError.magnitude);

                // 使用指数移动平均平滑方向一致性
                directionConsistency = directionConsistency * 0.7f + directionFactor * 0.3f;
            }
            else if (currentVelocity.magnitude <= 0.002f && lastPositionError != Vector3.zero)
            {
                // 如果误差在增大，说明在远离目标
                if (Mathf.Abs(errorMagnitudeChange) > 0.0005f)
                {
                    float directionFactor = errorMagnitudeChange > 0 ? -0.5f : 0.5f;
                    directionConsistency = directionConsistency * 0.8f + directionFactor * 0.2f;
                }
            }
        }

        lastDistance = currentDistance;
        lastPositionError = currentPositionError;
    }

    /// <summary>
    /// 检测停滞状态 - 当ROV在某个距离停滞不前时触发强制推进
    /// </summary>
    private void CheckStagnation(float currentDistance)
    {
        // 只在距离目标较近但未到达时检测停滞
        if (currentDistance > ArriveDistance || currentDistance < FinalStopDistance)
        {
            // 重置停滞检测
            stagnationCheckTime = Time.time;
            lastStagnationDistance = currentDistance;
            isStagnant = false;
            return;
        }

        // 检查是否开始新的停滞检测周期
        if (Time.time - stagnationCheckTime > 3f) // 每3秒检测一次
        {
            float distanceChange = Mathf.Abs(currentDistance - lastStagnationDistance);

            // 如果3秒内距离变化小于0.02m，认为是停滞
            if (distanceChange < 0.02f && currentVelocity.magnitude < 0.01f)
            {
                isStagnant = true;
                if (index % 30 == 0)
                {
                    Debug.Log($"检测到停滞状态! 距离: {currentDistance:F4}m, 3秒内变化: {distanceChange:F4}m, 速度: {currentVelocity.magnitude:F4}m/s");
                }
            }
            else
            {
                isStagnant = false;
            }

            // 更新检测基准
            stagnationCheckTime = Time.time;
            lastStagnationDistance = currentDistance;
        }
    }

    /// <summary>
    /// 应用自适应控制增益（基于方向一致性和简化的低速判断）
    /// </summary>
    private float ApplyAdaptiveControl(float basePidGainFactor, float distance)
    {
        if (!EnableAdaptiveControl || !isInArriveZone)
        {
            return basePidGainFactor;
        }

        float adaptiveFactor = 1.0f;

        // 低速或静止情况下的简化判断
        if (currentVelocity.magnitude < LowSpeedThreshold)
        {
            // 主要基于距离趋势判断
            if (distanceTrend > DistanceTrendSensitivity * 0.5f) // 降低阈值
            {
                float trendFactor = Mathf.Clamp01(distanceTrend / (DistanceTrendSensitivity * 5f));
                adaptiveFactor += (AdaptiveGainMultiplier - 1.0f) * trendFactor;

                if (index % 30 == 0)
                {
                    Debug.Log($"低速自适应控制触发 - 距离趋势: {distanceTrend:F4}, 增益倍数: {adaptiveFactor:F2}x");
                }
            }
        }
        else
        {
            // 正常速度下基于方向一致性判断
            if (directionConsistency < -0.1f)
            {
                float directionFactor = Mathf.Clamp01(-directionConsistency);
                adaptiveFactor += (AdaptiveGainMultiplier - 1.0f) * directionFactor;
            }
            // 距离趋势作为辅助判断
            else if (distanceTrend > DistanceTrendSensitivity)
            {
                float trendFactor = Mathf.Clamp01(distanceTrend / (DistanceTrendSensitivity * 10f));
                adaptiveFactor += (AdaptiveGainMultiplier - 1.0f) * trendFactor * 0.5f;
            }
        }

        return basePidGainFactor * adaptiveFactor;
    }

    /// <summary>
    /// 应用自适应速度控制（基于方向一致性和简化的低速判断）
    /// </summary>
    private float ApplyAdaptiveSpeedControl(float baseSpeedFactor, float distance)
    {
        if (!EnableAdaptiveControl || !isInArriveZone)
        {
            return baseSpeedFactor;
        }

        float adaptiveFactor = 1.0f;

        // 低速或静止情况下的简化判断
        if (currentVelocity.magnitude < LowSpeedThreshold)
        {
            // 主要基于距离趋势判断
            if (distanceTrend > DistanceTrendSensitivity * 0.5f)
            {
                float trendFactor = Mathf.Clamp01(distanceTrend / (DistanceTrendSensitivity * 3f));
                adaptiveFactor += (AdaptiveSpeedMultiplier - 1.0f) * trendFactor;
            }
        }
        else
        {
            // 正常速度下基于方向一致性判断
            if (directionConsistency < -0.1f)
            {
                float directionFactor = Mathf.Clamp01(-directionConsistency);
                adaptiveFactor += (AdaptiveSpeedMultiplier - 1.0f) * directionFactor;
            }
            else if (distanceTrend > DistanceTrendSensitivity)
            {
                float trendFactor = Mathf.Clamp01(distanceTrend / (DistanceTrendSensitivity * 5f));
                adaptiveFactor += (AdaptiveSpeedMultiplier - 1.0f) * trendFactor * 0.5f;
            }
        }

        return baseSpeedFactor * adaptiveFactor;
    }

    /// <summary>
    /// 设置精度增强参数
    /// </summary>
    public void SetPrecisionModeParameters(bool enable = true, float threshold = 0.1f, float gainBoost = 1.5f, float minSpeed = 0.08f)
    {
        EnablePrecisionMode = enable;
        PrecisionModeThreshold = threshold;
        PrecisionGainBoost = gainBoost;
        PrecisionMinSpeed = minSpeed;
        Debug.Log($"精度增强模式已{(enable ? "启用" : "禁用")}: 阈值={threshold:F2}m, 增益提升={gainBoost:F1}x, 最小速度={minSpeed:F2}");
    }

    /// <summary>
    /// 强制回归控制 - 当ROV明显超过目标位置时强制施加反向力
    /// </summary>
    private float ApplyForceReturn(float baseGainFactor, float distance, Vector3 positionError)
    {
        if (!EnableForceReturn || !isInArriveZone || distance > ForceReturnThreshold)
        {
            return baseGainFactor;
        }

        // 检查是否需要强制回归
        bool needForceReturn = false;
        string forceReason = "";

        // 条件1: 距离趋势持续增大
        if (distanceTrend > DistanceTrendSensitivity * 2f)
        {
            needForceReturn = true;
            forceReason = "距离持续增大";
        }

        // 条件2: 方向一致性强烈表明远离目标
        if (directionConsistency < -0.3f)
        {
            needForceReturn = true;
            forceReason = "强烈远离目标";
        }

        // 条件3: 在到达区域内停留时间过长且距离没有改善
        if (Time.time - arriveZoneEntryTime > 5f && distance > FinalStopDistance * 2f)
        {
            needForceReturn = true;
            forceReason = "长时间无改善";
        }

        // 条件4: 检测到停滞状态（关键！）
        if (isStagnant && distance > FinalStopDistance)
        {
            needForceReturn = true;
            forceReason = "停滞状态";
        }

        if (needForceReturn)
        {
            float forceMultiplier = ForceReturnMultiplier;

            // 根据距离调整强制回归的强度
            float distanceFactor = Mathf.Clamp01(distance / ForceReturnThreshold);
            forceMultiplier = 1f + (ForceReturnMultiplier - 1f) * distanceFactor;

            if (index % 30 == 0)
            {
                Debug.Log($"强制回归触发! 原因: {forceReason}, 距离: {distance:F4}m, 增益倍数: {forceMultiplier:F2}x");
            }

            return baseGainFactor * forceMultiplier;
        }

        return baseGainFactor;
    }

    /// <summary>
    /// 设置自适应控制参数
    /// </summary>
    public void SetAdaptiveControlParameters(bool enable = true, float sensitivity = 0.001f, float gainMultiplier = 2.0f, float speedMultiplier = 1.5f, float lowSpeedThreshold = 0.005f)
    {
        EnableAdaptiveControl = enable;
        DistanceTrendSensitivity = sensitivity;
        AdaptiveGainMultiplier = gainMultiplier;
        AdaptiveSpeedMultiplier = speedMultiplier;
        LowSpeedThreshold = lowSpeedThreshold;
        Debug.Log($"自适应控制已{(enable ? "启用" : "禁用")}: 敏感度={sensitivity:F4}, 增益倍数={gainMultiplier:F1}x, 速度倍数={speedMultiplier:F1}x, 低速阈值={lowSpeedThreshold:F3}");
    }

    /// <summary>
    /// 设置强制回归参数
    /// </summary>
    public void SetForceReturnParameters(bool enable = true, float threshold = 0.05f, float multiplier = 3.0f)
    {
        EnableForceReturn = enable;
        ForceReturnThreshold = threshold;
        ForceReturnMultiplier = multiplier;
        Debug.Log($"强制回归已{(enable ? "启用" : "禁用")}: 阈值={threshold:F2}m, 控制倍数={multiplier:F1}x");
    }

    /// <summary>
    /// 设置自适应朝向控制参数
    /// </summary>
    public void SetAdaptiveHeadingParameters(bool enable = true, float fastRotationThreshold = 15f,
        float adaptiveRotationThreshold = 45f, float fastSpeedMultiplier = 3.0f,
        float adaptiveSpeedMultiplier = 1.5f, float toleranceAngle = 2f, bool enableMoveWhileRotating = true,
        float rotationStrength = 2.0f)
    {
        EnableAdaptiveHeading = enable;
        FastRotationAngleThreshold = fastRotationThreshold;
        AdaptiveRotationAngleThreshold = adaptiveRotationThreshold;
        FastRotationSpeedMultiplier = fastSpeedMultiplier;
        AdaptiveRotationSpeedMultiplier = adaptiveSpeedMultiplier;
        HeadingToleranceAngle = toleranceAngle;
        EnableMoveWhileRotating = enableMoveWhileRotating;
        HeadingRotationStrength = rotationStrength;

        Debug.Log($"自适应朝向控制已{(enable ? "启用" : "禁用")}:");
        Debug.Log($"  快速旋转阈值: {fastRotationThreshold:F1}°");
        Debug.Log($"  自适应旋转阈值: {adaptiveRotationThreshold:F1}°");
        Debug.Log($"  快速旋转速度倍数: {fastSpeedMultiplier:F1}x");
        Debug.Log($"  自适应旋转速度倍数: {adaptiveSpeedMultiplier:F1}x");
        Debug.Log($"  朝向容差: {toleranceAngle:F1}°");
        Debug.Log($"  边移动边旋转: {(enableMoveWhileRotating ? "启用" : "禁用")}");
        Debug.Log($"  旋转强度: {rotationStrength:F1}x");
    }

    /// <summary>
    /// 简单测试方法：移动到相对位置
    /// </summary>
    public void TestMove(Vector3 relativePosition)
    {
        MoveRelative(relativePosition);
    }

    /// <summary>
    /// 精确移动到目标位置 - 优化版本，减少超调
    /// </summary>
    public void MoveToTargetPrecise(Vector3 targetPos)
    {
        // 重置PID状态以确保干净的开始
        ResetPIDState();

        // 设置优化的控制参数以减少超调
        SetPrecisionModeParameters(true, 0.2f, 1.2f, 0.05f);
        SetAdaptiveControlParameters(true, 0.0005f, 1.5f, 1.2f, 0.003f);
        SetForceReturnParameters(true, 0.08f, 2.0f);

        // 设置目标位置
        SetTargetPosition(targetPos);

        Debug.Log($"开始精确移动到目标位置: {targetPos:F3}，当前位置: {Rov.RovTransform.position:F3}");
    }

    /// <summary>
    /// 检查是否已到达目标位置
    /// </summary>
    public bool IsAtTarget()
    {
        if (!targetPosition.HasValue) return true;

        Vector3 currentPos = Rov.RovTransform.position;
        float distance = Vector3.Distance(currentPos, targetPosition.Value);
        float speed = currentVelocity.magnitude;

        return distance < FinalStopDistance && speed < FinalStopSpeed;
    }

    /// <summary>
    /// 获取到目标的距离
    /// </summary>
    public float GetDistanceToTarget()
    {
        if (!targetPosition.HasValue) return 0f;

        Vector3 currentPos = Rov.RovTransform.position;
        return Vector3.Distance(currentPos, targetPosition.Value);
    }

    /// <summary>
    /// 获取当前朝向误差
    /// </summary>
    /// <returns>朝向误差（度）</returns>
    public float GetCurrentHeadingError()
    {
        return currentHeadingError;
    }

    /// <summary>
    /// 获取当前朝向控制模式
    /// </summary>
    /// <returns>朝向控制模式</returns>
    public AdaptiveHeadingMode GetCurrentHeadingMode()
    {
        if (!EnableAdaptiveHeading || !shouldFaceTarget || !headingTargetPosition.HasValue)
            return AdaptiveHeadingMode.OnTarget;

        return DetermineHeadingMode(currentHeadingError);
    }

    /// <summary>
    /// 测试朝向控制 - 移动到指定位置并朝向该位置
    /// </summary>
    /// <param name="targetPos">目标位置</param>
    public void TestAdaptiveHeading(Vector3 targetPos)
    {
        // 启用自适应朝向控制（增加旋转强度）
        SetAdaptiveHeadingParameters(true, 15f, 45f, 3.0f, 1.5f, 2f, true, 2.0f);

        // 设置朝向目标位置
        SetTargetPositionWithHeading(targetPos, true);

        Debug.Log($"开始自适应朝向测试 - 目标位置: {targetPos:F3}");
        Debug.Log($"当前位置: {Rov.RovTransform.position:F3}");
        Debug.Log($"当前朝向: {Rov.RovTransform.eulerAngles.y:F1}°");
    }

    /// <summary>
    /// 确定当前控制阶段
    /// </summary>
    private ControlPhase DetermineControlPhase(float positionDistance, float rotationError)
    {
        if (!EnableSequentialControl)
        {
            return ControlPhase.Balanced; // 禁用分阶段控制时，使用平衡模式
        }

        // 如果只设置了位置目标
        if (targetPosition.HasValue && !targetRotation.HasValue)
            return ControlPhase.PositionOnly;

        // 如果只设置了旋转目标
        if (!targetPosition.HasValue && targetRotation.HasValue)
            return ControlPhase.RotationOnly;

        // 同时设置了位置和旋转目标时的分阶段策略
        if (positionDistance > PositionPriorityDistance)
        {
            return ControlPhase.PositionOnly; // 远距离时只控制位置
        }
        else if (positionDistance > RotationStartDistance)
        {
            return ControlPhase.PositionPrimary; // 中距离时位置为主
        }
        else if (positionDistance > ArriveDistance)
        {
            return ControlPhase.Balanced; // 近距离时平衡控制
        }
        else
        {
            // 到达位置目标后，根据旋转误差决定
            if (rotationError > 10f) // 10度
                return ControlPhase.RotationPrimary;
            else
                return ControlPhase.Balanced;
        }
    }

    /// <summary>
    /// 判断是否应该启用位置控制
    /// </summary>
    private bool ShouldEnablePositionControl(ControlPhase phase, float distance)
    {
        switch (phase)
        {
            case ControlPhase.PositionOnly:
            case ControlPhase.PositionPrimary:
            case ControlPhase.Balanced:
                return true;
            case ControlPhase.RotationPrimary:
                return distance > FinalStopDistance; // 只有在未到达最终位置时才继续位置控制
            case ControlPhase.RotationOnly:
                return false;
            default:
                return true;
        }
    }

    /// <summary>
    /// 判断是否应该启用旋转控制
    /// </summary>
    private bool ShouldEnableRotationControl(ControlPhase phase, float distance)
    {
        switch (phase)
        {
            case ControlPhase.PositionOnly:
                return false;
            case ControlPhase.PositionPrimary:
                return distance < RotationStartDistance; // 开始接近时才启用旋转
            case ControlPhase.Balanced:
            case ControlPhase.RotationPrimary:
            case ControlPhase.RotationOnly:
                return true;
            default:
                return false;
        }
    }

    /// <summary>
    /// 计算旋转控制的增益因子（根据距离和控制模式调整）
    /// </summary>
    private float CalculateRotationGainFactor(float distance, float basePidGainFactor)
    {
        // 如果是纯旋转控制（没有位置目标），使用更高的增益
        if (!targetPosition.HasValue && targetRotation.HasValue)
        {
            return basePidGainFactor * RotationOnlyGainMultiplier;
        }

        // 有位置目标时的分阶段增益控制
        if (distance > RotationStartDistance)
        {
            // 距离较远时，旋转增益很小
            return basePidGainFactor * 0.3f;
        }
        else if (distance > ArriveDistance)
        {
            // 中等距离时，逐渐增加旋转增益
            float factor = 1.0f - (distance - ArriveDistance) / (RotationStartDistance - ArriveDistance);
            return basePidGainFactor * Mathf.Lerp(0.3f, 0.8f, factor);
        }
        else
        {
            // 接近目标时，旋转增益较高
            return basePidGainFactor * 0.8f;
        }
    }

    /// <summary>
    /// 获取当前控制阶段的描述
    /// </summary>
    private string GetControlPhase(float distance)
    {
        if (distance > SlowDownDistance)
            return "全速接近";
        else if (distance > PrecisionDistance)
            return "线性减速";
        else if (distance > ArriveDistance)
            return "精细控制";
        else if (distance > FinalStopDistance)
            return "超精细调整";
        else
            return "最终定位";
    }

    #endregion
    #region 非动力学移动
    
    private Vector3 _velocity;              // 三维移动速度（y:上下，z:前后）
    
    [Header("Kinematic Keyboard")]
    [Header("旋转参数")]
    [NonSerialized]public float rotateAcceleration = 10f;  // 旋转加速度（度/秒²）
    [NonSerialized]public float rotateDeceleration = 20f; // 旋转减速度
    [NonSerialized]public float maxRotateSpeed = 20f;     // 最大旋转速度（度/秒）
    private float _rotationVelocity;       // 旋转速度（绕Y轴）
    #region Kinematic_MoveKeyboard
 

    private void onPreStepForward()
    {
        if (IsLockROV)
        {
            if (LockRovPos.HasValue)
            {
                Rov.rovRigidBody.transform.position = LockRovPos.Value;
            }

            if (LockRovRotate.HasValue)
            {
                Rov.rovRigidBody.transform.rotation =  Quaternion.Euler(LockRovRotate.Value);
            }
            Rov.rovRigidBody.SyncNativeTransform();
        }
    }
    
    // 处理移动轴向
    void HandleMovementAxis_Keyboard(float input, ref float currentSpeed, float deltaTime, ROVMoveType moveType)
    {
        float targetSpeed = input * RovProperty.MaxMoveSpeed(moveType);
        float acceleration = input != 0 ?  RovProperty.MoveAcceleration(moveType) : RovProperty.MoveDeceleration(moveType);
        currentSpeed = Mathf.MoveTowards(currentSpeed, targetSpeed, acceleration * deltaTime);
    }

    // 处理旋转轴向
    void HandleRotationAxis_Keyboard(float input, float deltaTime)
    {
        float targetSpeed = input * maxRotateSpeed;
        float acceleration = input != 0 ? rotateAcceleration : rotateDeceleration;
        _rotationVelocity = Mathf.MoveTowards(_rotationVelocity, targetSpeed, acceleration * deltaTime);
        // Debug.Log("input" + input + " rotationVelocity:" + _rotationVelocity);
    }

    #endregion
    
    #endregion
    #region 配重调节
    private float RangeMin = -0.5f;
    public float RangeMax = 2.0f;
    public float targetMassAjustWeight = 0;
    public float massAjustChangeRate = 0.05f;
    //(-1~1)
    private float currentMassWeight = 0f;
    private void onMassAdjust()
    {
        if (Input.GetKeyDown(KeyCode.N))
        {
            targetMassAjustWeight -= 0.1f;
        }else if (Input.GetKeyDown(KeyCode.M))
        {
            targetMassAjustWeight += 0.1f;
        }
        if(!Mathf.Approximately(currentMassWeight, targetMassAjustWeight))
        {
            SetTargetMassAjustWeight();
        }
    }
    
    /// <summary>
    /// 设置目标配重
    /// </summary>
    /// <param name="weight"> 目标配重系数 </param>
    /// 基于当前的配重调节
    /// 比如当前配重为10000，系数是-0.5~0.5,那么调节后的重量是5000~15000
    /// 低于10000时，会上浮，高于时会下降
    public void SetTargetMassAjustWeight()
    {
        float calweight = Mathf.Clamp(targetMassAjustWeight, RangeMin, RangeMax);
        // 平滑过渡当前比例
        currentMassWeight = Mathf.MoveTowards(currentMassWeight, calweight, massAjustChangeRate * Time.deltaTime);
        Rov.onMassAdjust(currentMassWeight);
    }
    #endregion

    #region 日志记录方法

    /// <summary>
    /// 初始化日志文件
    /// </summary>
    private void InitializeLogFile()
    {
        if (!EnableFileLogging) return;

        try
        {
            // 创建日志文件路径（Assets目录）
            string assetsPath = Application.dataPath;
            logFilePath = Path.Combine(assetsPath, LogFileName);

            // 如果文件已存在，删除旧日志
            if (File.Exists(logFilePath))
            {
                File.Delete(logFilePath);
                Debug.Log("已删除旧的日志文件");
            }

            // 创建日志文件写入器
            logWriter = new StreamWriter(logFilePath, false, System.Text.Encoding.UTF8);
            logFileInitialized = true;

            // 写入日志头部信息
            WriteLogHeader();

            Debug.Log($"ROV移动日志文件已创建: {logFilePath}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"创建日志文件失败: {e.Message}");
            EnableFileLogging = false;
        }
    }

    /// <summary>
    /// 写入日志头部信息
    /// </summary>
    private void WriteLogHeader()
    {
        if (!logFileInitialized) return;

        logWriter.WriteLine("=".PadRight(80, '='));
        logWriter.WriteLine($"ROV移动控制日志");
        logWriter.WriteLine($"开始时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        logWriter.WriteLine($"Unity版本: {Application.unityVersion}");
        logWriter.WriteLine($"场景名称: {UnityEngine.SceneManagement.SceneManager.GetActiveScene().name}");
        logWriter.WriteLine($"旋转日志: {(EnableRotationLogging ? "启用" : "禁用")}");
        logWriter.WriteLine($"自适应朝向控制: {(EnableAdaptiveHeading ? "启用" : "禁用")}");
        logWriter.WriteLine($"旋转强度倍数: {HeadingRotationStrength:F1}x");
        logWriter.WriteLine("=".PadRight(80, '='));
        logWriter.WriteLine();

        // 写入日志格式说明
        logWriter.WriteLine("日志格式说明:");
        logWriter.WriteLine("- 旋转事件: [时间戳] 旋转事件: 事件类型 - 详细信息 | 当前朝向 | 朝向误差");
        logWriter.WriteLine("- 控制状态: 每10帧输出一次详细的控制状态信息");
        logWriter.WriteLine("- 朝向控制模式: OnTarget(已对准), FineAdjustment(精细调整), AdaptiveRotation(自适应旋转), FastRotation(快速旋转)");
        logWriter.WriteLine();
        logWriter.Flush();
    }

    /// <summary>
    /// 构建日志信息
    /// </summary>
    private string BuildLogInfo(int frameIndex, Vector3 targetPos, Vector3 currentPos, Vector3 posError,
        float totalDist, Vector3 velocity, string phase, string mode, bool posControl, bool rotControl,
        float outX, float outY, float outZ, float speedFac, float adaptiveSpeedFac,
        float axisX, float axisY, float axisZ, float axisRotY, bool isRotOnly,
        AdaptiveHeadingMode headingMode, float headingError, float headingOutput)
    {
        var sb = new System.Text.StringBuilder();

        sb.AppendLine($"=== ROV控制状态 Frame {frameIndex} Time {Time.time:F2}s ===");
        sb.AppendLine($"目标位置: {targetPos:F3}, 当前位置: {currentPos:F3}");
        sb.AppendLine($"位置误差向量: {posError:F3}, 距离: {totalDist:F4}m");
        sb.AppendLine($"当前速度: {velocity:F4}, 速度大小: {velocity.magnitude:F4}m/s");
        sb.AppendLine($"控制阶段: {phase}, 控制模式: {mode}");
        sb.AppendLine($"位置控制: {posControl}, 旋转控制: {rotControl}");
        sb.AppendLine($"PID输出 - X:{outX:F3}, Y:{outY:F3}, Z:{outZ:F3}");
        sb.AppendLine($"速度因子: {speedFac:F3}, 自适应速度因子: {adaptiveSpeedFac:F3}");
        sb.AppendLine($"最终轴输出: X={axisX:F3}, Y={axisY:F3}, Z={axisZ:F3}, RotY={axisRotY:F3}");

        // 添加旋转相关的详细日志
        if (EnableRotationLogging)
        {
            Vector3 currentRotation = Rov.RovTransform.eulerAngles;
            sb.AppendLine($"--- 旋转状态详情 ---");
            sb.AppendLine($"当前欧拉角: {currentRotation:F2}");
            sb.AppendLine($"当前角速度: {currentAngularVelocity:F3}");
            sb.AppendLine($"旋转输出倍数: {(EnableAdaptiveHeading && shouldFaceTarget ? HeadingRotationStrength : 0.5f):F2}");
            sb.AppendLine($"最终旋转力: {axisRotY * (EnableAdaptiveHeading && shouldFaceTarget ? HeadingRotationStrength : 0.5f):F3}");
        }

        // 添加朝向控制信息
        if (EnableAdaptiveHeading && shouldFaceTarget)
        {
            sb.AppendLine($"--- 朝向控制详情 ---");
            sb.AppendLine($"朝向控制模式: {headingMode}");
            sb.AppendLine($"朝向误差: {headingError:F2}°");
            sb.AppendLine($"朝向控制输出: {headingOutput:F3}");
            sb.AppendLine($"快速旋转模式: {(isInFastRotationMode ? "是" : "否")}");
            sb.AppendLine($"旋转强度倍数: {HeadingRotationStrength:F1}x");
            sb.AppendLine($"目标朝向位置: {headingTargetPosition:F3}");

            // 计算朝向相关的详细信息
            if (headingTargetPosition.HasValue)
            {
                Vector3 currentPos2 = Rov.RovTransform.position;
                Vector3 currentForward = Rov.RovTransform.forward;
                Vector3 directionToTarget = (headingTargetPosition.Value - currentPos2).normalized;

                sb.AppendLine($"当前朝向: {currentForward:F3}");
                sb.AppendLine($"目标方向: {directionToTarget:F3}");
                sb.AppendLine($"方向点积: {Vector3.Dot(currentForward, directionToTarget):F3}");
            }

            // 显示最终旋转输出值
            float finalRotOutput = axisRotY * HeadingRotationStrength;
            sb.AppendLine($"最终旋转输出: {finalRotOutput:F3}");
        }

        if (EnableAdaptiveControl && isInArriveZone)
        {
            string trendStr = distanceTrend > DistanceTrendSensitivity ? "远离" :
                             distanceTrend < -DistanceTrendSensitivity ? "接近" : "稳定";
            string directionStr = directionConsistency > 0.1f ? "朝向目标" :
                                 directionConsistency < -0.1f ? "远离目标" : "方向不明";
            sb.AppendLine($"自适应控制 - 方向一致性: {directionStr} ({directionConsistency:F3}), 距离趋势: {trendStr} ({distanceTrend:F4})");

            // 检查自适应控制是否真正生效
            float baseGain = CalculatePIDGainFactor(totalDist);
            float adaptiveGain = ApplyAdaptiveControl(baseGain, totalDist);
            sb.AppendLine($"增益对比 - 基础: {baseGain:F3}, 自适应后: {adaptiveGain:F3}, 提升: {(adaptiveGain/baseGain):F2}x");

            if (lastPositionError != Vector3.zero)
            {
                float errorChange = totalDist - lastPositionError.magnitude;
                sb.AppendLine($"误差变化: {errorChange:F4}m, 上帧误差: {lastPositionError.magnitude:F4}m");
            }
        }

        if (isRotOnly)
        {
            sb.AppendLine($"纯旋转模式 - 速度倍数: {RotationOnlySpeedMultiplier:F1}x, 增益倍数: {RotationOnlyGainMultiplier:F1}x");
        }

        sb.AppendLine("=".PadRight(50, '='));

        return sb.ToString();
    }

    /// <summary>
    /// 写入日志文件
    /// </summary>
    private void WriteToLogFile(string logInfo)
    {
        if (!EnableFileLogging || !logFileInitialized || logWriter == null) return;

        try
        {
            logWriter.WriteLine(logInfo);
            logWriter.Flush(); // 立即刷新到文件
        }
        catch (System.Exception e)
        {
            Debug.LogError($"写入日志文件失败: {e.Message}");
        }
    }

    /// <summary>
    /// 关闭日志文件
    /// </summary>
    private void CloseLogFile()
    {
        if (logWriter != null)
        {
            try
            {
                logWriter.WriteLine();
                logWriter.WriteLine("=".PadRight(80, '='));
                logWriter.WriteLine($"日志结束时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                logWriter.WriteLine("=".PadRight(80, '='));
                logWriter.Close();
                logWriter.Dispose();
                logWriter = null;

                Debug.Log($"ROV移动日志文件已保存: {logFilePath}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"关闭日志文件失败: {e.Message}");
            }
        }
    }

    /// <summary>
    /// 手动保存并关闭日志文件
    /// </summary>
    public void SaveLogFile()
    {
        CloseLogFile();
        if (EnableFileLogging)
        {
            InitializeLogFile(); // 重新初始化以继续记录
        }
    }

    /// <summary>
    /// 记录旋转事件日志
    /// </summary>
    /// <param name="eventType">事件类型</param>
    /// <param name="details">详细信息</param>
    private void LogRotationEvent(string eventType, string details = "")
    {
        if (!EnableFileLogging || !EnableRotationLogging || !logFileInitialized) return;

        try
        {
            string timestamp = System.DateTime.Now.ToString("HH:mm:ss.fff");
            Vector3 currentRotation = Rov.RovTransform.eulerAngles;
            float currentHeading = currentRotation.y;

            string logEntry = $"[{timestamp}] 旋转事件: {eventType}";
            if (!string.IsNullOrEmpty(details))
            {
                logEntry += $" - {details}";
            }
            logEntry += $" | 当前朝向: {currentHeading:F1}° | 朝向误差: {currentHeadingError:F1}°";

            logWriter.WriteLine(logEntry);
            logWriter.Flush();

            // 同时输出到控制台（可选）
            Debug.Log(logEntry);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"写入旋转日志失败: {e.Message}");
        }
    }

    #endregion

}
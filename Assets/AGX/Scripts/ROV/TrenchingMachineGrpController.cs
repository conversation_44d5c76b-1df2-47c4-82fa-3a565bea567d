
using System;
using System.Collections;
using System.Threading.Tasks;
using agxSDK;
using AGXUnity;
using AGXUnity.Model;
using UnityEngine;

public enum DeformableTerrainType
{
    DeformableTerrain,
    DeformableTerrainPagerSmall,
    DeformableTerrainPagerBig,
}

public class TrenchingMachineGrpController : MonoBehaviour
{
    public Constraint GrpPrismaticConstraint;
    public Constraint GrpRotateonstraint;
    private RangeReal range;
    Animator animator;
    public DeformableTerrainType TerrainType = DeformableTerrainType.DeformableTerrain;
    public DeformableTerrain terrain;
    public DeformableTerrainPager TerrainPagerSmall;
    public DeformableTerrainPager TerrainPagerBig;

    public DeformableTerrainBase terrainBase
    {
        get
        {
            if(TerrainType == DeformableTerrainType.DeformableTerrain)
                return terrain;
            else if (TerrainType == DeformableTerrainType.DeformableTerrainPagerSmall)
                return TerrainPagerSmall;
            else if (TerrainType == DeformableTerrainType.DeformableTerrainPagerBig)
                return TerrainPagerBig;
            return terrain;
        }
    }
    private void Start()
    { 
        var rangeController = GrpRotateonstraint.GetController<RangeController>();
        range = rangeController.Range;
        animator = GetComponentInParent<Animator>();
        TerrainShovel.GetInitialized<DeformableTerrainShovel>();
        AGXUtil.SetAGXCollosionIgnoreAll(TerrainShovel.GetComponent<RigidBody>(), CollisionsShapeType.Terrain);
        isShovelCollisionCable = true;
        TerrainShovel.AutoAddToTerrains = true;
        StartCoroutine(DisableShovel());
    }

    private IEnumerator DisableShovel()
    {
        yield return new WaitForSeconds(0.1f);
        SetEnableShovel(false);
        
    }

    public DeformableTerrainShovel TerrainShovel;
    [Header("挖沟机")]
    public AGXUnity.Collide.Shape[] grpShapes;
    private bool _isShovelCollisionCable = true;

    /// <summary>
    /// 行挖机与绳缆碰撞是否开启
    /// </summary>
    public bool isShovelCollisionCable
    {
        get { return _isShovelCollisionCable; }
        set
        {
            _isShovelCollisionCable = value;
            Debug.Log("是否运行挖机与绳缆碰撞:" + value);
            for (int i = 0; i < grpShapes.Length; i++)
            {
                for (int j = 0; j < Cables.Length; j++)
                {
                    AGXUtil.SetAGXCollisionsEnable(grpShapes[i], Cables[j], value);
                }    
            }
        }
    }
    public Cable[] Cables; 
    public bool isShovelEnable { get; private set; }
    
    /// <summary>
    /// 设置是否启动挖土功能
    /// </summary>
    /// <param name="isEnable"></param>
    public void SetEnableShovel(bool isEnable)
    {
        isShovelEnable = isEnable;
        //左右挖沟时，当开启挖沟，会禁止挖机与土壤碰撞，从而实现左右挖沟的功能
        for (int i = 0; i < grpShapes.Length; i++)
        {
            AGXUtil.SetAGXCollisionsEnable(grpShapes[i], terrainBase, !isEnable);
        }
        TerrainShovel.enabled = isEnable;
    }

    private float Get1DAxis(KeyCode negative, KeyCode positive)
    {
        int value = 0;
        if (Input.GetKey(negative))
        {
            value -= 1;
        }
        if(Input.GetKey(positive))
        {
            value += 1;
        }
        return value;
    }

    private void Update()
    {
        float v = Get1DAxis(KeyCode.C,KeyCode.V);
        if (v == 0)
        {
            StopMove();
        }else
        {
            MoveAnim(v);
        }
        if(Input.GetKeyDown(KeyCode.T))
        {
            isShovelCollisionCable = !isShovelCollisionCable;
           
        }
    }

    private float animSpeed = 0.2f;
    private void MoveAnim(float f)
    {
        TargetSpeedController prisTargetSpeedController = GrpPrismaticConstraint.GetController<TargetSpeedController>();
        LockController prisLockController = GrpPrismaticConstraint.GetController<LockController>();
        TargetSpeedController rotateTargetSpeedController = GrpRotateonstraint.GetController<TargetSpeedController>();
        LockController rotateLockController = GrpRotateonstraint.GetController<LockController>();

        float curAngle = GrpRotateonstraint.GetCurrentAngle();
        if (f > 0)
        {
            if (Mathf.Abs(curAngle - range.Min) > 0.001f)
            {
                prisLockController.Enable = false;
                rotateLockController.Enable = false;
                rotateTargetSpeedController.Speed = -animSpeed;
                prisTargetSpeedController.Speed = animSpeed;
                SetAnimFloat();
            }
            else
            {
                if (!prisLockController.Enable || !rotateLockController.Enable)
                {
                    StopMove();
                }
            }
        }else if(f < 0)
        {
            if (Mathf.Abs(curAngle - range.Max) > 0.001f)
            {
                prisLockController.Enable = false;
                rotateLockController.Enable = false;
                rotateTargetSpeedController.Speed = animSpeed;
                prisTargetSpeedController.Speed = animSpeed;
                SetAnimFloat();
            }
            else
            {
                if (!prisLockController.Enable || !rotateLockController.Enable)
                {
                    StopMove();
                }
            }
        }
    }

    private void StopMove()
    {
        GrpPrismaticConstraint.GetController<TargetSpeedController>().Speed = 0;
        LockController prisLockController = GrpPrismaticConstraint.GetController<LockController>();
        prisLockController.Enable = true;
        prisLockController.Position = GrpPrismaticConstraint.GetCurrentAngle();
        GrpRotateonstraint.GetController<TargetSpeedController>().Speed = 0;
        LockController rotateLockController = GrpRotateonstraint.GetController<LockController>();
        rotateLockController.Enable = true;
        rotateLockController.Position = GrpRotateonstraint.GetCurrentAngle();
        SetAnimFloat();
    }

    private void SetAnimFloat()
    {
        float v = GrpRotateonstraint.GetCurrentAngle() / range.Min;
        SetEnableShovel(v > 0.05f);
        animator.SetFloat("Front",v);
    }

}


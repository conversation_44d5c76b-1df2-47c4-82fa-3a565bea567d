
using System;
using System.Collections;
using System.Collections.Generic;
using agx;
using AGXUnity;
using AGXUnity.Collide;
using AGXUnity.Model;
using Unity.VisualScripting;
using UnityEngine;
public class DestroySoilParticle2 : MonoBehaviour
{
    DeformableTerrainType type;
    private float time;
    public Box box;
    public DeformableTerrain terrain;
    public DeformableTerrainPager TerrainPager;
    public bool isDestorySoilParticle = true;
    public Transform ShovelBottom;
    private void Awake()
    {
        type = terrain.gameObject.activeSelf ? DeformableTerrainType.DeformableTerrain : DeformableTerrainType.DeformableTerrainPagerSmall;
        // Simulation.Instance.ContactCallbacks.OnContact(OnContactShape,box);
    }
    agx.ParticleSystem _ps;

    agx.ParticleSystem PS {
        get
        {
            if (_ps == null)
            {
                _ps = Simulation.Instance.Native.getParticleSystem();
            }
            return _ps;
        }
    }
    private void OnSimulationPost()
    {
        var coarseParticles = type == DeformableTerrainType.DeformableTerrain ? terrain.GetParticles() : TerrainPager.GetParticles();
        if ( coarseParticles == null )
            return;
        int m_numCoarseParticles = (int)coarseParticles.size();
        for (uint i = 0; i < m_numCoarseParticles; ++i)
        {
            if (timers.Find(x =>  x.GranularBodyPtr == coarseParticles.at(i)) == null)
            {
                timers.Add(GetDestroySoilParticleTimer(coarseParticles.at(i)));
            }
        }
    }
    List<DestroySoilParticleTimer> timers = new List<DestroySoilParticleTimer>();
    Queue<DestroySoilParticleTimer> DestroySoilParticleTimersPools = new Queue<DestroySoilParticleTimer>();

    DestroySoilParticleTimer GetDestroySoilParticleTimer(GranularBodyPtr granularBodyPtr)
    {
        DestroySoilParticleTimer timer = null; 
        if(DestroySoilParticleTimersPools.Count > 0)
        {
            timer =  DestroySoilParticleTimersPools.Dequeue();
            timer.Reset();
        }
        else
        {
            timer = new DestroySoilParticleTimer();
        }
        timer.SetGranularBody(granularBodyPtr);
        return timer;   
    }

    List<DestroySoilParticleTimer> removeList = new List<DestroySoilParticleTimer>();
    float collectTime;
    private void Update()
    {
        removeList.Clear();
        foreach (var timer in timers)
        {
            if (timer.CheckDestroy(PS))
            {
                removeList.Add(timer);
            }
        }
        foreach (var timer in removeList)
        {
            DestroySoilParticleTimersPools.Enqueue(timer);
        }
        collectTime += Time.deltaTime;
        if (collectTime > 0.1f)
        {
            collectTime -= 0.1f;
            OnSimulationPost();
        }
    }
    
}

public class DestroySoilParticleTimer
{
    public float time;
    public GranularBodyPtr GranularBodyPtr;

    public DestroySoilParticleTimer()
    {
        Reset();
    }
    
    public void Reset()
    {
        GranularBodyPtr = null;
        time = float.MaxValue;
    }

    public void SetGranularBody(GranularBodyPtr granularBodyPtr)
    {
        GranularBodyPtr = granularBodyPtr;
        time = Time.time;
    }

    public bool CheckDestroy(agx.ParticleSystem particleSystem)
    {
        if(Time.time - time > 0.5f)
        {
            if (GranularBodyPtr != null)
            {
                try
                {
                    particleSystem.destroyParticle(GranularBodyPtr);
                }
                catch
                {
                }
            }
            Reset();
            return true;
        }
        return false;
    }
    
}
using UnityEngine;
using System.Collections;

/// <summary>
/// ROV精确移动使用示例
/// 展示如何在代码中使用优化后的精确移动功能
/// </summary>
public class ROVPrecisionMoveExample : MonoBehaviour
{
    [Header("ROV控制器")]
    public RovController rovController;
    
    [Header("示例目标位置")]
    public Vector3[] waypoints = new Vector3[]
    {
        new Vector3(29.534f, 9.527f, 3.340f),  // 原始测试目标
        new Vector3(30.0f, 10.0f, 4.0f),       // 第二个目标
        new Vector3(28.0f, 8.0f, 2.0f),        // 第三个目标
    };
    
    [Header("自动巡航设置")]
    public bool autoPatrol = false;
    public float waitTimeAtWaypoint = 3f;
    
    private int currentWaypointIndex = 0;
    private bool isMoving = false;
    private Coroutine patrolCoroutine;

    void Start()
    {
        if (rovController == null)
        {
            rovController = FindObjectOfType<RovController>();
        }
        
        if (rovController == null)
        {
            Debug.LogError("未找到RovController组件！");
            return;
        }

        Debug.Log("ROV精确移动示例已启动");
        Debug.Log("按键说明:");
        Debug.Log("Space - 移动到下一个航点");
        Debug.Log("Enter - 开始/停止自动巡航");
        Debug.Log("Escape - 停止所有移动");
        
        if (autoPatrol)
        {
            StartPatrol();
        }
    }

    void Update()
    {
        if (rovController == null) return;
        
        // Space键：移动到下一个航点
        if (Input.GetKeyDown(KeyCode.Space) && !isMoving)
        {
            MoveToNextWaypoint();
        }
        
        // Enter键：开始/停止自动巡航
        if (Input.GetKeyDown(KeyCode.Return))
        {
            TogglePatrol();
        }
        
        // Escape键：停止所有移动
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            StopAllMovement();
        }
        
        // 检查移动状态
        if (isMoving && rovController.IsAtTarget())
        {
            OnWaypointReached();
        }
    }

    /// <summary>
    /// 移动到下一个航点
    /// </summary>
    public void MoveToNextWaypoint()
    {
        if (waypoints.Length == 0)
        {
            Debug.LogWarning("没有设置航点！");
            return;
        }
        
        Vector3 targetPos = waypoints[currentWaypointIndex];
        
        Debug.Log($"=== 开始精确移动到航点 {currentWaypointIndex + 1} ===");
        Debug.Log($"目标位置: {targetPos:F3}");
        
        // 使用优化的精确移动方法
        rovController.MoveToTargetPrecise(targetPos);
        isMoving = true;
        
        // 移动到下一个航点索引
        currentWaypointIndex = (currentWaypointIndex + 1) % waypoints.Length;
    }

    /// <summary>
    /// 到达航点时的处理
    /// </summary>
    private void OnWaypointReached()
    {
        isMoving = false;
        Vector3 currentPos = rovController.Rov.RovTransform.position;
        float finalDistance = rovController.GetDistanceToTarget();
        
        Debug.Log($"=== 成功到达航点！ ===");
        Debug.Log($"最终位置: {currentPos:F3}");
        Debug.Log($"最终误差: {finalDistance:F4}m");
        
        // 评估到达精度
        if (finalDistance < 0.02f)
            Debug.Log("精度评估: 优秀");
        else if (finalDistance < 0.05f)
            Debug.Log("精度评估: 良好");
        else if (finalDistance < 0.1f)
            Debug.Log("精度评估: 一般");
        else
            Debug.Log("精度评估: 需要改进");
    }

    /// <summary>
    /// 开始/停止自动巡航
    /// </summary>
    public void TogglePatrol()
    {
        if (patrolCoroutine == null)
        {
            StartPatrol();
        }
        else
        {
            StopPatrol();
        }
    }

    /// <summary>
    /// 开始自动巡航
    /// </summary>
    public void StartPatrol()
    {
        if (patrolCoroutine != null)
        {
            StopCoroutine(patrolCoroutine);
        }
        
        patrolCoroutine = StartCoroutine(PatrolCoroutine());
        Debug.Log("开始自动巡航");
    }

    /// <summary>
    /// 停止自动巡航
    /// </summary>
    public void StopPatrol()
    {
        if (patrolCoroutine != null)
        {
            StopCoroutine(patrolCoroutine);
            patrolCoroutine = null;
        }
        
        Debug.Log("停止自动巡航");
    }

    /// <summary>
    /// 自动巡航协程
    /// </summary>
    private IEnumerator PatrolCoroutine()
    {
        while (true)
        {
            // 移动到下一个航点
            MoveToNextWaypoint();
            
            // 等待到达目标
            while (isMoving)
            {
                yield return new WaitForSeconds(0.1f);
            }
            
            // 在航点等待
            Debug.Log($"在航点等待 {waitTimeAtWaypoint} 秒...");
            yield return new WaitForSeconds(waitTimeAtWaypoint);
        }
    }

    /// <summary>
    /// 停止所有移动
    /// </summary>
    public void StopAllMovement()
    {
        StopPatrol();
        rovController.StopMovement();
        isMoving = false;
        Debug.Log("已停止所有移动");
    }

    /// <summary>
    /// 设置新的航点列表
    /// </summary>
    public void SetWaypoints(Vector3[] newWaypoints)
    {
        waypoints = newWaypoints;
        currentWaypointIndex = 0;
        Debug.Log($"已设置 {waypoints.Length} 个新航点");
    }

    /// <summary>
    /// 添加航点
    /// </summary>
    public void AddWaypoint(Vector3 waypoint)
    {
        System.Array.Resize(ref waypoints, waypoints.Length + 1);
        waypoints[waypoints.Length - 1] = waypoint;
        Debug.Log($"已添加航点: {waypoint:F3}");
    }

    /// <summary>
    /// 移动到指定位置（公共接口）
    /// </summary>
    public void MoveTo(Vector3 position)
    {
        Debug.Log($"精确移动到: {position:F3}");
        rovController.MoveToTargetPrecise(position);
        isMoving = true;
    }

    /// <summary>
    /// 检查是否正在移动
    /// </summary>
    public bool IsMoving()
    {
        return isMoving;
    }

    /// <summary>
    /// 获取当前目标航点索引
    /// </summary>
    public int GetCurrentWaypointIndex()
    {
        return currentWaypointIndex;
    }

    /// <summary>
    /// 在Scene视图中绘制航点
    /// </summary>
    void OnDrawGizmos()
    {
        if (waypoints == null || waypoints.Length == 0) return;
        
        // 绘制航点
        for (int i = 0; i < waypoints.Length; i++)
        {
            Gizmos.color = (i == currentWaypointIndex) ? Color.red : Color.yellow;
            Gizmos.DrawWireSphere(waypoints[i], 0.2f);
            
            // 绘制航点编号
            Gizmos.color = Color.white;
            Vector3 labelPos = waypoints[i] + Vector3.up * 0.5f;
            
            #if UNITY_EDITOR
            UnityEditor.Handles.Label(labelPos, (i + 1).ToString());
            #endif
        }
        
        // 绘制航点之间的连线
        Gizmos.color = Color.cyan;
        for (int i = 0; i < waypoints.Length; i++)
        {
            int nextIndex = (i + 1) % waypoints.Length;
            Gizmos.DrawLine(waypoints[i], waypoints[nextIndex]);
        }
        
        // 绘制当前ROV位置到目标的连线
        if (rovController != null && rovController.Rov != null && isMoving)
        {
            Vector3 currentPos = rovController.Rov.RovTransform.position;
            Vector3 targetPos = waypoints[(currentWaypointIndex - 1 + waypoints.Length) % waypoints.Length];
            
            Gizmos.color = Color.green;
            Gizmos.DrawLine(currentPos, targetPos);
        }
    }
}

using UnityEngine;

/// <summary>
/// 自适应朝向控制使用示例
/// 演示如何使用ROV的自适应朝向控制功能
/// </summary>
public class AdaptiveHeadingExample : MonoBehaviour
{
    [Header("ROV控制器")]
    public RovController rovController;
    
    [Header("示例目标点")]
    public Transform[] waypoints;
    
    [Header("自动演示")]
    public bool autoDemo = false;
    public float demoInterval = 15f; // 每个目标点的演示时间
    
    private int currentWaypointIndex = 0;
    private float lastDemoTime = 0f;
    
    void Start()
    {
        if (rovController == null)
        {
            rovController = FindObjectOfType<RovController>();
        }
        
        if (rovController == null)
        {
            Debug.LogError("未找到RovController！请确保场景中有ROV控制器。");
            return;
        }
        
        // 配置自适应朝向控制参数
        SetupAdaptiveHeading();
        
        Debug.Log("自适应朝向控制示例已启动");
        Debug.Log("按键说明：");
        Debug.Log("Q - 测试快速旋转（大角度）");
        Debug.Log("W - 测试自适应旋转（中角度）");
        Debug.Log("E - 测试精细调整（小角度）");
        Debug.Log("R - 移动到下一个航点");
        Debug.Log("T - 切换自动演示模式");
        Debug.Log("Y - 停止所有移动");
    }
    
    void Update()
    {
        if (rovController == null) return;
        
        // 处理键盘输入
        HandleInput();
        
        // 自动演示逻辑
        if (autoDemo && waypoints != null && waypoints.Length > 0)
        {
            if (Time.time - lastDemoTime > demoInterval)
            {
                MoveToNextWaypoint();
                lastDemoTime = Time.time;
            }
        }
    }
    
    void OnGUI()
    {
        // 显示状态信息
        GUILayout.BeginArea(new Rect(Screen.width - 300, 10, 290, 200));
        GUILayout.Label("=== 自适应朝向控制状态 ===", GUI.skin.box);
        
        if (rovController != null)
        {
            float headingError = rovController.GetCurrentHeadingError();
            AdaptiveHeadingMode mode = rovController.GetCurrentHeadingMode();
            float distance = rovController.GetDistanceToTarget();
            bool atTarget = rovController.IsAtTarget();
            
            GUILayout.Label($"朝向误差: {headingError:F1}°");
            GUILayout.Label($"控制模式: {GetModeDescription(mode)}");
            GUILayout.Label($"到目标距离: {distance:F2}m");
            GUILayout.Label($"已到达目标: {(atTarget ? "是" : "否")}");
            
            GUILayout.Space(10);
            GUILayout.Label($"自动演示: {(autoDemo ? "开启" : "关闭")}");
            if (waypoints != null && waypoints.Length > 0)
            {
                GUILayout.Label($"当前航点: {currentWaypointIndex + 1}/{waypoints.Length}");
            }
        }
        
        GUILayout.EndArea();
    }
    
    private void SetupAdaptiveHeading()
    {
        // 配置自适应朝向控制参数
        rovController.SetAdaptiveHeadingParameters(
            enable: true,                    // 启用自适应朝向控制
            fastRotationThreshold: 15f,      // 快速旋转角度阈值
            adaptiveRotationThreshold: 45f,  // 自适应旋转角度阈值
            fastSpeedMultiplier: 3.0f,       // 快速旋转速度倍数
            adaptiveSpeedMultiplier: 1.5f,   // 自适应旋转速度倍数
            toleranceAngle: 2f,              // 朝向容差角度
            enableMoveWhileRotating: true    // 启用边移动边旋转
        );
        
        Debug.Log("自适应朝向控制参数已配置");
    }
    
    private void HandleInput()
    {
        if (Input.GetKeyDown(KeyCode.Q))
        {
            TestFastRotation();
        }
        else if (Input.GetKeyDown(KeyCode.W))
        {
            TestAdaptiveRotation();
        }
        else if (Input.GetKeyDown(KeyCode.E))
        {
            TestFineAdjustment();
        }
        else if (Input.GetKeyDown(KeyCode.R))
        {
            MoveToNextWaypoint();
        }
        else if (Input.GetKeyDown(KeyCode.T))
        {
            ToggleAutoDemo();
        }
        else if (Input.GetKeyDown(KeyCode.Y))
        {
            StopAllMovement();
        }
    }
    
    private void TestFastRotation()
    {
        // 创建一个需要大角度旋转的目标点（90度方向）
        Vector3 rovPos = rovController.transform.position;
        Vector3 targetPos = rovPos + rovController.transform.right * 10f;
        
        rovController.SetTargetPositionWithHeading(targetPos, true);
        
        Debug.Log("测试快速旋转模式 - 目标在90度方向");
        LogCurrentState();
    }
    
    private void TestAdaptiveRotation()
    {
        // 创建一个需要中等角度旋转的目标点（45度方向）
        Vector3 rovPos = rovController.transform.position;
        Vector3 direction = (rovController.transform.forward + rovController.transform.right).normalized;
        Vector3 targetPos = rovPos + direction * 10f;
        
        rovController.SetTargetPositionWithHeading(targetPos, true);
        
        Debug.Log("测试自适应旋转模式 - 目标在45度方向");
        LogCurrentState();
    }
    
    private void TestFineAdjustment()
    {
        // 创建一个需要小角度调整的目标点（10度方向）
        Vector3 rovPos = rovController.transform.position;
        Vector3 direction = (rovController.transform.forward * 0.9f + rovController.transform.right * 0.1f).normalized;
        Vector3 targetPos = rovPos + direction * 10f;
        
        rovController.SetTargetPositionWithHeading(targetPos, true);
        
        Debug.Log("测试精细调整模式 - 目标在10度偏移方向");
        LogCurrentState();
    }
    
    private void MoveToNextWaypoint()
    {
        if (waypoints == null || waypoints.Length == 0)
        {
            Debug.LogWarning("没有设置航点！请在Inspector中设置waypoints数组。");
            return;
        }
        
        Transform targetWaypoint = waypoints[currentWaypointIndex];
        if (targetWaypoint == null)
        {
            Debug.LogWarning($"航点 {currentWaypointIndex} 为空！");
            return;
        }
        
        rovController.SetTargetPositionWithHeading(targetWaypoint.position, true);
        
        Debug.Log($"移动到航点 {currentWaypointIndex + 1}: {targetWaypoint.position:F2}");
        LogCurrentState();
        
        // 移动到下一个航点
        currentWaypointIndex = (currentWaypointIndex + 1) % waypoints.Length;
    }
    
    private void ToggleAutoDemo()
    {
        autoDemo = !autoDemo;
        lastDemoTime = Time.time;
        
        Debug.Log($"自动演示模式: {(autoDemo ? "开启" : "关闭")}");
        
        if (autoDemo)
        {
            MoveToNextWaypoint();
        }
    }
    
    private void StopAllMovement()
    {
        rovController.StopMovement();
        rovController.SetHeadingControl(false);
        autoDemo = false;
        
        Debug.Log("已停止所有移动和朝向控制");
    }
    
    private void LogCurrentState()
    {
        Vector3 rovPos = rovController.transform.position;
        float rovYaw = rovController.transform.eulerAngles.y;
        
        Debug.Log($"ROV当前状态 - 位置: {rovPos:F2}, 朝向: {rovYaw:F1}°");
    }
    
    private string GetModeDescription(AdaptiveHeadingMode mode)
    {
        switch (mode)
        {
            case AdaptiveHeadingMode.OnTarget:
                return "已对准";
            case AdaptiveHeadingMode.FineAdjustment:
                return "精细调整";
            case AdaptiveHeadingMode.AdaptiveRotation:
                return "自适应旋转";
            case AdaptiveHeadingMode.FastRotation:
                return "快速旋转";
            default:
                return "未知";
        }
    }
}

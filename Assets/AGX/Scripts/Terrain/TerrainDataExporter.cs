using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using System.IO;
using System.Linq;

public class TerrainDataExporter : EditorWindow
{
    private string exportPath = "Assets/AGX/Scenes/SplitTerrain";
    private bool overwriteExisting = true;
    private Vector2 scrollPosition;

    [MenuItem("Tools/Terrain Data Exporter")]
    public static void ShowWindow()
    {
        GetWindow<TerrainDataExporter>("Terrain Data Exporter");
    }

    private void OnGUI()
    {
        GUILayout.Label("Terrain Data Exporter", EditorStyles.boldLabel);
        GUILayout.Space(10);

        // 显示导出路径
        GUILayout.Label("Export Path:");
        EditorGUILayout.BeginHorizontal();
        exportPath = EditorGUILayout.TextField(exportPath);
        if (GUILayout.Button("Browse", GUILayout.Width(80)))
        {
            string selectedPath = EditorUtility.OpenFolderPanel("Select Export Folder", "Assets", "");
            if (!string.IsNullOrEmpty(selectedPath))
            {
                // 转换为项目相对路径
                if (selectedPath.StartsWith(Application.dataPath))
                {
                    exportPath = "Assets" + selectedPath.Substring(Application.dataPath.Length);
                }
                else
                {
                    EditorUtility.DisplayDialog("Error", "Please select a folder inside the Assets directory.", "OK");
                }
            }
        }
        EditorGUILayout.EndHorizontal();

        // 覆盖选项
        overwriteExisting = EditorGUILayout.Toggle("Overwrite Existing Assets", overwriteExisting);

        GUILayout.Space(10);

        // 导出按钮
        if (GUILayout.Button("Export Terrain Data"))
        {
            ExportTerrainData();
        }

        // 重新绑定按钮
        if (GUILayout.Button("Rebind Terrain Data"))
        {
            RebindTerrainData();
        }

        GUILayout.Space(10);
        GUILayout.Label("Terrain Objects in Scene:", EditorStyles.boldLabel);
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        // 列出场景中的所有 Terrain
        Terrain[] terrains = FindObjectsOfType<Terrain>();
        if (terrains.Length == 0)
        {
            EditorGUILayout.LabelField("No terrains found in the current scene.");
        }
        else
        {
            foreach (Terrain terrain in terrains)
            {
                EditorGUILayout.ObjectField(terrain, typeof(Terrain), true);
                if (terrain.terrainData != null)
                {
                    EditorGUILayout.LabelField("Terrain Data Path:", AssetDatabase.GetAssetPath(terrain.terrainData));
                }
            }
        }
        EditorGUILayout.EndScrollView();
    }

    private void ExportTerrainData()
    {
        // 检查路径有效性
        if (string.IsNullOrEmpty(exportPath) || !exportPath.StartsWith("Assets"))
        {
            EditorUtility.DisplayDialog("Error", "Please specify a valid export path within the Assets folder.", "OK");
            return;
        }

        // 创建导出文件夹
        if (!AssetDatabase.IsValidFolder(exportPath))
        {
            Directory.CreateDirectory(Path.Combine(Application.dataPath, exportPath.Substring(7)));
            AssetDatabase.Refresh();
        }

        // 获取所有 Terrain
        Terrain[] terrains = FindObjectsOfType<Terrain>();
        if (terrains.Length == 0)
        {
            EditorUtility.DisplayDialog("Warning", "No terrains found in the current scene.", "OK");
            return;
        }

        int exportedCount = 0;
        int skippedCount = 0;
        foreach (Terrain terrain in terrains)
        {
            if (terrain.terrainData == null) continue;

            // 生成唯一文件名
            string terrainName = string.IsNullOrEmpty(terrain.name) ? "Terrain" : terrain.name;
            string assetPath = $"{exportPath}/{terrainName}.asset";

            // 检查是否已存在资产
            string existingPath = AssetDatabase.GetAssetPath(terrain.terrainData);
            if (!string.IsNullOrEmpty(existingPath) && existingPath != terrain.terrainData.name)
            {
                if (!overwriteExisting)
                {
                    Debug.Log($"Terrain {terrain.name} already uses external asset at {existingPath}. Skipping.");
                    skippedCount++;
                    continue;
                }
                else
                {
                    // 删除现有资产以便覆盖
                    AssetDatabase.DeleteAsset(existingPath);
                }
            }

            // 保存 TerrainData 为资产
            AssetDatabase.CreateAsset(terrain.terrainData, assetPath);
            exportedCount++;
        }

        // 保存场景
        if (exportedCount > 0 || skippedCount > 0)
        {
            AssetDatabase.SaveAssets();
            EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            EditorSceneManager.SaveScene(EditorSceneManager.GetActiveScene());
            string message = $"{exportedCount} terrain data assets exported to {exportPath}.";
            if (skippedCount > 0)
            {
                message += $"\n{skippedCount} existing assets skipped.";
            }
            EditorUtility.DisplayDialog("Success", message, "OK");
        }
        else
        {
            EditorUtility.DisplayDialog("Info", "No terrain data exported. All terrains may already use external assets.", "OK");
        }
    }

    private void RebindTerrainData()
    {
        // 获取所有 Terrain
        Terrain[] terrains = FindObjectsOfType<Terrain>();
        if (terrains.Length == 0)
        {
            EditorUtility.DisplayDialog("Warning", "No terrains found in the current scene.", "OK");
            return;
        }

        int reboundCount = 0;
        foreach (Terrain terrain in terrains)
        {
            if (terrain.terrainData == null) continue;

            // 查找匹配的 TerrainData 资产
            string terrainName = string.IsNullOrEmpty(terrain.name) ? "Terrain" : terrain.name;
            string expectedPath = $"{exportPath}/{terrainName}.asset";
            TerrainData loadedData = AssetDatabase.LoadAssetAtPath<TerrainData>(expectedPath);

            if (loadedData != null && loadedData != terrain.terrainData)
            {
                terrain.terrainData = loadedData;
                reboundCount++;
            }
        }

        // 保存场景
        if (reboundCount > 0)
        {
            EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
            EditorSceneManager.SaveScene(EditorSceneManager.GetActiveScene());
            EditorUtility.DisplayDialog("Success", $"{reboundCount} terrains rebound to external terrain data.", "OK");
        }
        else
        {
            EditorUtility.DisplayDialog("Info", "No terrains needed rebinding.", "OK");
        }
    }
}
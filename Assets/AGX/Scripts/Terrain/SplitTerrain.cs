using AGXUnity.Model;
using Unity.VisualScripting;
using UnityEngine;

public enum SpltType
{
    Split_2_2,
    Split_4_4,
    Split_8_8,
    Split_16_16,
    Split_32_32,
}

public class SplitTerrain : MonoBehaviour
{
    public Terrain sourceTerrain; // 原始Terrain

    public SpltType splitType = SpltType.Split_16_16;
    public DeformableTerrainPager TerrainPager;
    int getSplitCount()
    {
        switch (splitType)
        {
            case SpltType.Split_2_2:
                return 2;
            case SpltType.Split_4_4:
                return 4;
            case SpltType.Split_8_8:
                return 8;
            case SpltType.Split_16_16:
                return 16;
            case SpltType.Split_32_32:
                return 32;
        }

        return 16;
    }

    [ContextMenu("Split Terrain")]
    void DoSplitTerrain()
    {
        if (sourceTerrain == null)
        {
            Debug.LogError("请指定一个sourceTerrain！");
            return;
        }
        
        int splitX = getSplitCount(); // X方向分割数
        int splitZ = splitX; // Z方向分割数
        GameObject terrainObj = new GameObject("SplitTerrain");
        terrainObj.transform.position = sourceTerrain.transform.parent == null 
            ? sourceTerrain.transform.position : sourceTerrain.transform.parent.position;
        TerrainData terrainData = sourceTerrain.terrainData;
        int heightmapResolution = terrainData.heightmapResolution; // 例如 257
        int dataResolution = heightmapResolution - 1; // 例如 256

        int subDataResX = dataResolution / splitX; // 例如 128
        int subDataResZ = dataResolution / splitZ; // 例如 128
        int subHeightmapRes = subDataResX + 1; // 例如 129

        float[,] heights = terrainData.GetHeights(0, 0, heightmapResolution, heightmapResolution);

        Terrain[,] subTerrains = new Terrain[splitX, splitZ];

        // 分割并创建子Terrain
        for (int x = 0; x < splitX; x++)
        {
            for (int z = 0; z < splitZ; z++)
            {
                int startX = x * subDataResX;
                int startZ = z * subDataResZ;

                float[,] subHeights = new float[subHeightmapRes, subHeightmapRes];
                for (int i = 0; i < subHeightmapRes; i++)
                {
                    for (int j = 0; j < subHeightmapRes; j++)
                    {
                        int srcX = startX + i;
                        int srcZ = startZ + j;
                        srcX = Mathf.Min(srcX, heightmapResolution - 1);
                        srcZ = Mathf.Min(srcZ, heightmapResolution - 1);
                        subHeights[i, j] = heights[srcX, srcZ];
                    }
                }

                TerrainData subTerrainData = new TerrainData();
                subTerrainData.heightmapResolution = subHeightmapRes;
                subTerrainData.size = new Vector3(terrainData.size.x / splitX, terrainData.size.y, terrainData.size.z / splitZ);
                subTerrainData.SetHeights(0, 0, subHeights);
                subTerrainData.SetDetailResolution(1024,32);
                GameObject subTerrainObj = Terrain.CreateTerrainGameObject(subTerrainData);
                subTerrainObj.name = $"SubTerrain_{x}_{z}";
                subTerrainObj.transform.SetParent( terrainObj.transform);
                // 使用你的位置计算
                subTerrainObj.transform.localPosition= new Vector3(z * subTerrainData.size.x, 0, x * subTerrainData.size.z);

                subTerrains[x, z] = subTerrainObj.GetComponent<Terrain>();
                subTerrains[x, z].terrainData.terrainLayers = terrainData.terrainLayers;
            }
        }

        // 设置邻居关系
        for (int x = 0; x < splitX; x++)
        {
            for (int z = 0; z < splitZ; z++)
            {
                Terrain current = subTerrains[x, z];
                Terrain left = (z > 0) ? subTerrains[x, z - 1] : null;
                Terrain right = (z < splitZ - 1) ? subTerrains[x, z + 1] : null;
                Terrain top = (x < splitX - 1) ? subTerrains[x + 1, z] : null;
                Terrain bottom = (x > 0) ? subTerrains[x - 1, z] : null;

                current.SetNeighbors(left, top, right, bottom);
                current.Flush();
            }
        }
        GameObject target = subTerrains[0, 0].gameObject;

        if (TerrainPager != null)
        {
            // 获取复制的组件类型
            System.Type componentType = TerrainPager.GetType();
            // 添加或获取目标组件
            Component newComponent = target.GetComponent(componentType) ?? target.AddComponent(componentType);

            // 复制组件的属性
            CopyComponentValues(TerrainPager, newComponent);
            var addTerrainPager = ((DeformableTerrainPager)newComponent);
            foreach (var shovel in  TerrainPager.Shovels)
            {
                addTerrainPager.Add(shovel);
            }
            FindObjectOfType<TrenchingMachineGrpController>().TerrainPagerBig = addTerrainPager;
        }

        TerrainTileLoader tileLoader =  terrainObj.AddComponent<TerrainTileLoader>();

        tileLoader.player = GameObject.Find("TrenchingMachine_RIG").transform;
    }
    // 复制组件的属性值
    private static void CopyComponentValues(Component source, Component destination)
    {
        if (source == null || destination == null) return;

        // 使用反射获取组件的所有公共字段和属性
        System.Type type = source.GetType();
        var fields = type.GetFields();
        var properties = type.GetProperties();

        // 复制字段
        foreach (var field in fields)
        {
            field.SetValue(destination, field.GetValue(source));
            Debug.Log("field:" + field.Name);
        }

        // 复制属性（仅复制可写属性）
        foreach (var property in properties)
        {
            if(property.Name.Equals("name"))
                continue;
            if (property.CanWrite && property.CanRead)
            {
                property.SetValue(destination, property.GetValue(source));
                Debug.Log("property:" + property.Name);
            }
        }
    }
}
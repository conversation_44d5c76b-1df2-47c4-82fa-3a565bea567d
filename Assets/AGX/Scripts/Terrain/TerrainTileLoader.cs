using System;
using UnityEngine;
using System.Collections.Generic;

public class TerrainTileLoader : MonoBehaviour
{
    public Transform player; // 玩家或相机
    [SerializeField] private float tileSize = 1500f; // 每个地形块大小
    [SerializeField] private float loadDistance = 5000f; // 加载距离
    
    private List<Terrain> allTiles = new List<Terrain>();
    
    private void Awake()
    {
        allTiles.AddRange( FindObjectsOfType<Terrain>());
        LoadTilesAround();
    }

    void Update()
    {
        LoadTilesAround();
    }

    void LoadTilesAround()
    {
        Vector3 offset = new Vector3(tileSize * 0.5f, 0, tileSize * 0.5f);
        foreach (var terrain in allTiles)
        {
            terrain.gameObject.SetActive(GetHorizontalDistance(terrain.gameObject.transform.position +offset , player.position) < loadDistance);
        }
    }
    
    float GetHorizontalDistance(Vector3 point1, Vector3 point2)
    {
        Vector2 p1 = new Vector2(point1.x, point1.z);
        Vector2 p2 = new Vector2(point2.x, point2.z);
        return Vector2.Distance(p1, p2);
    }
    
}

using System;
using Unity.VisualScripting;
using UnityEngine;

public enum MachineControl
{
    ROV,
    CrawlerCar,
}

public class TrenchingMachine : MonoBehaviour
{
    RovController rovController;
    public MachineControl machineControl;

    public Transform checkGround;
    
    private void Awake()
    {
        rovController = GetComponent<RovController>();
        Debug.Log("当前控制类型:"+machineControl);
    }

    public void Update()
    {
        if(Input.GetKeyDown(KeyCode.Space))
        {
            machineControl = machineControl == MachineControl.ROV ? MachineControl.CrawlerCar : MachineControl.ROV;
            Debug.Log("当前控制类型:"+machineControl);
        }
        // if(machineControl == MachineControl.ROV)
        // {
        //     rovController.targetMassAjustWeight = 0;
        // }else if (machineControl == MachineControl.CrawlerCar)
        // {
        //     RaycastHit hit;
        //     if (Physics.Raycast(checkGround.position, Vector3.down, out hit, 100))
        //     {
        //         if (hit.collider.GetType() == typeof(TerrainCollider))
        //         {
        //             if (hit.distance > 1.0f)
        //             {
        //                 rovController.targetMassAjustWeight = 0.05f;
        //             }else if (hit.distance > 0.5f)
        //             {
        //                 rovController.targetMassAjustWeight = 0.1f;
        //             }
        //             else
        //             {
        //                 rovController.targetMassAjustWeight = 2.0f;
        //             }
        //         }
        //     }
        // }
    }
}
using AGXUnity;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;
using Input = UnityEngine.Input;
#if UNITY_EDITOR
using UnityEditor;
#endif
public class AgxLincense : MonoBehaviour
{
    public GameObject Panel;
    public Button unLoadBtn;
    public Button loadBtn;
    public Text text;
    // Start is called before the first frame update
    StringBuilder sb = new StringBuilder();

    private void Awake()
    {
        Panel.SetActive(false);
    }

    void Start()
    {
        string AGXLicPath = s_getAGXLicPath();
        Debug.Log(AGXLicPath);
        text.text = LicenseManager.LoadFile()? "证书已加载" : "证书未加载";
        unLoadBtn.onClick.AddListener(() =>
        {
            sb.Clear();
            if (!s_delLincese())
            {
                sb.AppendLine("卸载证书失败: " + LicenseManager.LicenseInfo.Status);
            }
            else {
                sb.AppendLine("卸载证书成功!");
            }
            text.text = sb.ToString();
        });
        loadBtn.onClick.AddListener(() =>
        {
            sb.Clear();
            if ( !LicenseManager.LoadFile()) {
                if (!s_loadLincese())
                {
                    sb.AppendLine("添加证书失败: " + LicenseManager.LicenseInfo.Status);
                }
                else {
                    if(File.Exists(Application.dataPath + "/agx.lfx"))
                    {
                        File.Copy(Application.dataPath + "/agx.lfx", AGXLicPath, true);
                        sb.AppendLine($"拷贝证书:" + AGXLicPath);
                    }
                    sb.AppendLine("添加证书成功!");
                }
                text.text = sb.ToString();
            }
            else
            {
                Debug.Log("证书已加载，或者证书操作过快");
            }
        });
    }

    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.F1) && Input.GetKey(KeyCode.LeftShift))
        {
            Panel.SetActive(!Panel.activeSelf);
        }
    }
    

    private static string s_getAGXLicPath()
    {
        string userProfilePath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
        return userProfilePath +@"\AppData\Local\Algoryx\agx\agx.lfx";  
    }

    private static bool s_delLincese()
    {
        var file = LicenseManager.FindLicenseFiles(LicenseInfo.LicenseType.Service).FirstOrDefault();
        return LicenseManager.DeactivateAndDelete(file);
    }
    private static bool s_loadLincese()
    {
        return AGXUnity.LicenseManager.Activate( 66220061,
            "6S97Q3A2",
            Application.dataPath );
    }
    

#if UNITY_EDITOR
    [MenuItem( "AGXUnity/AGX--证书/添加证书", priority = 1 )]
    public static void AddLincense()
    {
        if ( !LicenseManager.LoadFile()) {
            string AGXLicPath = s_getAGXLicPath();
            if (!s_loadLincese())
            {
                Debug.Log("添加证书失败: " + LicenseManager.LicenseInfo.Status);
            }
            else {
                if(File.Exists(Application.dataPath + "/agx.lfx"))
                {
                    File.Copy(Application.dataPath + "/agx.lfx", AGXLicPath, true);
                    Debug.Log($"拷贝证书:" + AGXLicPath);
                }
                Debug.Log("添加证书成功!");
            }
        }else
        {
            Debug.Log("证书已加载，或者证书操作过快");
        }
    }
    
    [MenuItem( "AGXUnity/AGX--证书/删除证书", priority = 2 )]
    public static void DelLincense()
    {
        if (!s_delLincese())
        {
           Debug.LogError("卸载证书失败: " + LicenseManager.LicenseInfo.Status);
        }
        else {
            Debug.Log("卸载证书成功!");
        }
    }
#endif
}
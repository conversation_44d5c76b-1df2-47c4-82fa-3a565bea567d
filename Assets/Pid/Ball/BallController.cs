using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BallController : Mono<PERSON><PERSON>aviour
{
    private BallPIDController speedPID;
    private BallPIDController positionPID;
    private float currentSpeed;
    private float currentPosition;
    private float force = 0f; // 初始力
    public Transform ballTransform; // 拖拽小球的 Transform
    public float targetPosition = 10.0f; // 目标位置

    void Start()
    {
        speedPID = new BallPIDController(10f, 0f, 0f, 20);    // 速度内环
        positionPID = new BallPIDController(10f, 0f, 3f, 20); // 位置外环
        currentSpeed = 0f;
        currentPosition = ballTransform.position.x; // 初始位置
    }

    void Update()
    {
        float dt = Time.deltaTime;

        // 外环：位置到目标速度
        float targetSpeed = positionPID.Calculate(targetPosition, currentPosition, dt, 0f); // 实际输出暂设为0
        targetSpeed = Mathf.Clamp(targetSpeed, -5.0f, 5.0f);

        // 内环：速度到力，使用前一帧的 force 作为实际输出
        float computedForce = speedPID.Calculate(targetSpeed, currentSpeed, dt, force);
        force = Mathf.Clamp(computedForce, -10.0f, 10.0f);

        // 更新小球物理
        currentSpeed += force * dt;
        currentPosition += currentSpeed * dt;

        // 更新小球位置
        ballTransform.position = new Vector3(currentPosition, ballTransform.position.y, ballTransform.position.z);

        Debug.Log($"位置: {currentPosition}, 速度: {currentSpeed}, 力: {force}");
    }
}

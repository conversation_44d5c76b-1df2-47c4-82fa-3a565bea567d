using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BallPIDController
{
    private float Kp, Ki, Kd;
    private float previousError;
    private float integral;
    private float integralSeparationThreshold = 2.0f; // 积分分离阈值
    private float integralMax = 100.0f; // 积分上限
    private float integralMin = -100.0f; // 积分下限
    private float outputMax = 10.0f; // 输出上限
    private float outputMin = -10.0f; // 输出下限

    public BallPIDController(float kp, float ki, float kd, float outputRange)
    {
        Kp = kp;
        Ki = ki;
        Kd = kd;
        previousError = 0f;
        integral = 0f;
        outputMax = outputRange;
        outputMin = -outputRange;
    }

    public float Calculate(float setpoint, float pv, float dt, float actualOutput)
    {
        float error = setpoint - pv;

        // 积分分离：偏差过大时不累积积分
        if (Mathf.Abs(error) > integralSeparationThreshold)
        {
            integral = 0f;
        }
        else
        {
            // 积分限幅：根据实际输出校正积分
            float computedOutput = (Kp * error) + (Ki * integral) + (Kd * (error - previousError) / dt);
            float outputError = computedOutput - actualOutput; // 输出偏差
            integral += (error * dt) - (outputError * Ki * dt); // 反馈校正

            // 限制积分范围
            integral = Mathf.Clamp(integral, integralMin, integralMax);
        }

        float derivative = (error - previousError) / dt;
        float output = (Kp * error) + (Ki * integral) + (Kd * derivative);
        output = Mathf.Clamp(output, outputMin, outputMax); // 限制输出
        previousError = error;
        return output;
    }
}

================================================================================
ROV移动控制日志
开始时间: 2025-08-05 14:19:17
Unity版本: 2022.3.55f1c1
场景名称: ROV
旋转日志: 启用
自适应朝向控制: 启用
旋转强度倍数: 2.0x
================================================================================

日志格式说明:
- 旋转事件: [时间戳] 旋转事件: 事件类型 - 详细信息 | 当前朝向 | 朝向误差
- 控制状态: 每10帧输出一次详细的控制状态信息
- 朝向控制模式: OnTarget(已对准), FineAdjustment(精细调整), AdaptiveRotation(自适应旋转), FastRotation(快速旋转)

[14:19:20.137] 旋转事件: 设置朝向目标 - 目标: (29.52, 2.74, 0.00), 当前: (19.52, 2.74, 0.00), 初始误差: 90.0° | 当前朝向: 0.0° | 朝向误差: 0.0°
[14:19:20.154] 旋转事件: 快速旋转模式 - 误差: 90.0°, 输出: 2.000 | 当前朝向: 0.0° | 朝向误差: 90.0°
[14:19:20.171] 旋转事件: 快速旋转模式 - 误差: 90.0°, 输出: 2.000 | 当前朝向: 0.0° | 朝向误差: 90.0°
[14:19:20.188] 旋转事件: 快速旋转模式 - 误差: 90.0°, 输出: 2.000 | 当前朝向: 360.0° | 朝向误差: 90.0°
[14:19:20.204] 旋转事件: 快速旋转模式 - 误差: 90.0°, 输出: 2.000 | 当前朝向: 360.0° | 朝向误差: 90.0°
[14:19:20.229] 旋转事件: 快速旋转模式 - 误差: 90.0°, 输出: 2.000 | 当前朝向: 360.0° | 朝向误差: 90.0°
[14:19:20.247] 旋转事件: 快速旋转模式 - 误差: 90.0°, 输出: 2.000 | 当前朝向: 360.0° | 朝向误差: 90.0°
[14:19:20.266] 旋转事件: 快速旋转模式 - 误差: 90.1°, 输出: 2.000 | 当前朝向: 359.9° | 朝向误差: 90.1°
[14:19:20.283] 旋转事件: 快速旋转模式 - 误差: 90.1°, 输出: 2.000 | 当前朝向: 359.9° | 朝向误差: 90.1°
[14:19:20.308] 旋转事件: 快速旋转模式 - 误差: 90.1°, 输出: 2.000 | 当前朝向: 359.9° | 朝向误差: 90.1°
[14:19:20.325] 旋转事件: 快速旋转模式 - 误差: 90.1°, 输出: 2.000 | 当前朝向: 359.9° | 朝向误差: 90.1°
=== ROV控制状态 Frame 100 Time 2.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.544, 2.735, 0.000)
位置误差向量: (9.981, 0.001, 0.000), 距离: 9.9805m
当前速度: (0.1814, -0.0056, -0.0006), 速度大小: 0.1815m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-30.159, Y:0.002, Z:0.000
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.025, Z=0.006, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 359.89, 359.52)
当前角速度: (-0.018, -0.967, -4.526)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 90.11°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.002, 0.000, 1.000)
目标方向: (1.000, 0.000, 0.000)
方向点积: -0.002
最终旋转输出: 0.000
==================================================

[14:19:20.344] 旋转事件: 快速旋转模式 - 误差: 90.1°, 输出: 2.000 | 当前朝向: 359.9° | 朝向误差: 90.1°
[14:19:20.369] 旋转事件: 快速旋转模式 - 误差: 90.1°, 输出: 2.000 | 当前朝向: 359.9° | 朝向误差: 90.1°
[14:19:20.389] 旋转事件: 快速旋转模式 - 误差: 90.2°, 输出: 2.000 | 当前朝向: 359.8° | 朝向误差: 90.2°
[14:19:20.406] 旋转事件: 快速旋转模式 - 误差: 90.2°, 输出: 2.000 | 当前朝向: 359.8° | 朝向误差: 90.2°
[14:19:20.424] 旋转事件: 快速旋转模式 - 误差: 90.2°, 输出: 2.000 | 当前朝向: 359.8° | 朝向误差: 90.2°
[14:19:20.447] 旋转事件: 快速旋转模式 - 误差: 90.2°, 输出: 2.000 | 当前朝向: 359.8° | 朝向误差: 90.2°
[14:19:20.465] 旋转事件: 快速旋转模式 - 误差: 90.3°, 输出: 2.000 | 当前朝向: 359.7° | 朝向误差: 90.3°
[14:19:20.483] 旋转事件: 快速旋转模式 - 误差: 90.3°, 输出: 2.000 | 当前朝向: 359.7° | 朝向误差: 90.3°
[14:19:20.508] 旋转事件: 快速旋转模式 - 误差: 90.3°, 输出: 2.000 | 当前朝向: 359.7° | 朝向误差: 90.3°
[14:19:20.525] 旋转事件: 快速旋转模式 - 误差: 90.3°, 输出: 2.000 | 当前朝向: 359.6° | 朝向误差: 90.3°
=== ROV控制状态 Frame 110 Time 2.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.594, 2.735, 0.000)
位置误差向量: (9.930, 0.002, 0.000), 距离: 9.9303m
当前速度: (0.2951, -0.0034, -0.0042), 速度大小: 0.2951m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-30.145, Y:0.001, Z:0.002
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.082, Z=0.019, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 359.65, 358.44)
当前角速度: (17639.980, -1.352, -5.522)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 90.35°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.006, 0.000, 1.000)
目标方向: (1.000, 0.000, 0.000)
方向点积: -0.006
最终旋转输出: 0.000
==================================================

[14:19:20.543] 旋转事件: 快速旋转模式 - 误差: 90.4°, 输出: 2.000 | 当前朝向: 359.6° | 朝向误差: 90.4°
[14:19:20.569] 旋转事件: 快速旋转模式 - 误差: 90.4°, 输出: 2.000 | 当前朝向: 359.6° | 朝向误差: 90.4°
[14:19:20.589] 旋转事件: 快速旋转模式 - 误差: 90.4°, 输出: 2.000 | 当前朝向: 359.6° | 朝向误差: 90.4°
[14:19:20.606] 旋转事件: 快速旋转模式 - 误差: 90.5°, 输出: 2.000 | 当前朝向: 359.5° | 朝向误差: 90.5°
[14:19:20.624] 旋转事件: 快速旋转模式 - 误差: 90.5°, 输出: 2.000 | 当前朝向: 359.5° | 朝向误差: 90.5°
[14:19:20.647] 旋转事件: 快速旋转模式 - 误差: 90.5°, 输出: 2.000 | 当前朝向: 359.5° | 朝向误差: 90.5°
[14:19:20.665] 旋转事件: 快速旋转模式 - 误差: 90.5°, 输出: 2.000 | 当前朝向: 359.5° | 朝向误差: 90.5°
[14:19:20.690] 旋转事件: 快速旋转模式 - 误差: 90.6°, 输出: 2.000 | 当前朝向: 359.4° | 朝向误差: 90.6°
[14:19:20.707] 旋转事件: 快速旋转模式 - 误差: 90.6°, 输出: 2.000 | 当前朝向: 359.4° | 朝向误差: 90.6°
[14:19:20.724] 旋转事件: 快速旋转模式 - 误差: 90.6°, 输出: 2.000 | 当前朝向: 359.4° | 朝向误差: 90.6°
=== ROV控制状态 Frame 120 Time 2.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.659, 2.735, -0.002)
位置误差向量: (9.865, 0.002, 0.002), 距离: 9.8651m
当前速度: (0.3453, 0.0037, -0.0114), 速度大小: 0.3455m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-30.010, Y:-0.003, Z:0.005
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.132, Z=0.033, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 359.37, 357.48)
当前角速度: (-0.017, -1.376, -4.024)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 90.62°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.011, 0.000, 1.000)
目标方向: (1.000, 0.000, 0.000)
方向点积: -0.011
最终旋转输出: 0.000
==================================================

[14:19:20.749] 旋转事件: 快速旋转模式 - 误差: 90.6°, 输出: 2.000 | 当前朝向: 359.3° | 朝向误差: 90.6°
[14:19:20.766] 旋转事件: 快速旋转模式 - 误差: 90.7°, 输出: 2.000 | 当前朝向: 359.3° | 朝向误差: 90.7°
[14:19:20.784] 旋转事件: 快速旋转模式 - 误差: 90.7°, 输出: 2.000 | 当前朝向: 359.3° | 朝向误差: 90.7°
[14:19:20.803] 旋转事件: 快速旋转模式 - 误差: 90.7°, 输出: 2.000 | 当前朝向: 359.3° | 朝向误差: 90.7°
[14:19:20.827] 旋转事件: 快速旋转模式 - 误差: 90.7°, 输出: 2.000 | 当前朝向: 359.2° | 朝向误差: 90.7°
[14:19:20.845] 旋转事件: 快速旋转模式 - 误差: 90.8°, 输出: 2.000 | 当前朝向: 359.2° | 朝向误差: 90.8°
[14:19:20.863] 旋转事件: 快速旋转模式 - 误差: 90.8°, 输出: 2.000 | 当前朝向: 359.2° | 朝向误差: 90.8°
[14:19:20.888] 旋转事件: 快速旋转模式 - 误差: 90.8°, 输出: 2.000 | 当前朝向: 359.2° | 朝向误差: 90.8°
[14:19:20.906] 旋转事件: 快速旋转模式 - 误差: 90.8°, 输出: 2.000 | 当前朝向: 359.1° | 朝向误差: 90.8°
[14:19:20.923] 旋转事件: 快速旋转模式 - 误差: 90.9°, 输出: 2.000 | 当前朝向: 359.1° | 朝向误差: 90.9°
=== ROV控制状态 Frame 130 Time 2.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.731, 2.736, -0.005)
位置误差向量: (9.793, 0.001, 0.005), 距离: 9.7933m
当前速度: (0.3675, 0.0137, -0.0221), 速度大小: 0.3685m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-29.821, Y:-0.007, Z:0.099
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.163, Z=0.057, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 359.10, 356.88)
当前角速度: (-0.032, -1.321, -2.222)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 90.86°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.016, 0.000, 1.000)
目标方向: (1.000, 0.000, 0.001)
方向点积: -0.015
最终旋转输出: 0.000
==================================================

[14:19:20.948] 旋转事件: 快速旋转模式 - 误差: 90.9°, 输出: 2.000 | 当前朝向: 359.1° | 朝向误差: 90.9°
[14:19:20.965] 旋转事件: 快速旋转模式 - 误差: 90.9°, 输出: 2.000 | 当前朝向: 359.1° | 朝向误差: 90.9°
[14:19:20.982] 旋转事件: 快速旋转模式 - 误差: 90.9°, 输出: 2.000 | 当前朝向: 359.0° | 朝向误差: 90.9°
[14:19:21.009] 旋转事件: 快速旋转模式 - 误差: 91.0°, 输出: 2.000 | 当前朝向: 359.0° | 朝向误差: 91.0°
[14:19:21.027] 旋转事件: 快速旋转模式 - 误差: 91.0°, 输出: 2.000 | 当前朝向: 359.0° | 朝向误差: 91.0°
[14:19:21.044] 旋转事件: 快速旋转模式 - 误差: 91.0°, 输出: 2.000 | 当前朝向: 358.9° | 朝向误差: 91.0°
[14:19:21.069] 旋转事件: 快速旋转模式 - 误差: 91.0°, 输出: 2.000 | 当前朝向: 358.9° | 朝向误差: 91.0°
[14:19:21.087] 旋转事件: 快速旋转模式 - 误差: 91.0°, 输出: 2.000 | 当前朝向: 358.9° | 朝向误差: 91.0°
[14:19:21.105] 旋转事件: 快速旋转模式 - 误差: 91.1°, 输出: 2.000 | 当前朝向: 358.9° | 朝向误差: 91.1°
[14:19:21.123] 旋转事件: 快速旋转模式 - 误差: 91.1°, 输出: 2.000 | 当前朝向: 358.8° | 朝向误差: 91.1°
=== ROV控制状态 Frame 140 Time 2.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.806, 2.740, -0.012)
位置误差向量: (9.719, -0.003, 0.012), 距离: 9.7185m
当前速度: (0.3783, 0.0247, -0.0384), 速度大小: 0.3811m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-29.610, Y:-0.118, Z:0.165
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.188, Z=0.076, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 358.85, 356.59)
当前角速度: (-0.029, -1.254, -0.867)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.08°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.020, 0.000, 1.000)
目标方向: (1.000, 0.000, 0.001)
方向点积: -0.019
最终旋转输出: 0.000
==================================================

[14:19:21.147] 旋转事件: 快速旋转模式 - 误差: 91.1°, 输出: 2.000 | 当前朝向: 358.8° | 朝向误差: 91.1°
[14:19:21.164] 旋转事件: 快速旋转模式 - 误差: 91.1°, 输出: 2.000 | 当前朝向: 358.8° | 朝向误差: 91.1°
[14:19:21.182] 旋转事件: 快速旋转模式 - 误差: 91.1°, 输出: 2.000 | 当前朝向: 358.8° | 朝向误差: 91.1°
[14:19:21.209] 旋转事件: 快速旋转模式 - 误差: 91.2°, 输出: 2.000 | 当前朝向: 358.7° | 朝向误差: 91.2°
[14:19:21.228] 旋转事件: 快速旋转模式 - 误差: 91.2°, 输出: 2.000 | 当前朝向: 358.7° | 朝向误差: 91.2°
[14:19:21.245] 旋转事件: 快速旋转模式 - 误差: 91.2°, 输出: 2.000 | 当前朝向: 358.7° | 朝向误差: 91.2°
[14:19:21.262] 旋转事件: 快速旋转模式 - 误差: 91.2°, 输出: 2.000 | 当前朝向: 358.7° | 朝向误差: 91.2°
[14:19:21.287] 旋转事件: 快速旋转模式 - 误差: 91.2°, 输出: 2.000 | 当前朝向: 358.6° | 朝向误差: 91.2°
[14:19:21.306] 旋转事件: 快速旋转模式 - 误差: 91.3°, 输出: 2.000 | 当前朝向: 358.6° | 朝向误差: 91.3°
[14:19:21.330] 旋转事件: 快速旋转模式 - 误差: 91.3°, 输出: 2.000 | 当前朝向: 358.6° | 朝向误差: 91.3°
=== ROV控制状态 Frame 150 Time 3.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.882, 2.746, -0.021)
位置误差向量: (9.642, -0.010, 0.021), 距离: 9.6422m
当前速度: (0.3841, 0.0350, -0.0559), 速度大小: 0.3898m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-29.388, Y:-0.153, Z:0.227
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.194, Z=0.094, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 358.60, 356.52)
当前角速度: (-0.017, -1.202, -0.069)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.27°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.024, 0.000, 1.000)
目标方向: (1.000, -0.001, 0.002)
方向点积: -0.022
最终旋转输出: 0.000
==================================================

[14:19:21.347] 旋转事件: 快速旋转模式 - 误差: 91.3°, 输出: 2.000 | 当前朝向: 358.6° | 朝向误差: 91.3°
[14:19:21.364] 旋转事件: 快速旋转模式 - 误差: 91.3°, 输出: 2.000 | 当前朝向: 358.6° | 朝向误差: 91.3°
[14:19:21.388] 旋转事件: 快速旋转模式 - 误差: 91.3°, 输出: 2.000 | 当前朝向: 358.5° | 朝向误差: 91.3°
[14:19:21.408] 旋转事件: 快速旋转模式 - 误差: 91.3°, 输出: 2.000 | 当前朝向: 358.5° | 朝向误差: 91.3°
[14:19:21.426] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.5° | 朝向误差: 91.4°
[14:19:21.443] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.5° | 朝向误差: 91.4°
[14:19:21.466] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.4° | 朝向误差: 91.4°
[14:19:21.484] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.4° | 朝向误差: 91.4°
[14:19:21.508] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.4° | 朝向误差: 91.4°
[14:19:21.525] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.4° | 朝向误差: 91.4°
=== ROV控制状态 Frame 160 Time 3.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.959, 2.754, -0.034)
位置误差向量: (9.565, -0.017, 0.034), 距离: 9.5650m
当前速度: (0.3874, 0.0427, -0.0733), 速度大小: 0.3966m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-29.160, Y:-0.170, Z:0.278
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.192, Z=0.111, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 358.36, 356.55)
当前角速度: (-0.023, -1.170, 0.305)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.43°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.029, 0.001, 1.000)
目标方向: (1.000, -0.002, 0.004)
方向点积: -0.025
最终旋转输出: 0.000
==================================================

[14:19:21.550] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.3° | 朝向误差: 91.4°
[14:19:21.567] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.3° | 朝向误差: 91.5°
[14:19:21.585] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.3° | 朝向误差: 91.5°
[14:19:21.610] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.3° | 朝向误差: 91.5°
[14:19:21.626] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.2° | 朝向误差: 91.5°
[14:19:21.643] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.2° | 朝向误差: 91.5°
[14:19:21.668] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.2° | 朝向误差: 91.5°
[14:19:21.685] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.2° | 朝向误差: 91.5°
[14:19:21.709] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.2° | 朝向误差: 91.5°
[14:19:21.725] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.1° | 朝向误差: 91.6°
=== ROV控制状态 Frame 170 Time 3.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.037, 2.764, -0.051)
位置误差向量: (9.487, -0.027, 0.051), 距离: 9.4874m
当前速度: (0.3896, 0.0475, -0.0898), 速度大小: 0.4026m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-28.929, Y:-0.168, Z:-0.045
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.187, Z=0.090, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 358.13, 356.63)
当前角速度: (-0.017, -1.155, 0.415)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.56°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.033, 0.001, 0.999)
目标方向: (1.000, -0.003, 0.005)
方向点积: -0.027
最终旋转输出: 0.000
==================================================

[14:19:21.749] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.1° | 朝向误差: 91.6°
[14:19:21.766] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.1° | 朝向误差: 91.6°
[14:19:21.784] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.1° | 朝向误差: 91.6°
[14:19:21.809] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.0° | 朝向误差: 91.6°
[14:19:21.826] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.0° | 朝向误差: 91.6°
[14:19:21.843] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.0° | 朝向误差: 91.6°
[14:19:21.870] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.0° | 朝向误差: 91.6°
[14:19:21.887] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.9° | 朝向误差: 91.7°
[14:19:21.905] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.9° | 朝向误差: 91.7°
[14:19:21.928] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.9° | 朝向误差: 91.7°
=== ROV控制状态 Frame 180 Time 3.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.115, 2.773, -0.069)
位置误差向量: (9.409, -0.036, 0.069), 距离: 9.4095m
当前速度: (0.3907, 0.0499, -0.0892), 速度大小: 0.4039m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-28.696, Y:-0.150, Z:-0.099
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.180, Z=0.095, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 357.90, 356.71)
当前角速度: (0.020, -1.163, 0.397)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.68°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.037, 0.000, 0.999)
目标方向: (1.000, -0.004, 0.007)
方向点积: -0.029
最终旋转输出: 0.000
==================================================

[14:19:21.946] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.9° | 朝向误差: 91.7°
[14:19:21.969] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.9° | 朝向误差: 91.7°
[14:19:21.986] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.8° | 朝向误差: 91.7°
[14:19:22.003] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.8° | 朝向误差: 91.7°
[14:19:22.023] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.8° | 朝向误差: 91.7°
[14:19:22.049] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.8° | 朝向误差: 91.8°
[14:19:22.067] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.7° | 朝向误差: 91.8°
[14:19:22.085] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.7° | 朝向误差: 91.8°
[14:19:22.104] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.7° | 朝向误差: 91.8°
[14:19:22.127] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.7° | 朝向误差: 91.8°
=== ROV控制状态 Frame 190 Time 3.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.194, 2.783, -0.087)
位置误差向量: (9.331, -0.046, 0.087), 距离: 9.3315m
当前速度: (0.3917, 0.0506, -0.0907), 速度大小: 0.4052m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-28.463, Y:-0.124, Z:-0.151
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.172, Z=0.101, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 357.67, 356.78)
当前角速度: (-0.031, -1.163, 0.325)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.80°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.041, 0.001, 0.999)
目标方向: (1.000, -0.005, 0.009)
方向点积: -0.031
最终旋转输出: 0.000
==================================================

[14:19:22.145] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.6° | 朝向误差: 91.8°
[14:19:22.162] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.6° | 朝向误差: 91.8°
[14:19:22.188] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.6° | 朝向误差: 91.8°
[14:19:22.207] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.6° | 朝向误差: 91.8°
[14:19:22.226] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.6° | 朝向误差: 91.9°
[14:19:22.244] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.5° | 朝向误差: 91.9°
[14:19:22.269] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.5° | 朝向误差: 91.9°
[14:19:22.288] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.5° | 朝向误差: 91.9°
[14:19:22.306] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.5° | 朝向误差: 91.9°
[14:19:22.323] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.4° | 朝向误差: 91.9°
=== ROV控制状态 Frame 200 Time 4.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.272, 2.793, -0.105)
位置误差向量: (9.253, -0.056, 0.105), 距离: 9.2533m
当前速度: (0.3923, 0.0483, -0.0926), 速度大小: 0.4060m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-28.228, Y:0.111, Z:-0.204
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.145, Z=0.106, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 357.44, 356.84)
当前角速度: (0.014, -1.157, 0.232)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.91°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.045, 0.000, 0.999)
目标方向: (1.000, -0.006, 0.011)
方向点积: -0.033
最终旋转输出: 0.000
==================================================

[14:19:22.349] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.4° | 朝向误差: 91.9°
[14:19:22.367] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.4° | 朝向误差: 91.9°
[14:19:22.384] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.4° | 朝向误差: 91.9°
[14:19:22.403] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.3° | 朝向误差: 92.0°
[14:19:22.430] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.3° | 朝向误差: 92.0°
[14:19:22.448] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.3° | 朝向误差: 92.0°
[14:19:22.465] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.3° | 朝向误差: 92.0°
[14:19:22.483] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.3° | 朝向误差: 92.0°
[14:19:22.510] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.2° | 朝向误差: 92.0°
[14:19:22.528] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.2° | 朝向误差: 92.0°
=== ROV控制状态 Frame 210 Time 4.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.350, 2.803, -0.124)
位置误差向量: (9.174, -0.066, 0.124), 距离: 9.1751m
当前速度: (0.3929, 0.0450, -0.0956), 速度大小: 0.4069m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-27.994, Y:0.143, Z:-0.257
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.138, Z=0.111, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 357.21, 356.87)
当前角速度: (-0.017, -1.151, 0.154)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.02°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.049, 0.001, 0.999)
目标方向: (1.000, -0.007, 0.013)
方向点积: -0.035
最终旋转输出: 0.000
==================================================

[14:19:22.549] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.2° | 朝向误差: 92.0°
[14:19:22.566] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.2° | 朝向误差: 92.0°
[14:19:22.584] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.1° | 朝向误差: 92.1°
[14:19:22.608] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.1° | 朝向误差: 92.1°
[14:19:22.628] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.1° | 朝向误差: 92.1°
[14:19:22.644] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.1° | 朝向误差: 92.1°
[14:19:22.663] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.0° | 朝向误差: 92.1°
[14:19:22.688] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.0° | 朝向误差: 92.1°
[14:19:22.706] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.0° | 朝向误差: 92.1°
[14:19:22.724] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.0° | 朝向误差: 92.1°
=== ROV控制状态 Frame 220 Time 4.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.429, 2.811, -0.143)
位置误差向量: (9.095, -0.074, 0.143), 距离: 9.0968m
当前速度: (0.3933, 0.0419, -0.0984), 速度大小: 0.4076m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-27.758, Y:0.173, Z:-0.312
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.133, Z=0.115, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 356.98, 356.90)
当前角速度: (0.003, -1.144, 0.092)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.12°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.053, 0.001, 0.999)
目标方向: (1.000, -0.008, 0.016)
方向点积: -0.037
最终旋转输出: 0.000
==================================================

[14:19:22.743] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.0° | 朝向误差: 92.1°
[14:19:22.770] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 356.9° | 朝向误差: 92.1°
[14:19:22.789] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.9° | 朝向误差: 92.2°
[14:19:22.807] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.9° | 朝向误差: 92.2°
[14:19:22.825] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.9° | 朝向误差: 92.2°
[14:19:22.845] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.8° | 朝向误差: 92.2°
[14:19:22.869] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.8° | 朝向误差: 92.2°
[14:19:22.886] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.8° | 朝向误差: 92.2°
[14:19:22.904] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.8° | 朝向误差: 92.2°
[14:19:22.927] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.7° | 朝向误差: 92.2°
=== ROV控制状态 Frame 230 Time 4.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.508, 2.819, -0.163)
位置误差向量: (9.017, -0.082, 0.163), 距离: 9.0185m
当前速度: (0.3937, 0.0392, -0.1013), 速度大小: 0.4084m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-27.522, Y:0.200, Z:-0.368
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.128, Z=0.119, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 356.75, 356.91)
当前角速度: (-0.008, -1.140, 0.047)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.22°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.057, 0.001, 0.998)
目标方向: (1.000, -0.009, 0.018)
方向点积: -0.039
最终旋转输出: 0.000
==================================================

[14:19:22.945] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.7° | 朝向误差: 92.2°
[14:19:22.968] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.7° | 朝向误差: 92.2°
[14:19:22.985] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.7° | 朝向误差: 92.2°
[14:19:23.003] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.7° | 朝向误差: 92.3°
[14:19:23.029] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.6° | 朝向误差: 92.3°
[14:19:23.049] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.6° | 朝向误差: 92.3°
[14:19:23.066] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.6° | 朝向误差: 92.3°
[14:19:23.084] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.6° | 朝向误差: 92.3°
[14:19:23.109] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.5° | 朝向误差: 92.3°
[14:19:23.126] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.5° | 朝向误差: 92.3°
=== ROV控制状态 Frame 240 Time 4.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.587, 2.827, -0.184)
位置误差向量: (8.938, -0.090, 0.184), 距离: 8.9402m
当前速度: (0.3941, 0.0367, -0.1039), 速度大小: 0.4092m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-27.287, Y:0.226, Z:-0.427
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.124, Z=0.123, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 356.52, 356.92)
当前角速度: (0.000, -1.134, 0.017)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.30°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.061, 0.001, 0.998)
目标方向: (1.000, -0.010, 0.021)
方向点积: -0.040
最终旋转输出: 0.000
==================================================

[14:19:23.143] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.5° | 朝向误差: 92.3°
[14:19:23.169] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.5° | 朝向误差: 92.3°
[14:19:23.187] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.5° | 朝向误差: 92.3°
[14:19:23.206] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.4° | 朝向误差: 92.3°
[14:19:23.223] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.4° | 朝向误差: 92.3°
[14:19:23.242] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.4° | 朝向误差: 92.4°
[14:19:23.266] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.4° | 朝向误差: 92.4°
[14:19:23.284] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.3° | 朝向误差: 92.4°
[14:19:23.310] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.3° | 朝向误差: 92.4°
[14:19:23.327] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.3° | 朝向误差: 92.4°
=== ROV控制状态 Frame 250 Time 5.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.665, 2.834, -0.205)
位置误差向量: (8.859, -0.097, 0.205), 距离: 8.8620m
当前速度: (0.3943, 0.0345, -0.1062), 速度大小: 0.4099m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-27.050, Y:0.250, Z:-0.487
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.120, Z=0.126, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 356.29, 356.92)
当前角速度: (-0.003, -1.129, -0.002)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.38°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.065, 0.001, 0.998)
目标方向: (1.000, -0.011, 0.023)
方向点积: -0.042
最终旋转输出: 0.000
==================================================

[14:19:23.343] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.3° | 朝向误差: 92.4°
[14:19:23.367] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.2° | 朝向误差: 92.4°
[14:19:23.383] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.2° | 朝向误差: 92.4°
[14:19:23.408] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.2° | 朝向误差: 92.4°
[14:19:23.424] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.2° | 朝向误差: 92.4°
[14:19:23.443] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.2° | 朝向误差: 92.4°
[14:19:23.466] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.1° | 朝向误差: 92.4°
[14:19:23.484] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.1° | 朝向误差: 92.4°
[14:19:23.509] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.1° | 朝向误差: 92.4°
[14:19:23.527] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 356.1° | 朝向误差: 92.5°
=== ROV控制状态 Frame 260 Time 5.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.744, 2.841, -0.226)
位置误差向量: (8.780, -0.104, 0.226), 距离: 8.7837m
当前速度: (0.3947, 0.0325, -0.1082), 速度大小: 0.4106m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-26.814, Y:0.272, Z:-0.550
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.117, Z=0.129, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 356.07, 356.92)
当前角速度: (-0.002, -1.126, -0.012)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.45°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.069, 0.001, 0.998)
目标方向: (1.000, -0.012, 0.026)
方向点积: -0.043
最终旋转输出: 0.000
==================================================

[14:19:23.543] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 356.0° | 朝向误差: 92.5°
[14:19:23.570] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 356.0° | 朝向误差: 92.5°
[14:19:23.588] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 356.0° | 朝向误差: 92.5°
[14:19:23.605] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 356.0° | 朝向误差: 92.5°
[14:19:23.623] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 356.0° | 朝向误差: 92.5°
[14:19:23.646] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.9° | 朝向误差: 92.5°
[14:19:23.665] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.9° | 朝向误差: 92.5°
[14:19:23.689] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.9° | 朝向误差: 92.5°
[14:19:23.705] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.9° | 朝向误差: 92.5°
[14:19:23.724] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.8° | 朝向误差: 92.5°
=== ROV控制状态 Frame 270 Time 5.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.823, 2.847, -0.248)
位置误差向量: (8.701, -0.110, 0.248), 距离: 8.7054m
当前速度: (0.3950, 0.0306, -0.1097), 速度大小: 0.4111m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-26.578, Y:0.294, Z:-0.613
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.114, Z=0.131, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 355.84, 356.91)
当前角速度: (-0.002, -1.122, -0.018)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.52°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.072, 0.001, 0.997)
目标方向: (1.000, -0.013, 0.028)
方向点积: -0.044
最终旋转输出: 0.000
==================================================

[14:19:23.748] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.8° | 朝向误差: 92.5°
[14:19:23.769] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.8° | 朝向误差: 92.5°

================================================================================
日志结束时间: 2025-08-05 14:19:24
================================================================================

================================================================================
ROV移动控制日志
开始时间: 2025-08-05 14:12:00
Unity版本: 2022.3.55f1c1
场景名称: ROV
旋转日志: 启用
自适应朝向控制: 启用
旋转强度倍数: 2.0x
================================================================================

日志格式说明:
- 旋转事件: [时间戳] 旋转事件: 事件类型 - 详细信息 | 当前朝向 | 朝向误差
- 控制状态: 每10帧输出一次详细的控制状态信息
- 朝向控制模式: OnTarget(已对准), FineAdjustment(精细调整), AdaptiveRotation(自适应旋转), FastRotation(快速旋转)

[14:12:03.632] 旋转事件: 设置朝向目标 - 目标: (29.52, 2.74, 0.00), 当前: (19.53, 2.74, 0.00), 初始误差: 90.0° | 当前朝向: 0.0° | 朝向误差: 0.0°
[14:12:03.646] 旋转事件: 快速旋转模式 - 误差: 90.0°, 输出: 2.000 | 当前朝向: 0.0° | 朝向误差: 90.0°
[14:12:03.664] 旋转事件: 快速旋转模式 - 误差: 90.0°, 输出: 2.000 | 当前朝向: 0.0° | 朝向误差: 90.0°
[14:12:03.682] 旋转事件: 快速旋转模式 - 误差: 90.0°, 输出: 2.000 | 当前朝向: 360.0° | 朝向误差: 90.0°
[14:12:03.706] 旋转事件: 快速旋转模式 - 误差: 90.0°, 输出: 2.000 | 当前朝向: 360.0° | 朝向误差: 90.0°
[14:12:03.724] 旋转事件: 快速旋转模式 - 误差: 90.0°, 输出: 2.000 | 当前朝向: 360.0° | 朝向误差: 90.0°
[14:12:03.741] 旋转事件: 快速旋转模式 - 误差: 90.0°, 输出: 2.000 | 当前朝向: 360.0° | 朝向误差: 90.0°
[14:12:03.768] 旋转事件: 快速旋转模式 - 误差: 90.1°, 输出: 2.000 | 当前朝向: 359.9° | 朝向误差: 90.1°
[14:12:03.785] 旋转事件: 快速旋转模式 - 误差: 90.1°, 输出: 2.000 | 当前朝向: 359.9° | 朝向误差: 90.1°
=== ROV控制状态 Frame 110 Time 2.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.538, 2.734, 0.000)
位置误差向量: (9.987, 0.003, 0.000), 距离: 9.9866m
当前速度: (0.1493, -0.0078, -0.0003), 速度大小: 0.1495m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-30.139, Y:0.003, Z:0.000
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.015, Z=0.004, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 359.93, 359.70)
当前角速度: (-0.007, -0.807, -3.828)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 90.07°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.001, 0.000, 1.000)
目标方向: (1.000, 0.000, 0.000)
方向点积: -0.001
最终旋转输出: 0.000
==================================================

[14:12:03.803] 旋转事件: 快速旋转模式 - 误差: 90.1°, 输出: 2.000 | 当前朝向: 359.9° | 朝向误差: 90.1°
[14:12:03.820] 旋转事件: 快速旋转模式 - 误差: 90.1°, 输出: 2.000 | 当前朝向: 359.9° | 朝向误差: 90.1°
[14:12:03.847] 旋转事件: 快速旋转模式 - 误差: 90.1°, 输出: 2.000 | 当前朝向: 359.9° | 朝向误差: 90.1°
[14:12:03.864] 旋转事件: 快速旋转模式 - 误差: 90.1°, 输出: 2.000 | 当前朝向: 359.9° | 朝向误差: 90.1°
[14:12:03.882] 旋转事件: 快速旋转模式 - 误差: 90.2°, 输出: 2.000 | 当前朝向: 359.8° | 朝向误差: 90.2°
[14:12:03.907] 旋转事件: 快速旋转模式 - 误差: 90.2°, 输出: 2.000 | 当前朝向: 359.8° | 朝向误差: 90.2°
[14:12:03.925] 旋转事件: 快速旋转模式 - 误差: 90.2°, 输出: 2.000 | 当前朝向: 359.8° | 朝向误差: 90.2°
[14:12:03.942] 旋转事件: 快速旋转模式 - 误差: 90.2°, 输出: 2.000 | 当前朝向: 359.8° | 朝向误差: 90.2°
[14:12:03.966] 旋转事件: 快速旋转模式 - 误差: 90.3°, 输出: 2.000 | 当前朝向: 359.7° | 朝向误差: 90.3°
[14:12:03.984] 旋转事件: 快速旋转模式 - 误差: 90.3°, 输出: 2.000 | 当前朝向: 359.7° | 朝向误差: 90.3°
=== ROV控制状态 Frame 120 Time 2.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.584, 2.733, 0.000)
位置误差向量: (9.941, 0.004, 0.000), 距离: 9.9409m
当前速度: (0.2797, -0.0055, -0.0032), 速度大小: 0.2798m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-30.158, Y:0.002, Z:0.002
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.070, Z=0.016, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (-0.01, 359.70, 358.66)
当前角速度: (-0.030, -1.315, -5.646)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 90.30°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.005, 0.000, 1.000)
目标方向: (1.000, 0.000, 0.000)
方向点积: -0.005
最终旋转输出: 0.000
==================================================

[14:12:04.002] 旋转事件: 快速旋转模式 - 误差: 90.3°, 输出: 2.000 | 当前朝向: 359.7° | 朝向误差: 90.3°
[14:12:04.027] 旋转事件: 快速旋转模式 - 误差: 90.3°, 输出: 2.000 | 当前朝向: 359.6° | 朝向误差: 90.3°
[14:12:04.045] 旋转事件: 快速旋转模式 - 误差: 90.4°, 输出: 2.000 | 当前朝向: 359.6° | 朝向误差: 90.4°
[14:12:04.063] 旋转事件: 快速旋转模式 - 误差: 90.4°, 输出: 2.000 | 当前朝向: 359.6° | 朝向误差: 90.4°
[14:12:04.081] 旋转事件: 快速旋转模式 - 误差: 90.4°, 输出: 2.000 | 当前朝向: 359.6° | 朝向误差: 90.4°
[14:12:04.105] 旋转事件: 快速旋转模式 - 误差: 90.5°, 输出: 2.000 | 当前朝向: 359.5° | 朝向误差: 90.5°
[14:12:04.123] 旋转事件: 快速旋转模式 - 误差: 90.5°, 输出: 2.000 | 当前朝向: 359.5° | 朝向误差: 90.5°
[14:12:04.141] 旋转事件: 快速旋转模式 - 误差: 90.5°, 输出: 2.000 | 当前朝向: 359.5° | 朝向误差: 90.5°
[14:12:04.166] 旋转事件: 快速旋转模式 - 误差: 90.5°, 输出: 2.000 | 当前朝向: 359.5° | 朝向误差: 90.5°
[14:12:04.184] 旋转事件: 快速旋转模式 - 误差: 90.6°, 输出: 2.000 | 当前朝向: 359.4° | 朝向误差: 90.6°
=== ROV控制状态 Frame 130 Time 2.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.647, 2.732, -0.002)
位置误差向量: (9.878, 0.005, 0.002), 距离: 9.8777m
当前速度: (0.3385, 0.0014, -0.0097), 速度大小: 0.3386m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-30.039, Y:-0.002, Z:0.005
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.123, Z=0.030, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 359.43, 357.65)
当前角速度: (-0.015, -1.381, -4.398)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 90.56°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.010, 0.000, 1.000)
目标方向: (1.000, 0.000, 0.000)
方向点积: -0.010
最终旋转输出: 0.000
==================================================

[14:12:04.201] 旋转事件: 快速旋转模式 - 误差: 90.6°, 输出: 2.000 | 当前朝向: 359.4° | 朝向误差: 90.6°
[14:12:04.233] 旋转事件: 快速旋转模式 - 误差: 90.6°, 输出: 2.000 | 当前朝向: 359.4° | 朝向误差: 90.6°
[14:12:04.244] 旋转事件: 快速旋转模式 - 误差: 90.6°, 输出: 2.000 | 当前朝向: 359.3° | 朝向误差: 90.6°
[14:12:04.262] 旋转事件: 快速旋转模式 - 误差: 90.7°, 输出: 2.000 | 当前朝向: 359.3° | 朝向误差: 90.7°
[14:12:04.286] 旋转事件: 快速旋转模式 - 误差: 90.7°, 输出: 2.000 | 当前朝向: 359.3° | 朝向误差: 90.7°
[14:12:04.304] 旋转事件: 快速旋转模式 - 误差: 90.7°, 输出: 2.000 | 当前朝向: 359.3° | 朝向误差: 90.7°
[14:12:04.322] 旋转事件: 快速旋转模式 - 误差: 90.7°, 输出: 2.000 | 当前朝向: 359.2° | 朝向误差: 90.7°
[14:12:04.340] 旋转事件: 快速旋转模式 - 误差: 90.8°, 输出: 2.000 | 当前朝向: 359.2° | 朝向误差: 90.8°
[14:12:04.365] 旋转事件: 快速旋转模式 - 误差: 90.8°, 输出: 2.000 | 当前朝向: 359.2° | 朝向误差: 90.8°
[14:12:04.391] 旋转事件: 快速旋转模式 - 误差: 90.8°, 输出: 2.000 | 当前朝向: 359.2° | 朝向误差: 90.8°
=== ROV控制状态 Frame 140 Time 2.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.718, 2.734, -0.005)
位置误差向量: (9.807, 0.003, 0.005), 距离: 9.8068m
当前速度: (0.3642, 0.0115, -0.0194), 速度大小: 0.3649m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-29.857, Y:-0.007, Z:0.009
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.158, Z=0.045, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 359.16, 356.97)
当前角速度: (-0.021, -1.334, -2.557)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 90.82°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.015, 0.000, 1.000)
目标方向: (1.000, 0.000, 0.000)
方向点积: -0.014
最终旋转输出: 0.000
==================================================

[14:12:04.402] 旋转事件: 快速旋转模式 - 误差: 90.8°, 输出: 2.000 | 当前朝向: 359.1° | 朝向误差: 90.8°
[14:12:04.427] 旋转事件: 快速旋转模式 - 误差: 90.9°, 输出: 2.000 | 当前朝向: 359.1° | 朝向误差: 90.9°
[14:12:04.443] 旋转事件: 快速旋转模式 - 误差: 90.9°, 输出: 2.000 | 当前朝向: 359.1° | 朝向误差: 90.9°
[14:12:04.462] 旋转事件: 快速旋转模式 - 误差: 90.9°, 输出: 2.000 | 当前朝向: 359.1° | 朝向误差: 90.9°
[14:12:04.487] 旋转事件: 快速旋转模式 - 误差: 90.9°, 输出: 2.000 | 当前朝向: 359.0° | 朝向误差: 90.9°
[14:12:04.504] 旋转事件: 快速旋转模式 - 误差: 91.0°, 输出: 2.000 | 当前朝向: 359.0° | 朝向误差: 91.0°
[14:12:04.522] 旋转事件: 快速旋转模式 - 误差: 91.0°, 输出: 2.000 | 当前朝向: 359.0° | 朝向误差: 91.0°
[14:12:04.548] 旋转事件: 快速旋转模式 - 误差: 91.0°, 输出: 2.000 | 当前朝向: 358.9° | 朝向误差: 91.0°
[14:12:04.565] 旋转事件: 快速旋转模式 - 误差: 91.0°, 输出: 2.000 | 当前朝向: 358.9° | 朝向误差: 91.0°
[14:12:04.583] 旋转事件: 快速旋转模式 - 误差: 91.0°, 输出: 2.000 | 当前朝向: 358.9° | 朝向误差: 91.0°
=== ROV控制状态 Frame 150 Time 3.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.792, 2.737, -0.010)
位置误差向量: (9.733, 0.000, 0.010), 距离: 9.7325m
当前速度: (0.3762, 0.0229, -0.0350), 速度大小: 0.3785m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-29.649, Y:-0.119, Z:0.152
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.186, Z=0.072, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 358.90, 356.63)
当前角速度: (-0.038, -1.266, -1.089)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.04°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.019, 0.000, 1.000)
目标方向: (1.000, 0.000, 0.001)
方向点积: -0.018
最终旋转输出: 0.000
==================================================

[14:12:04.607] 旋转事件: 快速旋转模式 - 误差: 91.1°, 输出: 2.000 | 当前朝向: 358.9° | 朝向误差: 91.1°
[14:12:04.624] 旋转事件: 快速旋转模式 - 误差: 91.1°, 输出: 2.000 | 当前朝向: 358.8° | 朝向误差: 91.1°
[14:12:04.642] 旋转事件: 快速旋转模式 - 误差: 91.1°, 输出: 2.000 | 当前朝向: 358.8° | 朝向误差: 91.1°
[14:12:04.661] 旋转事件: 快速旋转模式 - 误差: 91.1°, 输出: 2.000 | 当前朝向: 358.8° | 朝向误差: 91.1°
[14:12:04.686] 旋转事件: 快速旋转模式 - 误差: 91.1°, 输出: 2.000 | 当前朝向: 358.8° | 朝向误差: 91.1°
[14:12:04.702] 旋转事件: 快速旋转模式 - 误差: 91.2°, 输出: 2.000 | 当前朝向: 358.7° | 朝向误差: 91.2°
[14:12:04.720] 旋转事件: 快速旋转模式 - 误差: 91.2°, 输出: 2.000 | 当前朝向: 358.7° | 朝向误差: 91.2°
[14:12:04.745] 旋转事件: 快速旋转模式 - 误差: 91.2°, 输出: 2.000 | 当前朝向: 358.7° | 朝向误差: 91.2°
[14:12:04.763] 旋转事件: 快速旋转模式 - 误差: 91.2°, 输出: 2.000 | 当前朝向: 358.7° | 朝向误差: 91.2°
[14:12:04.781] 旋转事件: 快速旋转模式 - 误差: 91.2°, 输出: 2.000 | 当前朝向: 358.7° | 朝向误差: 91.2°
=== ROV控制状态 Frame 160 Time 3.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.868, 2.743, -0.019)
位置误差向量: (9.656, -0.006, 0.019), 距离: 9.6565m
当前速度: (0.3829, 0.0338, -0.0522), 速度大小: 0.3879m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-29.429, Y:-0.158, Z:0.215
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.194, Z=0.091, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 358.65, 356.52)
当前角速度: (-0.015, -1.215, -0.192)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.24°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.024, 0.000, 1.000)
目标方向: (1.000, -0.001, 0.002)
方向点积: -0.022
最终旋转输出: 0.000
==================================================

[14:12:04.806] 旋转事件: 快速旋转模式 - 误差: 91.3°, 输出: 2.000 | 当前朝向: 358.6° | 朝向误差: 91.3°
[14:12:04.822] 旋转事件: 快速旋转模式 - 误差: 91.3°, 输出: 2.000 | 当前朝向: 358.6° | 朝向误差: 91.3°
[14:12:04.840] 旋转事件: 快速旋转模式 - 误差: 91.3°, 输出: 2.000 | 当前朝向: 358.6° | 朝向误差: 91.3°
[14:12:04.866] 旋转事件: 快速旋转模式 - 误差: 91.3°, 输出: 2.000 | 当前朝向: 358.6° | 朝向误差: 91.3°
[14:12:04.885] 旋转事件: 快速旋转模式 - 误差: 91.3°, 输出: 2.000 | 当前朝向: 358.5° | 朝向误差: 91.3°
[14:12:04.902] 旋转事件: 快速旋转模式 - 误差: 91.3°, 输出: 2.000 | 当前朝向: 358.5° | 朝向误差: 91.3°
[14:12:04.926] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.5° | 朝向误差: 91.4°
[14:12:04.942] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.5° | 朝向误差: 91.4°
[14:12:04.960] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.4° | 朝向误差: 91.4°
[14:12:04.987] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.4° | 朝向误差: 91.4°
=== ROV控制状态 Frame 170 Time 3.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (19.945, 2.751, -0.031)
位置误差向量: (9.579, -0.014, 0.031), 距离: 9.5795m
当前速度: (0.3867, 0.0420, -0.0698), 速度大小: 0.3952m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-29.202, Y:-0.177, Z:0.269
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.194, Z=0.108, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 358.41, 356.54)
当前角速度: (-0.026, -1.176, 0.253)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.40°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.028, 0.001, 1.000)
目标方向: (1.000, -0.001, 0.003)
方向点积: -0.024
最终旋转输出: 0.000
==================================================

[14:12:05.013] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.4° | 朝向误差: 91.4°
[14:12:05.023] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.4° | 朝向误差: 91.4°
[14:12:05.040] 旋转事件: 快速旋转模式 - 误差: 91.4°, 输出: 2.000 | 当前朝向: 358.3° | 朝向误差: 91.4°
[14:12:05.065] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.3° | 朝向误差: 91.5°
[14:12:05.082] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.3° | 朝向误差: 91.5°
[14:12:05.106] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.3° | 朝向误差: 91.5°
[14:12:05.124] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.2° | 朝向误差: 91.5°
[14:12:05.142] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.2° | 朝向误差: 91.5°
[14:12:05.168] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.2° | 朝向误差: 91.5°
[14:12:05.185] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.2° | 朝向误差: 91.5°
=== ROV控制状态 Frame 180 Time 3.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.023, 2.760, -0.047)
位置误差向量: (9.502, -0.023, 0.047), 距离: 9.5020m
当前速度: (0.3891, 0.0473, -0.0865), 速度大小: 0.4014m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-28.972, Y:-0.178, Z:0.308
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.189, Z=0.123, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 358.18, 356.61)
当前角速度: (-0.020, -1.160, 0.403)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.54°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.032, 0.001, 0.999)
目标方向: (1.000, -0.002, 0.005)
方向点积: -0.027
最终旋转输出: 0.000
==================================================

[14:12:05.203] 旋转事件: 快速旋转模式 - 误差: 91.5°, 输出: 2.000 | 当前朝向: 358.2° | 朝向误差: 91.5°
[14:12:05.220] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.1° | 朝向误差: 91.6°
[14:12:05.245] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.1° | 朝向误差: 91.6°
[14:12:05.264] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.1° | 朝向误差: 91.6°
[14:12:05.280] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.1° | 朝向误差: 91.6°
[14:12:05.305] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.0° | 朝向误差: 91.6°
[14:12:05.323] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.0° | 朝向误差: 91.6°
[14:12:05.347] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.0° | 朝向误差: 91.6°
[14:12:05.364] 旋转事件: 快速旋转模式 - 误差: 91.6°, 输出: 2.000 | 当前朝向: 358.0° | 朝向误差: 91.6°
[14:12:05.381] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.9° | 朝向误差: 91.7°
=== ROV控制状态 Frame 190 Time 3.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.101, 2.769, -0.065)
位置误差向量: (9.424, -0.032, 0.065), 距离: 9.4241m
当前速度: (0.3904, 0.0500, -0.0888), 速度大小: 0.4035m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-28.740, Y:-0.163, Z:-0.088
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.182, Z=0.094, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 357.95, 356.69)
当前角速度: (0.069, -1.163, 0.403)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.66°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.036, 0.000, 0.999)
目标方向: (1.000, -0.003, 0.007)
方向点积: -0.029
最终旋转输出: 0.000
==================================================

[14:12:05.405] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.9° | 朝向误差: 91.7°
[14:12:05.422] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.9° | 朝向误差: 91.7°
[14:12:05.446] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.9° | 朝向误差: 91.7°
[14:12:05.474] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.9° | 朝向误差: 91.7°
[14:12:05.484] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.8° | 朝向误差: 91.7°
[14:12:05.501] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.8° | 朝向误差: 91.7°
[14:12:05.526] 旋转事件: 快速旋转模式 - 误差: 91.7°, 输出: 2.000 | 当前朝向: 357.8° | 朝向误差: 91.7°
[14:12:05.543] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.8° | 朝向误差: 91.8°
[14:12:05.567] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.7° | 朝向误差: 91.8°
[14:12:05.585] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.7° | 朝向误差: 91.8°
=== ROV控制状态 Frame 200 Time 4.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.179, 2.780, -0.083)
位置误差向量: (9.346, -0.043, 0.083), 距离: 9.3461m
当前速度: (0.3915, 0.0509, -0.0904), 速度大小: 0.4050m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-28.507, Y:-0.137, Z:-0.140
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.174, Z=0.100, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 357.72, 356.77)
当前角速度: (-0.053, -1.163, 0.342)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.78°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.040, 0.000, 0.999)
目标方向: (1.000, -0.005, 0.009)
方向点积: -0.031
最终旋转输出: 0.000
==================================================

[14:12:05.602] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.7° | 朝向误差: 91.8°
[14:12:05.627] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.7° | 朝向误差: 91.8°
[14:12:05.644] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.6° | 朝向误差: 91.8°
[14:12:05.663] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.6° | 朝向误差: 91.8°
[14:12:05.687] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.6° | 朝向误差: 91.8°
[14:12:05.704] 旋转事件: 快速旋转模式 - 误差: 91.8°, 输出: 2.000 | 当前朝向: 357.6° | 朝向误差: 91.8°
[14:12:05.722] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.6° | 朝向误差: 91.9°
[14:12:05.745] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.5° | 朝向误差: 91.9°
[14:12:05.764] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.5° | 朝向误差: 91.9°
[14:12:05.782] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.5° | 朝向误差: 91.9°
=== ROV控制状态 Frame 210 Time 4.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.257, 2.790, -0.101)
位置误差向量: (9.267, -0.053, 0.101), 距离: 9.2679m
当前速度: (0.3922, 0.0500, -0.0920), 速度大小: 0.4060m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-28.272, Y:0.098, Z:-0.193
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.147, Z=0.105, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 357.48, 356.83)
当前角速度: (0.023, -1.158, 0.249)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 91.89°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.044, 0.000, 0.999)
目标方向: (1.000, -0.006, 0.011)
方向点积: -0.033
最终旋转输出: 0.000
==================================================

[14:12:05.808] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.5° | 朝向误差: 91.9°
[14:12:05.825] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.4° | 朝向误差: 91.9°
[14:12:05.842] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.4° | 朝向误差: 91.9°
[14:12:05.866] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.4° | 朝向误差: 91.9°
[14:12:05.884] 旋转事件: 快速旋转模式 - 误差: 91.9°, 输出: 2.000 | 当前朝向: 357.4° | 朝向误差: 91.9°
[14:12:05.901] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.3° | 朝向误差: 92.0°
[14:12:05.933] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.3° | 朝向误差: 92.0°
[14:12:05.943] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.3° | 朝向误差: 92.0°
[14:12:05.967] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.3° | 朝向误差: 92.0°
[14:12:05.984] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.3° | 朝向误差: 92.0°
=== ROV控制状态 Frame 220 Time 4.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.336, 2.799, -0.120)
位置误差向量: (9.189, -0.062, 0.120), 距离: 9.1897m
当前速度: (0.3928, 0.0463, -0.0950), 速度大小: 0.4068m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-28.038, Y:0.131, Z:-0.246
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.140, Z=0.110, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 357.25, 356.87)
当前角速度: (-0.020, -1.154, 0.168)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.00°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.048, 0.000, 0.999)
目标方向: (1.000, -0.007, 0.013)
方向点积: -0.035
最终旋转输出: 0.000
==================================================

[14:12:06.002] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.2° | 朝向误差: 92.0°
[14:12:06.027] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.2° | 朝向误差: 92.0°
[14:12:06.044] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.2° | 朝向误差: 92.0°
[14:12:06.062] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 357.2° | 朝向误差: 92.0°
[14:12:06.087] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.1° | 朝向误差: 92.1°
[14:12:06.105] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.1° | 朝向误差: 92.1°
[14:12:06.122] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.1° | 朝向误差: 92.1°
[14:12:06.146] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.1° | 朝向误差: 92.1°
[14:12:06.164] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.0° | 朝向误差: 92.1°
[14:12:06.182] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.0° | 朝向误差: 92.1°
=== ROV控制状态 Frame 230 Time 4.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.414, 2.808, -0.139)
位置误差向量: (9.110, -0.071, 0.139), 距离: 9.1115m
当前速度: (0.3932, 0.0430, -0.0977), 速度大小: 0.4074m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-27.802, Y:0.162, Z:-0.300
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.134, Z=0.114, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 357.02, 356.89)
当前角速度: (0.003, -1.146, 0.102)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.10°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.052, 0.001, 0.999)
目标方向: (1.000, -0.008, 0.015)
方向点积: -0.037
最终旋转输出: 0.000
==================================================

[14:12:06.207] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.0° | 朝向误差: 92.1°
[14:12:06.224] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.0° | 朝向误差: 92.1°
[14:12:06.242] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 357.0° | 朝向误差: 92.1°
[14:12:06.266] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 356.9° | 朝向误差: 92.1°
[14:12:06.283] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.9° | 朝向误差: 92.2°
[14:12:06.300] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.9° | 朝向误差: 92.2°
[14:12:06.325] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.9° | 朝向误差: 92.2°
[14:12:06.342] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.8° | 朝向误差: 92.2°
[14:12:06.360] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.8° | 朝向误差: 92.2°
[14:12:06.395] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.8° | 朝向误差: 92.2°
=== ROV控制状态 Frame 240 Time 4.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.493, 2.816, -0.159)
位置误差向量: (9.031, -0.079, 0.159), 距离: 9.0332m
当前速度: (0.3936, 0.0401, -0.1007), 速度大小: 0.4082m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-27.567, Y:0.190, Z:-0.357
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.130, Z=0.119, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 356.79, 356.91)
当前角速度: (-0.006, -1.140, 0.055)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.20°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.056, 0.001, 0.998)
目标方向: (1.000, -0.009, 0.018)
方向点积: -0.038
最终旋转输出: 0.000
==================================================

[14:12:06.405] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.8° | 朝向误差: 92.2°
[14:12:06.422] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.7° | 朝向误差: 92.2°
[14:12:06.446] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.7° | 朝向误差: 92.2°
[14:12:06.464] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.7° | 朝向误差: 92.2°
[14:12:06.482] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 356.7° | 朝向误差: 92.2°
[14:12:06.507] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.7° | 朝向误差: 92.3°
[14:12:06.523] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.6° | 朝向误差: 92.3°
[14:12:06.549] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.6° | 朝向误差: 92.3°
[14:12:06.565] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.6° | 朝向误差: 92.3°
[14:12:06.583] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.6° | 朝向误差: 92.3°
=== ROV控制状态 Frame 250 Time 5.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.572, 2.824, -0.179)
位置误差向量: (8.953, -0.087, 0.179), 距离: 8.9549m
当前速度: (0.3940, 0.0375, -0.1034), 速度大小: 0.4090m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-27.331, Y:0.217, Z:-0.415
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.125, Z=0.122, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 356.57, 356.91)
当前角速度: (-0.002, -1.135, 0.023)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.29°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.060, 0.001, 0.998)
目标方向: (1.000, -0.010, 0.020)
方向点积: -0.040
最终旋转输出: 0.000
==================================================

[14:12:06.601] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.5° | 朝向误差: 92.3°
[14:12:06.624] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.5° | 朝向误差: 92.3°
[14:12:06.641] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.5° | 朝向误差: 92.3°
[14:12:06.665] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.5° | 朝向误差: 92.3°
[14:12:06.682] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.5° | 朝向误差: 92.3°
[14:12:06.707] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.4° | 朝向误差: 92.3°
[14:12:06.725] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 356.4° | 朝向误差: 92.3°
[14:12:06.742] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.4° | 朝向误差: 92.4°
[14:12:06.766] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.4° | 朝向误差: 92.4°
[14:12:06.783] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.3° | 朝向误差: 92.4°
=== ROV控制状态 Frame 260 Time 5.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.651, 2.831, -0.200)
位置误差向量: (8.874, -0.094, 0.200), 距离: 8.8766m
当前速度: (0.3942, 0.0352, -0.1058), 速度大小: 0.4097m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-27.095, Y:0.241, Z:-0.475
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.122, Z=0.126, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 356.34, 356.92)
当前角速度: (-0.003, -1.129, 0.003)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.37°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.064, 0.001, 0.998)
目标方向: (1.000, -0.011, 0.023)
方向点积: -0.041
最终旋转输出: 0.000
==================================================

[14:12:06.801] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.3° | 朝向误差: 92.4°
[14:12:06.825] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.3° | 朝向误差: 92.4°
[14:12:06.842] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.3° | 朝向误差: 92.4°
[14:12:06.862] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.2° | 朝向误差: 92.4°
[14:12:06.887] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.2° | 朝向误差: 92.4°
[14:12:06.904] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.2° | 朝向误差: 92.4°
[14:12:06.921] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.2° | 朝向误差: 92.4°
[14:12:06.945] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.2° | 朝向误差: 92.4°
[14:12:06.962] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.1° | 朝向误差: 92.4°
[14:12:06.988] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.1° | 朝向误差: 92.4°
=== ROV控制状态 Frame 270 Time 5.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.730, 2.838, -0.222)
位置误差向量: (8.795, -0.101, 0.222), 距离: 8.7983m
当前速度: (0.3946, 0.0331, -0.1078), 速度大小: 0.4104m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-26.858, Y:0.264, Z:-0.537
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.118, Z=0.128, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 356.11, 356.92)
当前角速度: (-0.002, -1.126, -0.012)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.44°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.068, 0.001, 0.998)
目标方向: (1.000, -0.012, 0.025)
方向点积: -0.043
最终旋转输出: 0.000
==================================================

[14:12:07.013] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 356.1° | 朝向误差: 92.4°
[14:12:07.023] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 356.1° | 朝向误差: 92.5°
[14:12:07.041] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 356.0° | 朝向误差: 92.5°
[14:12:07.066] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 356.0° | 朝向误差: 92.5°
[14:12:07.084] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 356.0° | 朝向误差: 92.5°
[14:12:07.100] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 356.0° | 朝向误差: 92.5°
[14:12:07.125] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 356.0° | 朝向误差: 92.5°
[14:12:07.142] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.9° | 朝向误差: 92.5°
[14:12:07.166] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.9° | 朝向误差: 92.5°
[14:12:07.186] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.9° | 朝向误差: 92.5°
=== ROV控制状态 Frame 280 Time 5.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.808, 2.845, -0.244)
位置误差向量: (8.716, -0.108, 0.244), 距离: 8.7201m
当前速度: (0.3950, 0.0312, -0.1094), 速度大小: 0.4111m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-26.622, Y:0.286, Z:-0.600
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.115, Z=0.131, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 355.89, 356.91)
当前角速度: (0.000, -1.123, -0.018)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.51°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.072, 0.001, 0.997)
目标方向: (1.000, -0.012, 0.028)
方向点积: -0.044
最终旋转输出: 0.000
==================================================

[14:12:07.203] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.9° | 朝向误差: 92.5°
[14:12:07.221] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.8° | 朝向误差: 92.5°
[14:12:07.245] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.8° | 朝向误差: 92.5°
[14:12:07.262] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.8° | 朝向误差: 92.5°
[14:12:07.286] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.8° | 朝向误差: 92.5°
[14:12:07.303] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 355.8° | 朝向误差: 92.5°
[14:12:07.322] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.7° | 朝向误差: 92.6°
[14:12:07.345] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.7° | 朝向误差: 92.6°
[14:12:07.362] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.7° | 朝向误差: 92.6°
[14:12:07.387] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.7° | 朝向误差: 92.6°
=== ROV控制状态 Frame 290 Time 5.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.887, 2.851, -0.266)
位置误差向量: (8.637, -0.114, 0.266), 距离: 8.6418m
当前速度: (0.3953, 0.0294, -0.1107), 速度大小: 0.4116m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-26.385, Y:0.306, Z:-0.665
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.112, Z=0.133, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 355.67, 356.91)
当前角速度: (-0.002, -1.118, -0.021)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.57°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.076, 0.001, 0.997)
目标方向: (0.999, -0.013, 0.031)
方向点积: -0.045
最终旋转输出: 0.000
==================================================

[14:12:07.405] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.6° | 朝向误差: 92.6°
[14:12:07.422] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.6° | 朝向误差: 92.6°
[14:12:07.446] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.6° | 朝向误差: 92.6°
[14:12:07.472] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.6° | 朝向误差: 92.6°
[14:12:07.482] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.6° | 朝向误差: 92.6°
[14:12:07.505] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.5° | 朝向误差: 92.6°
[14:12:07.522] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.5° | 朝向误差: 92.6°
[14:12:07.546] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.5° | 朝向误差: 92.6°
[14:12:07.563] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.5° | 朝向误差: 92.6°
[14:12:07.581] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.4° | 朝向误差: 92.6°
=== ROV控制状态 Frame 300 Time 6.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (20.967, 2.856, -0.288)
位置误差向量: (8.558, -0.119, 0.288), 距离: 8.5635m
当前速度: (0.3957, 0.0278, -0.1117), 速度大小: 0.4121m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-26.148, Y:0.325, Z:-0.730
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.109, Z=0.135, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 355.44, 356.90)
当前角速度: (0.000, -1.117, -0.023)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.63°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.079, 0.001, 0.997)
目标方向: (0.999, -0.014, 0.034)
方向点积: -0.046
最终旋转输出: 0.000
==================================================

[14:12:07.606] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.4° | 朝向误差: 92.6°
[14:12:07.624] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.4° | 朝向误差: 92.6°
[14:12:07.641] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 355.4° | 朝向误差: 92.6°
[14:12:07.665] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.4° | 朝向误差: 92.7°
[14:12:07.683] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.3° | 朝向误差: 92.7°
[14:12:07.706] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.3° | 朝向误差: 92.7°
[14:12:07.723] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.3° | 朝向误差: 92.7°
[14:12:07.747] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.3° | 朝向误差: 92.7°
[14:12:07.772] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.2° | 朝向误差: 92.7°
[14:12:07.782] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.2° | 朝向误差: 92.7°
=== ROV控制状态 Frame 310 Time 6.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (21.046, 2.862, -0.310)
位置误差向量: (8.479, -0.125, 0.310), 距离: 8.4853m
当前速度: (0.3961, 0.0263, -0.1123), 速度大小: 0.4125m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-25.911, Y:0.343, Z:-0.797
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.106, Z=0.137, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 355.22, 356.90)
当前角速度: (0.000, -1.114, -0.024)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.69°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.083, 0.001, 0.997)
目标方向: (0.999, -0.015, 0.037)
方向点积: -0.047
最终旋转输出: 0.000
==================================================

[14:12:07.800] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.2° | 朝向误差: 92.7°
[14:12:07.826] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.2° | 朝向误差: 92.7°
[14:12:07.844] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.2° | 朝向误差: 92.7°
[14:12:07.861] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.1° | 朝向误差: 92.7°
[14:12:07.885] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.1° | 朝向误差: 92.7°
[14:12:07.902] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.1° | 朝向误差: 92.7°
[14:12:07.928] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.1° | 朝向误差: 92.7°
[14:12:07.945] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.0° | 朝向误差: 92.7°
[14:12:07.962] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.0° | 朝向误差: 92.7°
[14:12:07.988] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.0° | 朝向误差: 92.7°
=== ROV控制状态 Frame 320 Time 6.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (21.125, 2.867, -0.333)
位置误差向量: (8.399, -0.130, 0.333), 距离: 8.4070m
当前速度: (0.3964, 0.0248, -0.1125), 速度大小: 0.4129m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-25.674, Y:0.360, Z:-0.864
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.103, Z=0.138, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 355.00, 356.89)
当前角速度: (0.000, -1.112, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.74°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.087, 0.001, 0.996)
目标方向: (0.999, -0.015, 0.040)
方向点积: -0.048
最终旋转输出: 0.000
==================================================

[14:12:08.005] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.0° | 朝向误差: 92.7°
[14:12:08.022] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 355.0° | 朝向误差: 92.7°
[14:12:08.046] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 354.9° | 朝向误差: 92.7°
[14:12:08.062] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.9° | 朝向误差: 92.8°
[14:12:08.082] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.9° | 朝向误差: 92.8°
[14:12:08.106] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.9° | 朝向误差: 92.8°
[14:12:08.125] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.8° | 朝向误差: 92.8°
[14:12:08.142] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.8° | 朝向误差: 92.8°
[14:12:08.165] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.8° | 朝向误差: 92.8°
[14:12:08.183] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.8° | 朝向误差: 92.8°
=== ROV控制状态 Frame 330 Time 6.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (21.204, 2.872, -0.355)
位置误差向量: (8.320, -0.135, 0.355), 距离: 8.3288m
当前速度: (0.3968, 0.0235, -0.1125), 速度大小: 0.4131m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-25.437, Y:0.376, Z:-0.932
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.100, Z=0.139, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 354.77, 356.89)
当前角速度: (0.000, -1.111, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.78°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.091, 0.001, 0.996)
目标方向: (0.999, -0.016, 0.043)
方向点积: -0.049
最终旋转输出: 0.000
==================================================

[14:12:08.200] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.8° | 朝向误差: 92.8°
[14:12:08.233] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.7° | 朝向误差: 92.8°
[14:12:08.242] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.7° | 朝向误差: 92.8°
[14:12:08.266] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.7° | 朝向误差: 92.8°
[14:12:08.284] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.7° | 朝向误差: 92.8°
[14:12:08.301] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.6° | 朝向误差: 92.8°
[14:12:08.325] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.6° | 朝向误差: 92.8°
[14:12:08.342] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.6° | 朝向误差: 92.8°
[14:12:08.367] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.6° | 朝向误差: 92.8°
[14:12:08.386] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.6° | 朝向误差: 92.8°
=== ROV控制状态 Frame 340 Time 6.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (21.284, 2.876, -0.378)
位置误差向量: (8.241, -0.139, 0.378), 距离: 8.2506m
当前速度: (0.3972, 0.0222, -0.1123), 速度大小: 0.4134m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-25.199, Y:0.392, Z:-1.000
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.098, Z=0.140, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 354.55, 356.88)
当前角速度: (-0.002, -1.109, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.82°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.095, 0.001, 0.995)
目标方向: (0.999, -0.017, 0.046)
方向点积: -0.049
最终旋转输出: 0.000
==================================================

[14:12:08.403] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.5° | 朝向误差: 92.8°
[14:12:08.420] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.5° | 朝向误差: 92.8°
[14:12:08.445] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.5° | 朝向误差: 92.8°
[14:12:08.463] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.5° | 朝向误差: 92.8°
[14:12:08.487] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.4° | 朝向误差: 92.8°
[14:12:08.505] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 354.4° | 朝向误差: 92.8°
[14:12:08.521] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.4° | 朝向误差: 92.9°
[14:12:08.547] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.4° | 朝向误差: 92.9°
[14:12:08.564] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.4° | 朝向误差: 92.9°
[14:12:08.581] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.3° | 朝向误差: 92.9°
=== ROV控制状态 Frame 350 Time 7.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (21.363, 2.881, -0.400)
位置误差向量: (8.161, -0.144, 0.400), 距离: 8.1723m
当前速度: (0.3976, 0.0210, -0.1118), 速度大小: 0.4135m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-24.961, Y:0.406, Z:-1.068
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.095, Z=0.140, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 354.33, 356.88)
当前角速度: (0.000, -1.109, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.86°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.099, 0.001, 0.995)
目标方向: (0.999, -0.018, 0.049)
方向点积: -0.050
最终旋转输出: 0.000
==================================================

[14:12:08.605] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.3° | 朝向误差: 92.9°
[14:12:08.622] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.3° | 朝向误差: 92.9°
[14:12:08.647] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.3° | 朝向误差: 92.9°
[14:12:08.664] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.2° | 朝向误差: 92.9°
[14:12:08.690] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.2° | 朝向误差: 92.9°
[14:12:08.707] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.2° | 朝向误差: 92.9°
[14:12:08.723] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.2° | 朝向误差: 92.9°
[14:12:08.741] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.2° | 朝向误差: 92.9°
[14:12:08.766] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.1° | 朝向误差: 92.9°
[14:12:08.784] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.1° | 朝向误差: 92.9°
=== ROV控制状态 Frame 360 Time 7.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (21.443, 2.885, -0.422)
位置误差向量: (8.082, -0.148, 0.422), 距离: 8.0941m
当前速度: (0.3980, 0.0198, -0.1111), 速度大小: 0.4137m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-24.723, Y:0.420, Z:-1.136
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.093, Z=0.141, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 354.11, 356.87)
当前角速度: (0.000, -1.109, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.90°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.103, 0.001, 0.995)
目标方向: (0.998, -0.018, 0.052)
方向点积: -0.051
最终旋转输出: 0.000
==================================================

[14:12:08.803] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.1° | 朝向误差: 92.9°
[14:12:08.826] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.1° | 朝向误差: 92.9°
[14:12:08.847] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.0° | 朝向误差: 92.9°
[14:12:08.865] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.0° | 朝向误差: 92.9°
[14:12:08.884] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.0° | 朝向误差: 92.9°
[14:12:08.901] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.0° | 朝向误差: 92.9°
[14:12:08.926] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 354.0° | 朝向误差: 92.9°
[14:12:08.945] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 353.9° | 朝向误差: 92.9°
[14:12:08.964] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 353.9° | 朝向误差: 92.9°
[14:12:08.982] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 353.9° | 朝向误差: 92.9°
=== ROV控制状态 Frame 370 Time 7.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (21.522, 2.888, -0.445)
位置误差向量: (8.002, -0.151, 0.445), 距离: 8.0158m
当前速度: (0.3983, 0.0187, -0.1102), 速度大小: 0.4137m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-24.484, Y:0.433, Z:-1.203
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.091, Z=0.141, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 353.89, 356.87)
当前角速度: (0.002, -1.108, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.93°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.107, 0.001, 0.994)
目标方向: (0.998, -0.019, 0.055)
方向点积: -0.051
最终旋转输出: 0.000
==================================================

[14:12:09.001] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 353.9° | 朝向误差: 92.9°
[14:12:09.027] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 353.8° | 朝向误差: 92.9°
[14:12:09.046] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 353.8° | 朝向误差: 92.9°
[14:12:09.064] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 353.8° | 朝向误差: 92.9°
[14:12:09.083] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 353.8° | 朝向误差: 92.9°
[14:12:09.102] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.8° | 朝向误差: 93.0°
[14:12:09.120] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.7° | 朝向误差: 93.0°
[14:12:09.154] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.7° | 朝向误差: 93.0°
[14:12:09.166] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.7° | 朝向误差: 93.0°
[14:12:09.185] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.7° | 朝向误差: 93.0°
=== ROV控制状态 Frame 380 Time 7.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (21.602, 2.892, -0.466)
位置误差向量: (7.922, -0.155, 0.466), 距离: 7.9376m
当前速度: (0.3986, 0.0176, -0.1091), 速度大小: 0.4137m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-24.245, Y:0.445, Z:-1.270
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.088, Z=0.141, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 353.67, 356.86)
当前角速度: (0.002, -1.106, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.97°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.110, 0.001, 0.994)
目标方向: (0.998, -0.020, 0.059)
方向点积: -0.052
最终旋转输出: 0.000
==================================================

[14:12:09.204] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.6° | 朝向误差: 93.0°
[14:12:09.221] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.6° | 朝向误差: 93.0°
[14:12:09.246] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.6° | 朝向误差: 93.0°
[14:12:09.264] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.6° | 朝向误差: 93.0°
[14:12:09.282] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.6° | 朝向误差: 93.0°
[14:12:09.308] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.5° | 朝向误差: 93.0°
[14:12:09.327] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.5° | 朝向误差: 93.0°
[14:12:09.346] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.5° | 朝向误差: 93.0°
[14:12:09.364] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.5° | 朝向误差: 93.0°
[14:12:09.383] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.4° | 朝向误差: 93.0°
=== ROV控制状态 Frame 390 Time 7.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (21.682, 2.895, -0.488)
位置误差向量: (7.843, -0.158, 0.488), 距离: 7.8594m
当前速度: (0.3991, 0.0166, -0.1079), 速度大小: 0.4138m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-24.007, Y:0.456, Z:-1.337
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.086, Z=0.141, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 353.44, 356.86)
当前角速度: (0.000, -1.106, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.99°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.114, 0.001, 0.993)
目标方向: (0.998, -0.020, 0.062)
方向点积: -0.052
最终旋转输出: 0.000
==================================================

[14:12:09.401] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.4° | 朝向误差: 93.0°
[14:12:09.427] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.4° | 朝向误差: 93.0°
[14:12:09.446] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.4° | 朝向误差: 93.0°
[14:12:09.466] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.4° | 朝向误差: 93.0°
[14:12:09.491] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.3° | 朝向误差: 93.0°
[14:12:09.502] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.3° | 朝向误差: 93.0°
[14:12:09.520] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.3° | 朝向误差: 93.0°
[14:12:09.547] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.3° | 朝向误差: 93.0°
[14:12:09.564] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.2° | 朝向误差: 93.0°
[14:12:09.582] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.2° | 朝向误差: 93.0°
=== ROV控制状态 Frame 400 Time 8.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (21.762, 2.899, -0.510)
位置误差向量: (7.763, -0.162, 0.510), 距离: 7.7811m
当前速度: (0.3995, 0.0156, -0.1065), 速度大小: 0.4138m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-23.768, Y:0.467, Z:-1.403
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.084, Z=0.141, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 353.22, 356.85)
当前角速度: (0.002, -1.106, -0.027)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.02°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.118, 0.001, 0.993)
目标方向: (0.998, -0.021, 0.065)
方向点积: -0.053
最终旋转输出: 0.000
==================================================

[14:12:09.601] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.2° | 朝向误差: 93.0°
[14:12:09.627] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.2° | 朝向误差: 93.0°
[14:12:09.645] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.2° | 朝向误差: 93.0°
[14:12:09.662] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.1° | 朝向误差: 93.0°
[14:12:09.681] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.1° | 朝向误差: 93.0°
[14:12:09.707] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.1° | 朝向误差: 93.0°
[14:12:09.728] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.1° | 朝向误差: 93.0°
[14:12:09.748] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.0° | 朝向误差: 93.0°
[14:12:09.777] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.0° | 朝向误差: 93.0°
[14:12:09.789] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.0° | 朝向误差: 93.0°
=== ROV控制状态 Frame 410 Time 8.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (21.842, 2.902, -0.531)
位置误差向量: (7.683, -0.165, 0.531), 距离: 7.7029m
当前速度: (0.3998, 0.0146, -0.1050), 速度大小: 0.4136m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-23.528, Y:0.478, Z:-1.469
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.082, Z=0.141, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 353.00, 356.85)
当前角速度: (0.000, -1.106, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.05°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.122, 0.001, 0.993)
目标方向: (0.997, -0.021, 0.069)
方向点积: -0.053
最终旋转输出: 0.000
==================================================

[14:12:09.802] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 353.0° | 朝向误差: 93.0°
[14:12:09.820] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 353.0° | 朝向误差: 93.1°
[14:12:09.847] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.9° | 朝向误差: 93.1°
[14:12:09.867] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.9° | 朝向误差: 93.1°
[14:12:09.888] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.9° | 朝向误差: 93.1°
[14:12:09.905] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.9° | 朝向误差: 93.1°
[14:12:09.932] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.8° | 朝向误差: 93.1°
[14:12:09.943] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.8° | 朝向误差: 93.1°
[14:12:09.962] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.8° | 朝向误差: 93.1°
[14:12:09.987] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.8° | 朝向误差: 93.1°
=== ROV控制状态 Frame 420 Time 8.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (21.922, 2.905, -0.552)
位置误差向量: (7.603, -0.168, 0.552), 距离: 7.6246m
当前速度: (0.4002, 0.0137, -0.1034), 速度大小: 0.4135m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-23.289, Y:0.487, Z:-1.533
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.080, Z=0.141, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 352.78, 356.84)
当前角速度: (0.002, -1.106, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.07°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.126, 0.001, 0.992)
目标方向: (0.997, -0.022, 0.072)
方向点积: -0.054
最终旋转输出: 0.000
==================================================

[14:12:10.006] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.8° | 朝向误差: 93.1°
[14:12:10.023] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.7° | 朝向误差: 93.1°
[14:12:10.043] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.7° | 朝向误差: 93.1°
[14:12:10.064] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.7° | 朝向误差: 93.1°
[14:12:10.094] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.7° | 朝向误差: 93.1°
[14:12:10.106] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.6° | 朝向误差: 93.1°
[14:12:10.125] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.6° | 朝向误差: 93.1°
[14:12:10.144] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.6° | 朝向误差: 93.1°
[14:12:10.161] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.6° | 朝向误差: 93.1°
[14:12:10.186] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.6° | 朝向误差: 93.1°
=== ROV控制状态 Frame 430 Time 8.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.002, 2.907, -0.572)
位置误差向量: (7.523, -0.170, 0.572), 距离: 7.5464m
当前速度: (0.4005, 0.0128, -0.1017), 速度大小: 0.4135m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-23.049, Y:0.496, Z:-1.597
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.078, Z=0.140, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 352.56, 356.84)
当前角速度: (0.000, -1.108, -0.027)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.09°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.130, 0.001, 0.992)
目标方向: (0.997, -0.023, 0.076)
方向点积: -0.054
最终旋转输出: 0.000
==================================================

[14:12:10.201] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.5° | 朝向误差: 93.1°
[14:12:10.228] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.5° | 朝向误差: 93.1°
[14:12:10.248] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.5° | 朝向误差: 93.1°
[14:12:10.261] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.5° | 朝向误差: 93.1°
[14:12:10.287] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.4° | 朝向误差: 93.1°
[14:12:10.305] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.4° | 朝向误差: 93.1°
[14:12:10.322] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.4° | 朝向误差: 93.1°
[14:12:10.347] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.4° | 朝向误差: 93.1°
[14:12:10.367] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.4° | 朝向误差: 93.1°
[14:12:10.385] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.3° | 朝向误差: 93.1°
=== ROV控制状态 Frame 440 Time 8.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.082, 2.910, -0.592)
位置误差向量: (7.443, -0.173, 0.592), 距离: 7.4681m
当前速度: (0.4008, 0.0119, -0.0999), 速度大小: 0.4133m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-22.809, Y:0.505, Z:-1.660
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.076, Z=0.140, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 352.34, 356.83)
当前角速度: (0.002, -1.106, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.11°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.133, 0.001, 0.991)
目标方向: (0.997, -0.023, 0.079)
方向点积: -0.054
最终旋转输出: 0.000
==================================================

[14:12:10.403] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.3° | 朝向误差: 93.1°
[14:12:10.424] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.3° | 朝向误差: 93.1°
[14:12:10.442] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.3° | 朝向误差: 93.1°
[14:12:10.467] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.2° | 朝向误差: 93.1°
[14:12:10.485] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.2° | 朝向误差: 93.1°
[14:12:10.505] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.2° | 朝向误差: 93.1°
[14:12:10.522] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.2° | 朝向误差: 93.1°
[14:12:10.553] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.2° | 朝向误差: 93.1°
[14:12:10.564] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.1° | 朝向误差: 93.1°
[14:12:10.582] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.1° | 朝向误差: 93.1°
=== ROV控制状态 Frame 450 Time 9.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.162, 2.912, -0.612)
位置误差向量: (7.362, -0.175, 0.612), 距离: 7.3899m
当前速度: (0.4011, 0.0111, -0.0980), 速度大小: 0.4131m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-22.569, Y:0.513, Z:-1.722
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.074, Z=0.139, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 352.12, 356.83)
当前角速度: (0.000, -1.108, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.13°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.137, 0.001, 0.991)
目标方向: (0.996, -0.024, 0.083)
方向点积: -0.055
最终旋转输出: 0.000
==================================================

[14:12:10.607] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.1° | 朝向误差: 93.1°
[14:12:10.626] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.1° | 朝向误差: 93.1°
[14:12:10.643] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.0° | 朝向误差: 93.1°
[14:12:10.667] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.0° | 朝向误差: 93.1°
[14:12:10.684] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.0° | 朝向误差: 93.1°
[14:12:10.710] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.0° | 朝向误差: 93.1°
[14:12:10.726] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 352.0° | 朝向误差: 93.1°
[14:12:10.744] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 351.9° | 朝向误差: 93.1°
[14:12:10.761] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 351.9° | 朝向误差: 93.1°
[14:12:10.785] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.9° | 朝向误差: 93.2°
=== ROV控制状态 Frame 460 Time 9.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.242, 2.914, -0.631)
位置误差向量: (7.282, -0.177, 0.631), 距离: 7.3116m
当前速度: (0.4014, 0.0102, -0.0960), 速度大小: 0.4128m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-22.328, Y:0.520, Z:-1.783
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.072, Z=0.138, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 351.89, 356.82)
当前角速度: (0.002, -1.108, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.15°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.141, 0.001, 0.990)
目标方向: (0.996, -0.024, 0.086)
方向点积: -0.055
最终旋转输出: 0.000
==================================================

[14:12:10.803] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.9° | 朝向误差: 93.2°
[14:12:10.821] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.9° | 朝向误差: 93.2°
[14:12:10.846] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.8° | 朝向误差: 93.2°
[14:12:10.865] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.8° | 朝向误差: 93.2°
[14:12:10.883] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.8° | 朝向误差: 93.2°
[14:12:10.906] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.8° | 朝向误差: 93.2°
[14:12:10.924] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.7° | 朝向误差: 93.2°
[14:12:10.942] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.7° | 朝向误差: 93.2°
[14:12:10.968] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.7° | 朝向误差: 93.2°
[14:12:10.986] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.7° | 朝向误差: 93.2°
=== ROV控制状态 Frame 470 Time 9.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.323, 2.916, -0.650)
位置误差向量: (7.202, -0.179, 0.650), 距离: 7.2333m
当前速度: (0.4017, 0.0094, -0.0939), 速度大小: 0.4126m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-22.087, Y:0.527, Z:-1.842
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.070, Z=0.138, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 351.67, 356.82)
当前角速度: (0.002, -1.108, -0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.17°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.145, 0.001, 0.989)
目标方向: (0.996, -0.025, 0.090)
方向点积: -0.055
最终旋转输出: 0.000
==================================================

[14:12:11.014] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.7° | 朝向误差: 93.2°
[14:12:11.026] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.6° | 朝向误差: 93.2°
[14:12:11.043] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.6° | 朝向误差: 93.2°
[14:12:11.061] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.6° | 朝向误差: 93.2°
[14:12:11.087] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.6° | 朝向误差: 93.2°
[14:12:11.105] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.5° | 朝向误差: 93.2°
[14:12:11.124] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.5° | 朝向误差: 93.2°
[14:12:11.142] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.5° | 朝向误差: 93.2°
[14:12:11.168] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.5° | 朝向误差: 93.2°
[14:12:11.180] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.5° | 朝向误差: 93.2°
=== ROV控制状态 Frame 480 Time 9.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.403, 2.918, -0.669)
位置误差向量: (7.121, -0.181, 0.669), 距离: 7.1551m
当前速度: (0.4020, 0.0086, -0.0918), 速度大小: 0.4124m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-21.847, Y:0.534, Z:-1.901
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.069, Z=0.137, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 351.45, 356.81)
当前角速度: (0.002, -1.109, -0.024)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.18°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.149, 0.001, 0.989)
目标方向: (0.995, -0.025, 0.093)
方向点积: -0.056
最终旋转输出: 0.000
==================================================

[14:12:11.206] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.4° | 朝向误差: 93.2°
[14:12:11.226] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.4° | 朝向误差: 93.2°
[14:12:11.244] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.4° | 朝向误差: 93.2°
[14:12:11.262] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.4° | 朝向误差: 93.2°
[14:12:11.281] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.3° | 朝向误差: 93.2°
[14:12:11.307] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.3° | 朝向误差: 93.2°
[14:12:11.326] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.3° | 朝向误差: 93.2°
[14:12:11.344] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.3° | 朝向误差: 93.2°
[14:12:11.363] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.3° | 朝向误差: 93.2°
[14:12:11.381] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.2° | 朝向误差: 93.2°
=== ROV控制状态 Frame 490 Time 9.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.483, 2.919, -0.687)
位置误差向量: (7.041, -0.182, 0.687), 距离: 7.0768m
当前速度: (0.4023, 0.0079, -0.0896), 速度大小: 0.4122m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-21.606, Y:0.539, Z:-1.958
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.067, Z=0.136, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 351.23, 356.81)
当前角速度: (0.002, -1.109, -0.024)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.20°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.152, 0.001, 0.988)
目标方向: (0.995, -0.026, 0.097)
方向点积: -0.056
最终旋转输出: 0.000
==================================================

[14:12:11.406] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.2° | 朝向误差: 93.2°
[14:12:11.430] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.2° | 朝向误差: 93.2°
[14:12:11.442] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.2° | 朝向误差: 93.2°
[14:12:11.474] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.1° | 朝向误差: 93.2°
[14:12:11.483] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.1° | 朝向误差: 93.2°
[14:12:11.502] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.1° | 朝向误差: 93.2°
[14:12:11.527] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.1° | 朝向误差: 93.2°
[14:12:11.544] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.1° | 朝向误差: 93.2°
[14:12:11.562] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.0° | 朝向误差: 93.2°
[14:12:11.586] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.0° | 朝向误差: 93.2°
=== ROV控制状态 Frame 500 Time 10.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.564, 2.921, -0.705)
位置误差向量: (6.961, -0.184, 0.705), 距离: 6.9985m
当前速度: (0.4025, 0.0071, -0.0873), 速度大小: 0.4120m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-21.365, Y:0.545, Z:-2.014
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.065, Z=0.135, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 351.01, 356.80)
当前角速度: (0.002, -1.111, -0.024)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.21°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.156, 0.001, 0.988)
目标方向: (0.995, -0.026, 0.101)
方向点积: -0.056
最终旋转输出: 0.000
==================================================

[14:12:11.604] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.0° | 朝向误差: 93.2°
[14:12:11.630] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 351.0° | 朝向误差: 93.2°
[14:12:11.642] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.9° | 朝向误差: 93.2°
[14:12:11.668] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.9° | 朝向误差: 93.2°
[14:12:11.685] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.9° | 朝向误差: 93.2°
[14:12:11.703] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.9° | 朝向误差: 93.2°
[14:12:11.720] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.9° | 朝向误差: 93.2°
[14:12:11.746] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.8° | 朝向误差: 93.2°
[14:12:11.764] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.8° | 朝向误差: 93.2°
[14:12:11.790] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.8° | 朝向误差: 93.2°
=== ROV控制状态 Frame 510 Time 10.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.644, 2.922, -0.722)
位置误差向量: (6.880, -0.185, 0.722), 距离: 6.9202m
当前速度: (0.4027, 0.0064, -0.0850), 速度大小: 0.4117m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-21.123, Y:0.550, Z:-2.069
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.064, Z=0.134, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 350.79, 356.80)
当前角速度: (0.002, -1.111, -0.024)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.23°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.160, 0.001, 0.987)
目标方向: (0.994, -0.027, 0.104)
方向点积: -0.056
最终旋转输出: 0.000
==================================================

[14:12:11.800] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.8° | 朝向误差: 93.2°
[14:12:11.826] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.7° | 朝向误差: 93.2°
[14:12:11.846] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.7° | 朝向误差: 93.2°
[14:12:11.862] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.7° | 朝向误差: 93.2°
[14:12:11.886] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.7° | 朝向误差: 93.2°
[14:12:11.904] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.7° | 朝向误差: 93.2°
[14:12:11.921] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.6° | 朝向误差: 93.2°
[14:12:11.946] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.6° | 朝向误差: 93.2°
[14:12:11.963] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.6° | 朝向误差: 93.2°
[14:12:11.980] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.6° | 朝向误差: 93.2°
=== ROV控制状态 Frame 520 Time 10.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.725, 2.923, -0.739)
位置误差向量: (6.799, -0.186, 0.739), 距离: 6.8419m
当前速度: (0.4030, 0.0057, -0.0826), 速度大小: 0.4114m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-20.882, Y:0.554, Z:-2.122
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.062, Z=0.133, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 350.56, 356.79)
当前角速度: (0.002, -1.112, -0.024)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.24°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.164, 0.000, 0.986)
目标方向: (0.994, -0.027, 0.108)
方向点积: -0.056
最终旋转输出: 0.000
==================================================

[14:12:12.005] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.5° | 朝向误差: 93.2°
[14:12:12.022] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.5° | 朝向误差: 93.2°
[14:12:12.040] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.5° | 朝向误差: 93.2°
[14:12:12.065] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.5° | 朝向误差: 93.2°
[14:12:12.082] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.5° | 朝向误差: 93.2°
[14:12:12.100] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.4° | 朝向误差: 93.2°
[14:12:12.124] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.4° | 朝向误差: 93.2°
[14:12:12.141] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.4° | 朝向误差: 93.2°
[14:12:12.165] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.4° | 朝向误差: 93.2°
[14:12:12.182] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 350.3° | 朝向误差: 93.2°
=== ROV控制状态 Frame 530 Time 10.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.806, 2.924, -0.755)
位置误差向量: (6.719, -0.188, 0.755), 距离: 6.7637m
当前速度: (0.4032, 0.0050, -0.0802), 速度大小: 0.4111m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-20.640, Y:0.558, Z:-2.174
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.060, Z=0.132, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 350.34, 356.79)
当前角速度: (0.002, -1.112, -0.023)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.25°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.168, 0.000, 0.986)
目标方向: (0.993, -0.028, 0.112)
方向点积: -0.057
最终旋转输出: 0.000
==================================================

[14:12:12.206] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.3° | 朝向误差: 93.3°
[14:12:12.224] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.3° | 朝向误差: 93.3°
[14:12:12.248] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.3° | 朝向误差: 93.3°
[14:12:12.267] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.3° | 朝向误差: 93.3°
[14:12:12.282] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.2° | 朝向误差: 93.3°
[14:12:12.300] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.2° | 朝向误差: 93.3°
[14:12:12.325] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.2° | 朝向误差: 93.3°
[14:12:12.341] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.2° | 朝向误差: 93.3°
[14:12:12.364] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.1° | 朝向误差: 93.3°
[14:12:12.382] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.1° | 朝向误差: 93.3°
=== ROV控制状态 Frame 540 Time 10.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.886, 2.925, -0.771)
位置误差向量: (6.638, -0.188, 0.771), 距离: 6.6854m
当前速度: (0.4034, 0.0043, -0.0777), 速度大小: 0.4108m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-20.398, Y:0.562, Z:-2.225
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.059, Z=0.131, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 350.12, 356.78)
当前角速度: (0.000, -1.114, -0.023)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.26°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.172, 0.000, 0.985)
目标方向: (0.993, -0.028, 0.115)
方向点积: -0.057
最终旋转输出: 0.000
==================================================

[14:12:12.408] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.1° | 朝向误差: 93.3°
[14:12:12.426] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.1° | 朝向误差: 93.3°
[14:12:12.442] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.1° | 朝向误差: 93.3°
[14:12:12.461] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.0° | 朝向误差: 93.3°
[14:12:12.484] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.0° | 朝向误差: 93.3°
[14:12:12.502] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.0° | 朝向误差: 93.3°
[14:12:12.526] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 350.0° | 朝向误差: 93.3°
[14:12:12.544] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.9° | 朝向误差: 93.3°
[14:12:12.561] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.9° | 朝向误差: 93.3°
[14:12:12.585] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.9° | 朝向误差: 93.3°
=== ROV控制状态 Frame 550 Time 11.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (22.967, 2.926, -0.786)
位置误差向量: (6.557, -0.189, 0.786), 距离: 6.6071m
当前速度: (0.4036, 0.0037, -0.0752), 速度大小: 0.4106m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-20.157, Y:0.565, Z:-2.274
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.057, Z=0.130, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 349.90, 356.78)
当前角速度: (0.002, -1.114, -0.023)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.27°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.175, 0.000, 0.984)
目标方向: (0.992, -0.029, 0.119)
方向点积: -0.057
最终旋转输出: 0.000
==================================================

[14:12:12.602] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.9° | 朝向误差: 93.3°
[14:12:12.627] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.9° | 朝向误差: 93.3°
[14:12:12.644] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.8° | 朝向误差: 93.3°
[14:12:12.662] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.8° | 朝向误差: 93.3°
[14:12:12.680] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.8° | 朝向误差: 93.3°
[14:12:12.714] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.8° | 朝向误差: 93.3°
[14:12:12.723] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.7° | 朝向误差: 93.3°
[14:12:12.747] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.7° | 朝向误差: 93.3°
[14:12:12.764] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.7° | 朝向误差: 93.3°
[14:12:12.786] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.7° | 朝向误差: 93.3°
=== ROV控制状态 Frame 560 Time 11.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (23.048, 2.927, -0.801)
位置误差向量: (6.477, -0.190, 0.801), 距离: 6.5288m
当前速度: (0.4037, 0.0030, -0.0726), 速度大小: 0.4102m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-19.915, Y:0.568, Z:-2.321
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.056, Z=0.129, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 349.67, 356.77)
当前角速度: (0.002, -1.115, -0.023)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.28°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.179, 0.000, 0.984)
目标方向: (0.992, -0.029, 0.123)
方向点积: -0.057
最终旋转输出: 0.000
==================================================

[14:12:12.804] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.6° | 朝向误差: 93.3°
[14:12:12.821] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.6° | 朝向误差: 93.3°
[14:12:12.845] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.6° | 朝向误差: 93.3°
[14:12:12.872] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.6° | 朝向误差: 93.3°
[14:12:12.883] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.6° | 朝向误差: 93.3°
[14:12:12.907] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.5° | 朝向误差: 93.3°
[14:12:12.924] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.5° | 朝向误差: 93.3°
[14:12:12.944] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.5° | 朝向误差: 93.3°
[14:12:12.960] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.5° | 朝向误差: 93.3°
[14:12:12.985] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.4° | 朝向误差: 93.3°
=== ROV控制状态 Frame 570 Time 11.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (23.129, 2.927, -0.815)
位置误差向量: (6.396, -0.190, 0.815), 距离: 6.4505m
当前速度: (0.4038, 0.0024, -0.0701), 速度大小: 0.4098m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-19.672, Y:0.571, Z:-2.368
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.055, Z=0.128, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 349.45, 356.77)
当前角速度: (0.002, -1.117, -0.021)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.29°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.183, 0.000, 0.983)
目标方向: (0.992, -0.030, 0.126)
方向点积: -0.057
最终旋转输出: 0.000
==================================================

[14:12:13.003] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.4° | 朝向误差: 93.3°
[14:12:13.028] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.4° | 朝向误差: 93.3°
[14:12:13.045] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.4° | 朝向误差: 93.3°
[14:12:13.063] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.4° | 朝向误差: 93.3°
[14:12:13.080] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.3° | 朝向误差: 93.3°
[14:12:13.106] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.3° | 朝向误差: 93.3°
[14:12:13.122] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.3° | 朝向误差: 93.3°
[14:12:13.146] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.3° | 朝向误差: 93.3°
[14:12:13.164] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.2° | 朝向误差: 93.3°
[14:12:13.183] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.2° | 朝向误差: 93.3°
=== ROV控制状态 Frame 580 Time 11.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (23.209, 2.928, -0.829)
位置误差向量: (6.315, -0.191, 0.829), 距离: 6.3722m
当前速度: (0.4040, 0.0018, -0.0674), 速度大小: 0.4096m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-19.430, Y:0.573, Z:-2.412
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.053, Z=0.126, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 349.23, 356.76)
当前角速度: (0.002, -1.117, -0.023)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.30°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.187, 0.000, 0.982)
目标方向: (0.991, -0.030, 0.130)
方向点积: -0.058
最终旋转输出: 0.000
==================================================

[14:12:13.200] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.2° | 朝向误差: 93.3°
[14:12:13.225] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.2° | 朝向误差: 93.3°
[14:12:13.242] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.2° | 朝向误差: 93.3°
[14:12:13.267] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.1° | 朝向误差: 93.3°
[14:12:13.283] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.1° | 朝向误差: 93.3°
[14:12:13.301] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.1° | 朝向误差: 93.3°
[14:12:13.333] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.1° | 朝向误差: 93.3°
[14:12:13.343] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.0° | 朝向误差: 93.3°
[14:12:13.366] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.0° | 朝向误差: 93.3°
[14:12:13.383] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.0° | 朝向误差: 93.3°
=== ROV控制状态 Frame 590 Time 11.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (23.290, 2.928, -0.842)
位置误差向量: (6.234, -0.191, 0.842), 距离: 6.2938m
当前速度: (0.4040, 0.0012, -0.0648), 速度大小: 0.4091m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-19.188, Y:0.574, Z:-2.455
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.052, Z=0.125, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 349.00, 356.76)
当前角速度: (0.002, -1.118, -0.021)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.31°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.191, 0.000, 0.982)
目标方向: (0.991, -0.030, 0.134)
方向点积: -0.058
最终旋转输出: 0.000
==================================================

[14:12:13.401] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.0° | 朝向误差: 93.3°
[14:12:13.426] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 349.0° | 朝向误差: 93.3°
[14:12:13.443] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.9° | 朝向误差: 93.3°
[14:12:13.468] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.9° | 朝向误差: 93.3°
[14:12:13.486] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.9° | 朝向误差: 93.3°
[14:12:13.504] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.9° | 朝向误差: 93.3°
[14:12:13.520] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.8° | 朝向误差: 93.3°
[14:12:13.545] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.8° | 朝向误差: 93.3°
[14:12:13.563] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.8° | 朝向误差: 93.3°
[14:12:13.586] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.8° | 朝向误差: 93.3°
=== ROV控制状态 Frame 600 Time 12.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (23.371, 2.928, -0.854)
位置误差向量: (6.154, -0.191, 0.854), 距离: 6.2155m
当前速度: (0.4042, 0.0006, -0.0621), 速度大小: 0.4089m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-18.946, Y:0.576, Z:-2.497
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.051, Z=0.124, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 348.78, 356.75)
当前角速度: (0.002, -1.120, -0.021)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.32°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.195, 0.000, 0.981)
目标方向: (0.990, -0.031, 0.137)
方向点积: -0.058
最终旋转输出: 0.000
==================================================

[14:12:13.603] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.8° | 朝向误差: 93.3°
[14:12:13.621] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.7° | 朝向误差: 93.3°
[14:12:13.645] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.7° | 朝向误差: 93.3°
[14:12:13.661] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.7° | 朝向误差: 93.3°
[14:12:13.681] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.7° | 朝向误差: 93.3°
[14:12:13.705] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.6° | 朝向误差: 93.3°
[14:12:13.723] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.6° | 朝向误差: 93.3°
[14:12:13.747] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.6° | 朝向误差: 93.3°
[14:12:13.763] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.6° | 朝向误差: 93.3°
[14:12:13.788] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.6° | 朝向误差: 93.3°
=== ROV控制状态 Frame 610 Time 12.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (23.452, 2.928, -0.867)
位置误差向量: (6.073, -0.191, 0.867), 距离: 6.1372m
当前速度: (0.4042, 0.0000, -0.0594), 速度大小: 0.4085m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-18.703, Y:0.577, Z:-2.537
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.049, Z=0.123, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 348.55, 356.75)
当前角速度: (0.002, -1.122, -0.020)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.32°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.198, 0.000, 0.980)
目标方向: (0.989, -0.031, 0.141)
方向点积: -0.058
最终旋转输出: 0.000
==================================================

[14:12:13.805] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.5° | 朝向误差: 93.3°
[14:12:13.822] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.5° | 朝向误差: 93.3°
[14:12:13.845] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.5° | 朝向误差: 93.3°
[14:12:13.862] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.5° | 朝向误差: 93.3°
[14:12:13.881] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.4° | 朝向误差: 93.3°
[14:12:13.905] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.4° | 朝向误差: 93.3°
[14:12:13.923] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.4° | 朝向误差: 93.3°
[14:12:13.948] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.4° | 朝向误差: 93.3°
[14:12:13.964] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.4° | 朝向误差: 93.3°
[14:12:13.982] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.3° | 朝向误差: 93.3°
=== ROV控制状态 Frame 620 Time 12.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (23.533, 2.928, -0.878)
位置误差向量: (5.992, -0.191, 0.878), 距离: 6.0589m
当前速度: (0.4043, -0.0005, -0.0567), 速度大小: 0.4082m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-18.461, Y:0.577, Z:-2.575
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.048, Z=0.121, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.97, 348.33, 356.75)
当前角速度: (0.002, -1.123, -0.020)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.33°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.202, 0.000, 0.979)
目标方向: (0.989, -0.032, 0.145)
方向点积: -0.058
最终旋转输出: 0.000
==================================================

[14:12:14.007] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.3° | 朝向误差: 93.3°
[14:12:14.024] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.3° | 朝向误差: 93.3°
[14:12:14.042] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.3° | 朝向误差: 93.3°
[14:12:14.066] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.2° | 朝向误差: 93.3°
[14:12:14.084] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.2° | 朝向误差: 93.3°
[14:12:14.102] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.2° | 朝向误差: 93.3°
[14:12:14.126] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.2° | 朝向误差: 93.3°
[14:12:14.143] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.1° | 朝向误差: 93.3°
[14:12:14.160] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.1° | 朝向误差: 93.3°
[14:12:14.184] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.1° | 朝向误差: 93.3°
=== ROV控制状态 Frame 630 Time 12.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (23.613, 2.928, -0.889)
位置误差向量: (5.911, -0.191, 0.889), 距离: 5.9806m
当前速度: (0.4043, -0.0011, -0.0539), 速度大小: 0.4078m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-18.218, Y:0.577, Z:-2.612
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.047, Z=0.120, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 348.10, 356.74)
当前角速度: (0.002, -1.123, -0.018)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.34°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.206, 0.000, 0.979)
目标方向: (0.988, -0.032, 0.149)
方向点积: -0.058
最终旋转输出: 0.000
==================================================

[14:12:14.202] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.1° | 朝向误差: 93.3°
[14:12:14.227] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.1° | 朝向误差: 93.3°
[14:12:14.252] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.0° | 朝向误差: 93.3°
[14:12:14.262] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.0° | 朝向误差: 93.3°
[14:12:14.281] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.0° | 朝向误差: 93.3°
[14:12:14.304] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 348.0° | 朝向误差: 93.3°
[14:12:14.321] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 347.9° | 朝向误差: 93.3°
[14:12:14.345] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 347.9° | 朝向误差: 93.3°
[14:12:14.363] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 347.9° | 朝向误差: 93.3°
[14:12:14.386] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 347.9° | 朝向误差: 93.3°
=== ROV控制状态 Frame 640 Time 12.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (23.694, 2.928, -0.900)
位置误差向量: (5.830, -0.191, 0.900), 距离: 5.9022m
当前速度: (0.4043, -0.0016, -0.0512), 速度大小: 0.4075m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-17.976, Y:0.577, Z:-2.647
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.046, Z=0.119, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 347.88, 356.74)
当前角速度: (0.002, -1.125, -0.018)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.35°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.210, 0.000, 0.978)
目标方向: (0.988, -0.032, 0.152)
方向点积: -0.058
最终旋转输出: 0.000
==================================================

[14:12:14.412] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 347.9° | 朝向误差: 93.3°
[14:12:14.421] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 347.8° | 朝向误差: 93.3°
[14:12:14.445] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 347.8° | 朝向误差: 93.3°
[14:12:14.462] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.8° | 朝向误差: 93.4°
[14:12:14.481] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.8° | 朝向误差: 93.4°
[14:12:14.505] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.7° | 朝向误差: 93.4°
[14:12:14.523] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.7° | 朝向误差: 93.4°
[14:12:14.541] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.7° | 朝向误差: 93.4°
[14:12:14.566] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.7° | 朝向误差: 93.4°
[14:12:14.583] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.7° | 朝向误差: 93.4°
=== ROV控制状态 Frame 650 Time 13.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (23.775, 2.927, -0.910)
位置误差向量: (5.749, -0.190, 0.910), 距离: 5.8239m
当前速度: (0.4043, -0.0021, -0.0484), 速度大小: 0.4072m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-17.733, Y:0.577, Z:-2.681
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.044, Z=0.117, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 347.65, 356.73)
当前角速度: (0.002, -1.126, -0.018)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.35°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.214, 0.000, 0.977)
目标方向: (0.987, -0.033, 0.156)
方向点积: -0.059
最终旋转输出: 0.000
==================================================

[14:12:14.607] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.6° | 朝向误差: 93.4°
[14:12:14.624] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.6° | 朝向误差: 93.4°
[14:12:14.641] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.6° | 朝向误差: 93.4°
[14:12:14.666] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.6° | 朝向误差: 93.4°
[14:12:14.684] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.5° | 朝向误差: 93.4°
[14:12:14.700] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.5° | 朝向误差: 93.4°
[14:12:14.727] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.5° | 朝向误差: 93.4°
[14:12:14.745] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.5° | 朝向误差: 93.4°
[14:12:14.762] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.5° | 朝向误差: 93.4°
[14:12:14.786] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.4° | 朝向误差: 93.4°
=== ROV控制状态 Frame 660 Time 13.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (23.856, 2.927, -0.919)
位置误差向量: (5.668, -0.190, 0.919), 距离: 5.7456m
当前速度: (0.4043, -0.0027, -0.0456), 速度大小: 0.4068m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-17.490, Y:0.576, Z:-2.712
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.043, Z=0.116, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 347.43, 356.73)
当前角速度: (0.002, -1.128, -0.018)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.36°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.218, 0.000, 0.976)
目标方向: (0.987, -0.033, 0.160)
方向点积: -0.059
最终旋转输出: 0.000
==================================================

[14:12:14.803] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.4° | 朝向误差: 93.4°
[14:12:14.820] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.4° | 朝向误差: 93.4°
[14:12:14.845] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.4° | 朝向误差: 93.4°
[14:12:14.863] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.3° | 朝向误差: 93.4°
[14:12:14.881] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.3° | 朝向误差: 93.4°
[14:12:14.906] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.3° | 朝向误差: 93.4°
[14:12:14.923] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.3° | 朝向误差: 93.4°
[14:12:14.941] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.2° | 朝向误差: 93.4°
[14:12:14.965] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.2° | 朝向误差: 93.4°
[14:12:14.982] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.2° | 朝向误差: 93.4°
=== ROV控制状态 Frame 670 Time 13.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (23.937, 2.926, -0.928)
位置误差向量: (5.588, -0.189, 0.928), 距离: 5.6672m
当前速度: (0.4042, -0.0032, -0.0427), 速度大小: 0.4064m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-17.248, Y:0.575, Z:-2.743
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.042, Z=0.115, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 347.20, 356.73)
当前角速度: (0.002, -1.129, -0.018)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.37°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.221, 0.000, 0.975)
目标方向: (0.986, -0.033, 0.164)
方向点积: -0.059
最终旋转输出: 0.000
==================================================

[14:12:15.006] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.2° | 朝向误差: 93.4°
[14:12:15.032] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.2° | 朝向误差: 93.4°
[14:12:15.042] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.1° | 朝向误差: 93.4°
[14:12:15.067] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.1° | 朝向误差: 93.4°
[14:12:15.084] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.1° | 朝向误差: 93.4°
[14:12:15.102] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.1° | 朝向误差: 93.4°
[14:12:15.125] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.0° | 朝向误差: 93.4°
[14:12:15.143] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.0° | 朝向误差: 93.4°
[14:12:15.166] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.0° | 朝向误差: 93.4°
[14:12:15.186] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.0° | 朝向误差: 93.4°
=== ROV控制状态 Frame 680 Time 13.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.018, 2.926, -0.936)
位置误差向量: (5.507, -0.189, 0.936), 距离: 5.5889m
当前速度: (0.4042, -0.0036, -0.0399), 速度大小: 0.4061m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-17.005, Y:0.574, Z:-2.771
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.041, Z=0.113, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 346.98, 356.72)
当前角速度: (0.002, -1.131, -0.017)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.38°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.225, 0.000, 0.974)
目标方向: (0.985, -0.034, 0.167)
方向点积: -0.059
最终旋转输出: 0.000
==================================================

[14:12:15.203] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 347.0° | 朝向误差: 93.4°
[14:12:15.222] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.9° | 朝向误差: 93.4°
[14:12:15.246] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.9° | 朝向误差: 93.4°
[14:12:15.263] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.9° | 朝向误差: 93.4°
[14:12:15.287] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.9° | 朝向误差: 93.4°
[14:12:15.305] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.8° | 朝向误差: 93.4°
[14:12:15.323] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.8° | 朝向误差: 93.4°
[14:12:15.341] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.8° | 朝向误差: 93.4°
[14:12:15.364] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.8° | 朝向误差: 93.4°
[14:12:15.382] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.8° | 朝向误差: 93.4°
=== ROV控制状态 Frame 690 Time 13.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.099, 2.925, -0.944)
位置误差向量: (5.426, -0.188, 0.944), 距离: 5.5106m
当前速度: (0.4041, -0.0041, -0.0370), 速度大小: 0.4058m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-16.763, Y:0.572, Z:-2.798
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.040, Z=0.112, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 346.75, 356.72)
当前角速度: (0.002, -1.132, -0.017)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.38°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.229, 0.000, 0.973)
目标方向: (0.985, -0.034, 0.171)
方向点积: -0.059
最终旋转输出: 0.000
==================================================

[14:12:15.405] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.7° | 朝向误差: 93.4°
[14:12:15.422] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.7° | 朝向误差: 93.4°
[14:12:15.446] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.7° | 朝向误差: 93.4°
[14:12:15.463] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.7° | 朝向误差: 93.4°
[14:12:15.487] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.6° | 朝向误差: 93.4°
[14:12:15.507] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.6° | 朝向误差: 93.4°
[14:12:15.523] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.6° | 朝向误差: 93.4°
[14:12:15.541] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.6° | 朝向误差: 93.4°
[14:12:15.565] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.5° | 朝向误差: 93.4°
[14:12:15.582] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.5° | 朝向误差: 93.4°
=== ROV控制状态 Frame 700 Time 14.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.179, 2.924, -0.951)
位置误差向量: (5.345, -0.187, 0.951), 距离: 5.4322m
当前速度: (0.4040, -0.0046, -0.0342), 速度大小: 0.4054m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-16.520, Y:0.570, Z:-2.823
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.039, Z=0.110, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 346.52, 356.72)
当前角速度: (0.002, -1.134, -0.015)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.39°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.233, 0.000, 0.972)
目标方向: (0.984, -0.034, 0.175)
方向点积: -0.059
最终旋转输出: 0.000
==================================================

[14:12:15.607] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.5° | 朝向误差: 93.4°
[14:12:15.625] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.5° | 朝向误差: 93.4°
[14:12:15.643] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.5° | 朝向误差: 93.4°
[14:12:15.666] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.4° | 朝向误差: 93.4°
[14:12:15.683] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.4° | 朝向误差: 93.4°
[14:12:15.702] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.4° | 朝向误差: 93.4°
[14:12:15.727] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.4° | 朝向误差: 93.4°
[14:12:15.743] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.3° | 朝向误差: 93.4°
[14:12:15.760] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.3° | 朝向误差: 93.4°
[14:12:15.793] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.3° | 朝向误差: 93.4°
=== ROV控制状态 Frame 710 Time 14.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.260, 2.923, -0.957)
位置误差向量: (5.264, -0.186, 0.957), 距离: 5.3539m
当前速度: (0.4039, -0.0051, -0.0313), 速度大小: 0.4051m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-16.278, Y:0.568, Z:-2.846
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.038, Z=0.109, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 346.30, 356.71)
当前角速度: (0.002, -1.135, -0.015)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.40°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.237, 0.000, 0.972)
目标方向: (0.983, -0.035, 0.179)
方向点积: -0.059
最终旋转输出: 0.000
==================================================

[14:12:15.803] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.3° | 朝向误差: 93.4°
[14:12:15.820] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.3° | 朝向误差: 93.4°
[14:12:15.845] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.2° | 朝向误差: 93.4°
[14:12:15.862] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.2° | 朝向误差: 93.4°
[14:12:15.886] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.2° | 朝向误差: 93.4°
[14:12:15.903] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.2° | 朝向误差: 93.4°
[14:12:15.921] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.1° | 朝向误差: 93.4°
[14:12:15.946] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.1° | 朝向误差: 93.4°
[14:12:15.962] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.1° | 朝向误差: 93.4°
[14:12:15.980] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.1° | 朝向误差: 93.4°
=== ROV控制状态 Frame 720 Time 14.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.341, 2.922, -0.963)
位置误差向量: (5.184, -0.185, 0.963), 距离: 5.2755m
当前速度: (0.4038, -0.0055, -0.0284), 速度大小: 0.4048m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-16.035, Y:0.565, Z:-2.868
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.037, Z=0.108, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 346.07, 356.71)
当前角速度: (0.003, -1.137, -0.015)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.40°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.241, 0.000, 0.971)
目标方向: (0.983, -0.035, 0.183)
方向点积: -0.059
最终旋转输出: 0.000
==================================================

[14:12:16.005] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.0° | 朝向误差: 93.4°
[14:12:16.022] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.0° | 朝向误差: 93.4°
[14:12:16.045] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.0° | 朝向误差: 93.4°
[14:12:16.063] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.0° | 朝向误差: 93.4°
[14:12:16.087] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 346.0° | 朝向误差: 93.4°
[14:12:16.105] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.9° | 朝向误差: 93.4°
[14:12:16.125] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.9° | 朝向误差: 93.4°
[14:12:16.142] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.9° | 朝向误差: 93.4°
[14:12:16.167] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.9° | 朝向误差: 93.4°
[14:12:16.184] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.8° | 朝向误差: 93.4°
=== ROV控制状态 Frame 730 Time 14.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.422, 2.921, -0.969)
位置误差向量: (5.103, -0.184, 0.969), 距离: 5.1972m
当前速度: (0.4036, -0.0059, -0.0254), 速度大小: 0.4044m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-15.793, Y:0.562, Z:-2.888
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.036, Z=0.106, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 345.84, 356.71)
当前角速度: (0.002, -1.138, -0.014)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.41°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.245, 0.000, 0.970)
目标方向: (0.982, -0.035, 0.186)
方向点积: -0.059
最终旋转输出: 0.000
==================================================

[14:12:16.201] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.8° | 朝向误差: 93.4°
[14:12:16.226] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.8° | 朝向误差: 93.4°
[14:12:16.242] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.8° | 朝向误差: 93.4°
[14:12:16.261] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.8° | 朝向误差: 93.4°
[14:12:16.285] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.7° | 朝向误差: 93.4°
[14:12:16.301] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.7° | 朝向误差: 93.4°
[14:12:16.327] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.7° | 朝向误差: 93.4°
[14:12:16.345] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.7° | 朝向误差: 93.4°
[14:12:16.363] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.6° | 朝向误差: 93.4°
[14:12:16.380] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.6° | 朝向误差: 93.4°
=== ROV控制状态 Frame 740 Time 14.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.502, 2.920, -0.973)
位置误差向量: (5.022, -0.183, 0.973), 距离: 5.1188m
当前速度: (0.4034, -0.0064, -0.0225), 速度大小: 0.4041m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-15.550, Y:0.559, Z:-2.906
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.035, Z=0.105, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 345.62, 356.71)
当前角速度: (0.002, -1.138, -0.014)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.42°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.248, 0.000, 0.969)
目标方向: (0.981, -0.036, 0.190)
方向点积: -0.060
最终旋转输出: 0.000
==================================================

[14:12:16.414] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.6° | 朝向误差: 93.4°
[14:12:16.425] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.6° | 朝向误差: 93.4°
[14:12:16.441] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.5° | 朝向误差: 93.4°
[14:12:16.465] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.5° | 朝向误差: 93.4°
[14:12:16.482] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.5° | 朝向误差: 93.4°
[14:12:16.506] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.5° | 朝向误差: 93.4°
[14:12:16.525] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.5° | 朝向误差: 93.4°
[14:12:16.543] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.4° | 朝向误差: 93.4°
[14:12:16.568] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.4° | 朝向误差: 93.4°
[14:12:16.586] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.4° | 朝向误差: 93.4°
=== ROV控制状态 Frame 750 Time 15.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.583, 2.918, -0.978)
位置误差向量: (4.941, -0.181, 0.978), 距离: 5.0405m
当前速度: (0.4032, -0.0068, -0.0196), 速度大小: 0.4037m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-15.308, Y:0.556, Z:-2.923
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.034, Z=0.103, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 345.39, 356.70)
当前角速度: (0.002, -1.140, -0.014)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.42°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.252, 0.000, 0.968)
目标方向: (0.980, -0.036, 0.194)
方向点积: -0.060
最终旋转输出: 0.000
==================================================

[14:12:16.603] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.4° | 朝向误差: 93.4°
[14:12:16.621] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.3° | 朝向误差: 93.4°
[14:12:16.644] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.3° | 朝向误差: 93.4°
[14:12:16.662] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.3° | 朝向误差: 93.4°
[14:12:16.686] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.3° | 朝向误差: 93.4°
[14:12:16.703] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.3° | 朝向误差: 93.4°
[14:12:16.721] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.2° | 朝向误差: 93.4°
[14:12:16.740] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.2° | 朝向误差: 93.4°
[14:12:16.766] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.2° | 朝向误差: 93.4°
[14:12:16.781] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.2° | 朝向误差: 93.4°
=== ROV控制状态 Frame 760 Time 15.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.664, 2.917, -0.981)
位置误差向量: (4.861, -0.180, 0.981), 距离: 4.9621m
当前速度: (0.4030, -0.0072, -0.0166), 速度大小: 0.4034m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-15.066, Y:0.552, Z:-2.937
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.033, Z=0.102, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 345.16, 356.70)
当前角速度: (0.003, -1.143, -0.012)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.43°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.256, 0.000, 0.967)
目标方向: (0.980, -0.036, 0.198)
方向点积: -0.060
最终旋转输出: 0.000
==================================================

[14:12:16.806] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.1° | 朝向误差: 93.4°
[14:12:16.823] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.1° | 朝向误差: 93.4°
[14:12:16.840] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.1° | 朝向误差: 93.4°
[14:12:16.879] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.1° | 朝向误差: 93.4°
[14:12:16.892] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.0° | 朝向误差: 93.4°
[14:12:16.907] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.0° | 朝向误差: 93.4°
[14:12:16.922] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.0° | 朝向误差: 93.4°
[14:12:16.949] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.0° | 朝向误差: 93.4°
[14:12:16.964] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 345.0° | 朝向误差: 93.4°
[14:12:16.995] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.9° | 朝向误差: 93.4°
=== ROV控制状态 Frame 770 Time 15.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.744, 2.915, -0.984)
位置误差向量: (4.780, -0.178, 0.984), 距离: 4.8838m
当前速度: (0.4028, -0.0076, -0.0137), 速度大小: 0.4031m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-14.824, Y:0.548, Z:-2.950
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.032, Z=0.101, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 344.93, 356.70)
当前角速度: (0.002, -1.144, -0.012)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.44°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.260, 0.000, 0.966)
目标方向: (0.979, -0.037, 0.202)
方向点积: -0.060
最终旋转输出: 0.000
==================================================

[14:12:17.007] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.9° | 朝向误差: 93.4°
[14:12:17.037] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.9° | 朝向误差: 93.4°
[14:12:17.047] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.9° | 朝向误差: 93.4°
[14:12:17.064] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.8° | 朝向误差: 93.4°
[14:12:17.081] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.8° | 朝向误差: 93.4°
[14:12:17.106] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.8° | 朝向误差: 93.4°
[14:12:17.123] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.8° | 朝向误差: 93.4°
[14:12:17.149] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.7° | 朝向误差: 93.4°
[14:12:17.167] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.7° | 朝向误差: 93.4°
[14:12:17.192] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.7° | 朝向误差: 93.4°
=== ROV控制状态 Frame 780 Time 15.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.825, 2.914, -0.987)
位置误差向量: (4.700, -0.177, 0.987), 距离: 4.8054m
当前速度: (0.4025, -0.0080, -0.0107), 速度大小: 0.4028m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-14.582, Y:0.544, Z:-2.961
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.031, Z=0.099, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 344.70, 356.69)
当前角速度: (0.002, -1.146, -0.012)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.44°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.264, 0.000, 0.965)
目标方向: (0.978, -0.037, 0.205)
方向点积: -0.060
最终旋转输出: 0.000
==================================================

[14:12:17.203] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.7° | 朝向误差: 93.4°
[14:12:17.227] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.7° | 朝向误差: 93.4°
[14:12:17.245] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.6° | 朝向误差: 93.4°
[14:12:17.262] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.6° | 朝向误差: 93.4°
[14:12:17.287] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.6° | 朝向误差: 93.4°
[14:12:17.304] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.6° | 朝向误差: 93.4°
[14:12:17.321] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.5° | 朝向误差: 93.4°
[14:12:17.349] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 344.5° | 朝向误差: 93.4°
[14:12:17.367] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.5° | 朝向误差: 93.5°
[14:12:17.386] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.5° | 朝向误差: 93.5°
=== ROV控制状态 Frame 790 Time 15.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.905, 2.912, -0.988)
位置误差向量: (4.619, -0.175, 0.988), 距离: 4.7271m
当前速度: (0.4023, -0.0084, -0.0077), 速度大小: 0.4024m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-14.340, Y:0.540, Z:-2.971
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.030, Z=0.098, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 344.47, 356.69)
当前角速度: (0.002, -1.147, -0.011)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.45°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.268, 0.000, 0.963)
目标方向: (0.977, -0.037, 0.209)
方向点积: -0.060
最终旋转输出: 0.000
==================================================

[14:12:17.404] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.4° | 朝向误差: 93.5°
[14:12:17.422] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.4° | 朝向误差: 93.5°
[14:12:17.447] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.4° | 朝向误差: 93.5°
[14:12:17.464] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.4° | 朝向误差: 93.5°
[14:12:17.481] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.4° | 朝向误差: 93.5°
[14:12:17.506] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.3° | 朝向误差: 93.5°
[14:12:17.525] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.3° | 朝向误差: 93.5°
[14:12:17.544] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.3° | 朝向误差: 93.5°
[14:12:17.562] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.3° | 朝向误差: 93.5°
[14:12:17.588] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.2° | 朝向误差: 93.5°
=== ROV控制状态 Frame 800 Time 16.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (24.986, 2.910, -0.990)
位置误差向量: (4.539, -0.174, 0.990), 距离: 4.6487m
当前速度: (0.4020, -0.0088, -0.0048), 速度大小: 0.4021m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-14.099, Y:0.535, Z:-2.979
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.030, Z=0.096, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 344.24, 356.69)
当前角速度: (0.002, -1.149, -0.011)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.46°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.272, 0.000, 0.962)
目标方向: (0.976, -0.037, 0.213)
方向点积: -0.060
最终旋转输出: 0.000
==================================================

[14:12:17.605] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.2° | 朝向误差: 93.5°
[14:12:17.622] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.2° | 朝向误差: 93.5°
[14:12:17.640] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.2° | 朝向误差: 93.5°
[14:12:17.666] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.2° | 朝向误差: 93.5°
[14:12:17.683] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.1° | 朝向误差: 93.5°
[14:12:17.706] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.1° | 朝向误差: 93.5°
[14:12:17.723] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.1° | 朝向误差: 93.5°
[14:12:17.741] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.1° | 朝向误差: 93.5°
[14:12:17.768] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.0° | 朝向误差: 93.5°
[14:12:17.785] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.0° | 朝向误差: 93.5°
=== ROV控制状态 Frame 810 Time 16.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (25.066, 2.909, -0.990)
位置误差向量: (4.458, -0.172, 0.990), 距离: 4.5703m
当前速度: (0.4017, -0.0091, -0.0018), 速度大小: 0.4018m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-13.857, Y:0.530, Z:-2.984
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.029, Z=0.095, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 344.01, 356.69)
当前角速度: (0.002, -1.151, -0.011)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.47°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.275, 0.000, 0.961)
目标方向: (0.976, -0.038, 0.217)
方向点积: -0.060
最终旋转输出: 0.000
==================================================

[14:12:17.803] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.0° | 朝向误差: 93.5°
[14:12:17.821] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 344.0° | 朝向误差: 93.5°
[14:12:17.845] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.9° | 朝向误差: 93.5°
[14:12:17.862] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.9° | 朝向误差: 93.5°
[14:12:17.887] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.9° | 朝向误差: 93.5°
[14:12:17.904] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.9° | 朝向误差: 93.5°
[14:12:17.922] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.9° | 朝向误差: 93.5°
[14:12:17.945] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.8° | 朝向误差: 93.5°
[14:12:17.971] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.8° | 朝向误差: 93.5°
[14:12:17.983] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.8° | 朝向误差: 93.5°
=== ROV控制状态 Frame 820 Time 16.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (25.146, 2.907, -0.990)
位置误差向量: (4.378, -0.170, 0.990), 距离: 4.4920m
当前速度: (0.4013, -0.0095, 0.0012), 速度大小: 0.4014m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-13.616, Y:0.525, Z:-2.988
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.028, Z=0.093, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 343.78, 356.69)
当前角速度: (0.002, -1.152, -0.011)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.47°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.279, 0.000, 0.960)
目标方向: (0.975, -0.038, 0.220)
方向点积: -0.061
最终旋转输出: 0.000
==================================================

[14:12:18.001] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.8° | 朝向误差: 93.5°
[14:12:18.025] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.7° | 朝向误差: 93.5°
[14:12:18.043] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.7° | 朝向误差: 93.5°
[14:12:18.067] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.7° | 朝向误差: 93.5°
[14:12:18.084] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.7° | 朝向误差: 93.5°
[14:12:18.100] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.6° | 朝向误差: 93.5°
[14:12:18.125] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.6° | 朝向误差: 93.5°
[14:12:18.143] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.6° | 朝向误差: 93.5°
[14:12:18.168] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.6° | 朝向误差: 93.5°
[14:12:18.185] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.6° | 朝向误差: 93.5°
=== ROV控制状态 Frame 830 Time 16.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (25.227, 2.905, -0.990)
位置误差向量: (4.298, -0.168, 0.990), 距离: 4.4136m
当前速度: (0.4010, -0.0098, 0.0042), 速度大小: 0.4012m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-13.375, Y:0.520, Z:-2.991
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.027, Z=0.092, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 343.55, 356.68)
当前角速度: (0.002, -1.154, -0.011)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.48°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.283, 0.000, 0.959)
目标方向: (0.974, -0.038, 0.224)
方向点积: -0.061
最终旋转输出: 0.000
==================================================

[14:12:18.203] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.5° | 朝向误差: 93.5°
[14:12:18.220] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.5° | 朝向误差: 93.5°
[14:12:18.245] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.5° | 朝向误差: 93.5°
[14:12:18.262] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.5° | 朝向误差: 93.5°
[14:12:18.281] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.4° | 朝向误差: 93.5°
[14:12:18.305] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.4° | 朝向误差: 93.5°
[14:12:18.322] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.4° | 朝向误差: 93.5°
[14:12:18.345] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.4° | 朝向误差: 93.5°
[14:12:18.362] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.3° | 朝向误差: 93.5°
[14:12:18.388] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.3° | 朝向误差: 93.5°
=== ROV控制状态 Frame 840 Time 16.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (25.307, 2.903, -0.988)
位置误差向量: (4.218, -0.166, 0.988), 距离: 4.3352m
当前速度: (0.4006, -0.0102, 0.0072), 速度大小: 0.4008m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-13.134, Y:0.514, Z:-2.991
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.026, Z=0.090, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 343.32, 356.68)
当前角速度: (0.003, -1.155, -0.008)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.49°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.287, 0.000, 0.958)
目标方向: (0.973, -0.038, 0.228)
方向点积: -0.061
最终旋转输出: 0.000
==================================================

[14:12:18.406] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.3° | 朝向误差: 93.5°
[14:12:18.431] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.3° | 朝向误差: 93.5°
[14:12:18.441] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.3° | 朝向误差: 93.5°
[14:12:18.464] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.2° | 朝向误差: 93.5°
[14:12:18.482] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.2° | 朝向误差: 93.5°
[14:12:18.505] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.2° | 朝向误差: 93.5°
[14:12:18.522] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.2° | 朝向误差: 93.5°
[14:12:18.547] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.1° | 朝向误差: 93.5°
[14:12:18.564] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.1° | 朝向误差: 93.5°
[14:12:18.588] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.1° | 朝向误差: 93.5°
=== ROV控制状态 Frame 850 Time 17.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (25.387, 2.901, -0.987)
位置误差向量: (4.138, -0.164, 0.987), 距离: 4.2569m
当前速度: (0.4003, -0.0105, 0.0103), 速度大小: 0.4005m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-12.893, Y:0.508, Z:-2.990
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.026, Z=0.089, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 343.09, 356.68)
当前角速度: (0.002, -1.158, -0.008)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.50°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.291, 0.000, 0.957)
目标方向: (0.972, -0.038, 0.232)
方向点积: -0.061
最终旋转输出: 0.000
==================================================

[14:12:18.601] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.1° | 朝向误差: 93.5°
[14:12:18.624] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.0° | 朝向误差: 93.5°
[14:12:18.641] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.0° | 朝向误差: 93.5°
[14:12:18.666] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.0° | 朝向误差: 93.5°
[14:12:18.683] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.0° | 朝向误差: 93.5°
[14:12:18.707] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 343.0° | 朝向误差: 93.5°
[14:12:18.724] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.9° | 朝向误差: 93.5°
[14:12:18.743] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.9° | 朝向误差: 93.5°
[14:12:18.766] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.9° | 朝向误差: 93.5°
[14:12:18.784] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.9° | 朝向误差: 93.5°
=== ROV控制状态 Frame 860 Time 17.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (25.467, 2.899, -0.984)
位置误差向量: (4.058, -0.162, 0.984), 距离: 4.1785m
当前速度: (0.3998, -0.0108, 0.0133), 速度大小: 0.4001m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-12.653, Y:0.502, Z:-2.987
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.025, Z=0.088, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 342.86, 356.68)
当前角速度: (0.002, -1.158, -0.006)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.51°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.295, 0.000, 0.956)
目标方向: (0.971, -0.039, 0.236)
方向点积: -0.061
最终旋转输出: 0.000
==================================================

[14:12:18.803] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.8° | 朝向误差: 93.5°
[14:12:18.826] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.8° | 朝向误差: 93.5°
[14:12:18.844] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.8° | 朝向误差: 93.5°
[14:12:18.867] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.8° | 朝向误差: 93.5°
[14:12:18.891] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.7° | 朝向误差: 93.5°
[14:12:18.902] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.7° | 朝向误差: 93.5°
[14:12:18.926] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.7° | 朝向误差: 93.5°
[14:12:18.943] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.7° | 朝向误差: 93.5°
[14:12:18.967] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.6° | 朝向误差: 93.5°
[14:12:18.984] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.6° | 朝向误差: 93.5°
=== ROV控制状态 Frame 870 Time 17.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (25.547, 2.896, -0.981)
位置误差向量: (3.978, -0.159, 0.981), 距离: 4.1002m
当前速度: (0.3994, -0.0112, 0.0163), 速度大小: 0.3999m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-12.413, Y:0.496, Z:-2.982
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.024, Z=0.086, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 342.63, 356.68)
当前角速度: (0.003, -1.161, -0.006)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.52°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.299, 0.000, 0.954)
目标方向: (0.970, -0.039, 0.239)
方向点积: -0.061
最终旋转输出: 0.000
==================================================

[14:12:19.003] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.6° | 朝向误差: 93.5°
[14:12:19.027] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.6° | 朝向误差: 93.5°
[14:12:19.045] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.6° | 朝向误差: 93.5°
[14:12:19.062] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.5° | 朝向误差: 93.5°
[14:12:19.087] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.5° | 朝向误差: 93.5°
[14:12:19.103] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.5° | 朝向误差: 93.5°
[14:12:19.127] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.5° | 朝向误差: 93.5°
[14:12:19.144] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.4° | 朝向误差: 93.5°
[14:12:19.161] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.4° | 朝向误差: 93.5°
[14:12:19.196] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.4° | 朝向误差: 93.5°
=== ROV控制状态 Frame 880 Time 17.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (25.626, 2.894, -0.978)
位置误差向量: (3.898, -0.157, 0.978), 距离: 4.0218m
当前速度: (0.3989, -0.0115, 0.0193), 速度大小: 0.3996m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-12.173, Y:0.490, Z:-2.975
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.024, Z=0.085, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 342.39, 356.68)
当前角速度: (0.002, -1.163, -0.006)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.53°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.302, 0.000, 0.953)
目标方向: (0.969, -0.039, 0.243)
方向点积: -0.061
最终旋转输出: 0.000
==================================================

[14:12:19.208] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.4° | 朝向误差: 93.5°
[14:12:19.226] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.3° | 朝向误差: 93.5°
[14:12:19.242] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.3° | 朝向误差: 93.5°
[14:12:19.267] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.3° | 朝向误差: 93.5°
[14:12:19.284] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.3° | 朝向误差: 93.5°
[14:12:19.301] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.3° | 朝向误差: 93.5°
[14:12:19.325] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.2° | 朝向误差: 93.5°
[14:12:19.351] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.2° | 朝向误差: 93.5°
[14:12:19.361] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.2° | 朝向误差: 93.5°
[14:12:19.385] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.2° | 朝向误差: 93.5°
=== ROV控制状态 Frame 890 Time 17.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (25.706, 2.892, -0.974)
位置误差向量: (3.818, -0.155, 0.974), 距离: 3.9434m
当前速度: (0.3984, -0.0118, 0.0224), 速度大小: 0.3992m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-11.933, Y:0.483, Z:-2.966
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.023, Z=0.083, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 342.16, 356.67)
当前角速度: (0.002, -1.164, -0.006)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.54°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.306, 0.000, 0.952)
目标方向: (0.968, -0.039, 0.247)
方向点积: -0.062
最终旋转输出: 0.000
==================================================

[14:12:19.403] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.1° | 朝向误差: 93.5°
[14:12:19.420] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.1° | 朝向误差: 93.5°
[14:12:19.445] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.1° | 朝向误差: 93.5°
[14:12:19.462] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.1° | 朝向误差: 93.5°
[14:12:19.486] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.0° | 朝向误差: 93.5°
[14:12:19.504] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.0° | 朝向误差: 93.5°
[14:12:19.521] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.0° | 朝向误差: 93.5°
[14:12:19.547] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.0° | 朝向误差: 93.5°
[14:12:19.564] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 342.0° | 朝向误差: 93.5°
[14:12:19.580] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 341.9° | 朝向误差: 93.5°
=== ROV控制状态 Frame 900 Time 18.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (25.786, 2.889, -0.969)
位置误差向量: (3.739, -0.153, 0.969), 距离: 3.8651m
当前速度: (0.3980, -0.0121, 0.0254), 速度大小: 0.3990m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-11.693, Y:0.477, Z:-2.956
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.022, Z=0.082, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.98, 341.93, 356.67)
当前角速度: (0.002, -1.166, -0.006)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.55°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.310, 0.000, 0.951)
目标方向: (0.967, -0.039, 0.251)
方向点积: -0.062
最终旋转输出: 0.000
==================================================

[14:12:19.606] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 341.9° | 朝向误差: 93.5°
[14:12:19.624] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 341.9° | 朝向误差: 93.5°
[14:12:19.642] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 341.9° | 朝向误差: 93.5°
[14:12:19.667] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.8° | 朝向误差: 93.6°
[14:12:19.684] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.8° | 朝向误差: 93.6°
[14:12:19.701] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.8° | 朝向误差: 93.6°
[14:12:19.725] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.8° | 朝向误差: 93.6°
[14:12:19.742] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.7° | 朝向误差: 93.6°
[14:12:19.767] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.7° | 朝向误差: 93.6°
[14:12:19.785] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.7° | 朝向误差: 93.6°
=== ROV控制状态 Frame 910 Time 18.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (25.865, 2.887, -0.963)
位置误差向量: (3.659, -0.150, 0.963), 距离: 3.7867m
当前速度: (0.3975, -0.0124, 0.0284), 速度大小: 0.3987m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-11.454, Y:0.470, Z:-2.944
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.022, Z=0.080, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 341.69, 356.67)
当前角速度: (0.002, -1.167, -0.005)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.56°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.314, 0.000, 0.949)
目标方向: (0.966, -0.040, 0.254)
方向点积: -0.062
最终旋转输出: 0.000
==================================================

[14:12:19.811] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.7° | 朝向误差: 93.6°
[14:12:19.821] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.6° | 朝向误差: 93.6°
[14:12:19.846] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.6° | 朝向误差: 93.6°
[14:12:19.863] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.6° | 朝向误差: 93.6°
[14:12:19.880] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.6° | 朝向误差: 93.6°
[14:12:19.904] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.6° | 朝向误差: 93.6°
[14:12:19.922] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.5° | 朝向误差: 93.6°
[14:12:19.945] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.5° | 朝向误差: 93.6°
[14:12:19.971] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.5° | 朝向误差: 93.6°
[14:12:19.981] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.5° | 朝向误差: 93.6°
=== ROV控制状态 Frame 920 Time 18.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (25.945, 2.885, -0.957)
位置误差向量: (3.580, -0.148, 0.957), 距离: 3.7083m
当前速度: (0.3969, -0.0127, 0.0315), 速度大小: 0.3984m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-11.215, Y:0.463, Z:-2.930
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.021, Z=0.079, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 341.46, 356.67)
当前角速度: (0.002, -1.170, -0.005)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.57°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.318, 0.000, 0.948)
目标方向: (0.965, -0.040, 0.258)
方向点积: -0.062
最终旋转输出: 0.000
==================================================

[14:12:20.005] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.4° | 朝向误差: 93.6°
[14:12:20.023] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.4° | 朝向误差: 93.6°
[14:12:20.040] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.4° | 朝向误差: 93.6°
[14:12:20.064] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.4° | 朝向误差: 93.6°
[14:12:20.081] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.3° | 朝向误差: 93.6°
[14:12:20.106] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.3° | 朝向误差: 93.6°
[14:12:20.124] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.3° | 朝向误差: 93.6°
[14:12:20.141] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.3° | 朝向误差: 93.6°
[14:12:20.164] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.2° | 朝向误差: 93.6°
[14:12:20.180] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.2° | 朝向误差: 93.6°
=== ROV控制状态 Frame 930 Time 18.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.024, 2.882, -0.951)
位置误差向量: (3.500, -0.145, 0.951), 距离: 3.6300m
当前速度: (0.3964, -0.0129, 0.0345), 速度大小: 0.3982m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-10.977, Y:0.455, Z:-2.914
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.020, Z=0.077, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 341.23, 356.67)
当前角速度: (0.002, -1.172, -0.003)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.58°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.322, 0.000, 0.947)
目标方向: (0.964, -0.040, 0.262)
方向点积: -0.062
最终旋转输出: 0.000
==================================================

[14:12:20.205] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.2° | 朝向误差: 93.6°
[14:12:20.225] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.2° | 朝向误差: 93.6°
[14:12:20.242] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.2° | 朝向误差: 93.6°
[14:12:20.275] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.1° | 朝向误差: 93.6°
[14:12:20.286] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.1° | 朝向误差: 93.6°
[14:12:20.302] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.1° | 朝向误差: 93.6°
[14:12:20.325] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.1° | 朝向误差: 93.6°
[14:12:20.342] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.0° | 朝向误差: 93.6°
[14:12:20.366] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.0° | 朝向误差: 93.6°
[14:12:20.383] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.0° | 朝向误差: 93.6°
=== ROV控制状态 Frame 940 Time 18.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.103, 2.879, -0.943)
位置误差向量: (3.421, -0.142, 0.943), 距离: 3.5516m
当前速度: (0.3959, -0.0132, 0.0376), 速度大小: 0.3979m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-10.738, Y:0.448, Z:-2.896
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.020, Z=0.076, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 340.99, 356.67)
当前角速度: (0.003, -1.172, -0.003)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.59°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.326, 0.000, 0.945)
目标方向: (0.963, -0.040, 0.266)
方向点积: -0.063
最终旋转输出: 0.000
==================================================

[14:12:20.401] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 341.0° | 朝向误差: 93.6°
[14:12:20.426] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.9° | 朝向误差: 93.6°
[14:12:20.445] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.9° | 朝向误差: 93.6°
[14:12:20.462] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.9° | 朝向误差: 93.6°
[14:12:20.485] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.9° | 朝向误差: 93.6°
[14:12:20.502] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.9° | 朝向误差: 93.6°
[14:12:20.526] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.8° | 朝向误差: 93.6°
[14:12:20.543] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.8° | 朝向误差: 93.6°
[14:12:20.560] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.8° | 朝向误差: 93.6°
[14:12:20.585] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.8° | 朝向误差: 93.6°
=== ROV控制状态 Frame 950 Time 19.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.182, 2.877, -0.936)
位置误差向量: (3.342, -0.140, 0.936), 距离: 3.4733m
当前速度: (0.3952, -0.0135, 0.0406), 速度大小: 0.3975m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-10.500, Y:0.440, Z:-2.877
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.019, Z=0.074, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 340.76, 356.67)
当前角速度: (0.002, -1.175, -0.002)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.60°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.330, 0.000, 0.944)
目标方向: (0.962, -0.040, 0.269)
方向点积: -0.063
最终旋转输出: 0.000
==================================================

[14:12:20.602] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.7° | 朝向误差: 93.6°
[14:12:20.626] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.7° | 朝向误差: 93.6°
[14:12:20.644] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.7° | 朝向误差: 93.6°
[14:12:20.660] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.7° | 朝向误差: 93.6°
[14:12:20.685] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.6° | 朝向误差: 93.6°
[14:12:20.703] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.6° | 朝向误差: 93.6°
[14:12:20.726] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.6° | 朝向误差: 93.6°
[14:12:20.746] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.6° | 朝向误差: 93.6°
[14:12:20.763] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.5° | 朝向误差: 93.6°
[14:12:20.786] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.5° | 朝向误差: 93.6°
=== ROV控制状态 Frame 960 Time 19.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.261, 2.874, -0.927)
位置误差向量: (3.263, -0.137, 0.927), 距离: 3.3949m
当前速度: (0.3946, -0.0138, 0.0437), 速度大小: 0.3973m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-10.263, Y:0.432, Z:-2.855
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.019, Z=0.073, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 340.52, 356.67)
当前角速度: (0.002, -1.176, -0.002)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.62°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.333, 0.000, 0.943)
目标方向: (0.961, -0.040, 0.273)
方向点积: -0.063
最终旋转输出: 0.000
==================================================

[14:12:20.803] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.5° | 朝向误差: 93.6°
[14:12:20.820] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.5° | 朝向误差: 93.6°
[14:12:20.846] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.5° | 朝向误差: 93.6°
[14:12:20.862] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.4° | 朝向误差: 93.6°
[14:12:20.893] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.4° | 朝向误差: 93.6°
[14:12:20.903] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.4° | 朝向误差: 93.6°
[14:12:20.927] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.4° | 朝向误差: 93.6°
[14:12:20.945] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.3° | 朝向误差: 93.6°
[14:12:20.962] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.3° | 朝向误差: 93.6°
[14:12:20.987] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.3° | 朝向误差: 93.6°
=== ROV控制状态 Frame 970 Time 19.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.340, 2.871, -0.918)
位置误差向量: (3.184, -0.134, 0.918), 距离: 3.3166m
当前速度: (0.3940, -0.0140, 0.0467), 速度大小: 0.3970m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-10.025, Y:0.424, Z:-2.832
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.018, Z=0.072, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 340.29, 356.67)
当前角速度: (0.002, -1.178, -0.002)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.63°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.337, 0.000, 0.941)
目标方向: (0.960, -0.040, 0.277)
方向点积: -0.063
最终旋转输出: 0.000
==================================================

[14:12:21.004] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.3° | 朝向误差: 93.6°
[14:12:21.020] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.2° | 朝向误差: 93.6°
[14:12:21.046] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.2° | 朝向误差: 93.6°
[14:12:21.065] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.2° | 朝向误差: 93.6°
[14:12:21.083] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.2° | 朝向误差: 93.6°
[14:12:21.106] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.1° | 朝向误差: 93.6°
[14:12:21.124] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.1° | 朝向误差: 93.6°
[14:12:21.140] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.1° | 朝向误差: 93.6°
[14:12:21.166] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.1° | 朝向误差: 93.6°
[14:12:21.183] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.1° | 朝向误差: 93.6°
=== ROV控制状态 Frame 980 Time 19.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.419, 2.868, -0.908)
位置误差向量: (3.105, -0.131, 0.908), 距离: 3.2382m
当前速度: (0.3934, -0.0142, 0.0498), 速度大小: 0.3968m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-9.788, Y:0.416, Z:-2.807
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.017, Z=0.070, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 340.05, 356.67)
当前角速度: (0.002, -1.181, 0.000)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.65°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.341, 0.000, 0.940)
目标方向: (0.959, -0.041, 0.281)
方向点积: -0.064
最终旋转输出: 0.000
==================================================

[14:12:21.201] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.0° | 朝向误差: 93.6°
[14:12:21.226] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 340.0° | 朝向误差: 93.6°
[14:12:21.243] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 340.0° | 朝向误差: 93.7°
[14:12:21.262] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 340.0° | 朝向误差: 93.7°
[14:12:21.286] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.9° | 朝向误差: 93.7°
[14:12:21.303] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.9° | 朝向误差: 93.7°
[14:12:21.326] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.9° | 朝向误差: 93.7°
[14:12:21.351] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.9° | 朝向误差: 93.7°
[14:12:21.362] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.8° | 朝向误差: 93.7°
[14:12:21.384] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.8° | 朝向误差: 93.7°
=== ROV控制状态 Frame 990 Time 19.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.498, 2.865, -0.898)
位置误差向量: (3.027, -0.128, 0.898), 距离: 3.1598m
当前速度: (0.3926, -0.0145, 0.0529), 速度大小: 0.3964m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-9.552, Y:0.408, Z:-2.780
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.017, Z=0.069, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 339.81, 356.67)
当前角速度: (0.002, -1.183, 0.000)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.66°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.345, 0.000, 0.939)
目标方向: (0.958, -0.041, 0.284)
方向点积: -0.064
最终旋转输出: 0.000
==================================================

[14:12:21.402] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.8° | 朝向误差: 93.7°
[14:12:21.426] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.8° | 朝向误差: 93.7°
[14:12:21.443] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.7° | 朝向误差: 93.7°
[14:12:21.462] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.7° | 朝向误差: 93.7°
[14:12:21.486] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.7° | 朝向误差: 93.7°
[14:12:21.504] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.7° | 朝向误差: 93.7°
[14:12:21.521] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.6° | 朝向误差: 93.7°
[14:12:21.545] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.6° | 朝向误差: 93.7°
[14:12:21.563] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.6° | 朝向误差: 93.7°
[14:12:21.581] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.6° | 朝向误差: 93.7°
=== ROV控制状态 Frame 1000 Time 20.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.576, 2.863, -0.887)
位置误差向量: (2.948, -0.126, 0.887), 距离: 3.0815m
当前速度: (0.3920, -0.0147, 0.0559), 速度大小: 0.3962m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-9.315, Y:0.399, Z:-2.752
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.016, Z=0.067, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 339.58, 356.67)
当前角速度: (0.002, -1.186, 0.002)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.68°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.349, 0.000, 0.937)
目标方向: (0.957, -0.041, 0.288)
方向点积: -0.064
最终旋转输出: 0.000
==================================================

[14:12:21.605] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.6° | 朝向误差: 93.7°
[14:12:21.623] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.5° | 朝向误差: 93.7°
[14:12:21.657] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.5° | 朝向误差: 93.7°
[14:12:21.667] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.5° | 朝向误差: 93.7°
[14:12:21.684] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.5° | 朝向误差: 93.7°
[14:12:21.700] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.4° | 朝向误差: 93.7°
[14:12:21.726] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.4° | 朝向误差: 93.7°
[14:12:21.744] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.4° | 朝向误差: 93.7°
[14:12:21.762] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.4° | 朝向误差: 93.7°
[14:12:21.785] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.3° | 朝向误差: 93.7°
=== ROV控制状态 Frame 1010 Time 20.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.654, 2.860, -0.876)
位置误差向量: (2.870, -0.123, 0.876), 距离: 3.0032m
当前速度: (0.3912, -0.0149, 0.0590), 速度大小: 0.3959m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-9.080, Y:0.391, Z:-2.721
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.016, Z=0.066, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 339.34, 356.67)
当前角速度: (0.003, -1.186, 0.002)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.69°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.353, 0.000, 0.936)
目标方向: (0.956, -0.041, 0.292)
方向点积: -0.064
最终旋转输出: 0.000
==================================================

[14:12:21.810] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.3° | 朝向误差: 93.7°
[14:12:21.821] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.3° | 朝向误差: 93.7°
[14:12:21.844] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.3° | 朝向误差: 93.7°
[14:12:21.862] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.2° | 朝向误差: 93.7°
[14:12:21.880] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.2° | 朝向误差: 93.7°
[14:12:21.905] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.2° | 朝向误差: 93.7°
[14:12:21.921] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.2° | 朝向误差: 93.7°
[14:12:21.946] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.2° | 朝向误差: 93.7°
[14:12:21.964] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.1° | 朝向误差: 93.7°
[14:12:21.982] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.1° | 朝向误差: 93.7°
=== ROV控制状态 Frame 1020 Time 20.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.733, 2.857, -0.863)
位置误差向量: (2.792, -0.120, 0.863), 距离: 2.9248m
当前速度: (0.3905, -0.0152, 0.0621), 速度大小: 0.3957m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-8.844, Y:0.382, Z:-2.689
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.015, Z=0.064, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 339.10, 356.67)
当前角速度: (0.003, -1.189, 0.003)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.71°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.357, 0.000, 0.934)
目标方向: (0.955, -0.041, 0.295)
方向点积: -0.065
最终旋转输出: 0.000
==================================================

[14:12:22.006] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.1° | 朝向误差: 93.7°
[14:12:22.022] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.1° | 朝向误差: 93.7°
[14:12:22.046] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.0° | 朝向误差: 93.7°
[14:12:22.064] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.0° | 朝向误差: 93.7°
[14:12:22.082] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.0° | 朝向误差: 93.7°
[14:12:22.115] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 339.0° | 朝向误差: 93.7°
[14:12:22.125] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.9° | 朝向误差: 93.7°
[14:12:22.142] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.9° | 朝向误差: 93.7°
[14:12:22.167] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.9° | 朝向误差: 93.7°
[14:12:22.183] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.9° | 朝向误差: 93.7°
=== ROV控制状态 Frame 1030 Time 20.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.811, 2.853, -0.851)
位置误差向量: (2.714, -0.117, 0.851), 距离: 2.8465m
当前速度: (0.3897, -0.0154, 0.0652), 速度大小: 0.3954m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-8.609, Y:0.373, Z:-2.655
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.015, Z=0.063, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 338.86, 356.67)
当前角速度: (0.002, -1.190, 0.003)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.73°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.361, 0.000, 0.933)
目标方向: (0.953, -0.041, 0.299)
方向点积: -0.065
最终旋转输出: 0.000
==================================================

[14:12:22.206] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.8° | 朝向误差: 93.7°
[14:12:22.224] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.8° | 朝向误差: 93.7°
[14:12:22.242] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.8° | 朝向误差: 93.7°
[14:12:22.267] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.8° | 朝向误差: 93.7°
[14:12:22.286] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.7° | 朝向误差: 93.7°
[14:12:22.302] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.7° | 朝向误差: 93.7°
[14:12:22.327] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.7° | 朝向误差: 93.7°
[14:12:22.343] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.7° | 朝向误差: 93.7°
[14:12:22.360] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 338.7° | 朝向误差: 93.7°
[14:12:22.384] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.6° | 朝向误差: 93.8°
=== ROV控制状态 Frame 1040 Time 20.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.888, 2.850, -0.837)
位置误差向量: (2.636, -0.113, 0.837), 距离: 2.7681m
当前速度: (0.3890, -0.0156, 0.0682), 速度大小: 0.3952m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-8.375, Y:0.364, Z:-2.618
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.014, Z=0.061, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 338.63, 356.67)
当前角速度: (0.002, -1.193, 0.003)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.75°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.364, 0.000, 0.931)
目标方向: (0.952, -0.041, 0.302)
方向点积: -0.065
最终旋转输出: 0.000
==================================================

[14:12:22.400] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.6° | 朝向误差: 93.8°
[14:12:22.425] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.6° | 朝向误差: 93.8°
[14:12:22.443] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.6° | 朝向误差: 93.8°
[14:12:22.467] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.5° | 朝向误差: 93.8°
[14:12:22.485] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.5° | 朝向误差: 93.8°
[14:12:22.501] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.5° | 朝向误差: 93.8°
[14:12:22.525] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.5° | 朝向误差: 93.8°
[14:12:22.542] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.4° | 朝向误差: 93.8°
[14:12:22.566] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.4° | 朝向误差: 93.8°
[14:12:22.585] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.4° | 朝向误差: 93.8°
=== ROV控制状态 Frame 1050 Time 21.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (26.966, 2.847, -0.823)
位置误差向量: (2.558, -0.110, 0.823), 距离: 2.6898m
当前速度: (0.3881, -0.0158, 0.0713), 速度大小: 0.3950m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-8.141, Y:0.355, Z:-2.581
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.014, Z=0.060, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 338.39, 356.67)
当前角速度: (0.002, -1.193, 0.005)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.77°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.368, 0.000, 0.930)
目标方向: (0.951, -0.041, 0.306)
方向点积: -0.066
最终旋转输出: 0.000
==================================================

[14:12:22.602] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.4° | 朝向误差: 93.8°
[14:12:22.627] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.3° | 朝向误差: 93.8°
[14:12:22.644] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.3° | 朝向误差: 93.8°
[14:12:22.661] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.3° | 朝向误差: 93.8°
[14:12:22.686] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.3° | 朝向误差: 93.8°
[14:12:22.703] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.2° | 朝向误差: 93.8°
[14:12:22.721] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.2° | 朝向误差: 93.8°
[14:12:22.747] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.2° | 朝向误差: 93.8°
[14:12:22.765] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.2° | 朝向误差: 93.8°
[14:12:22.781] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.1° | 朝向误差: 93.8°
=== ROV控制状态 Frame 1060 Time 21.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.044, 2.844, -0.809)
位置误差向量: (2.481, -0.107, 0.809), 距离: 2.6115m
当前速度: (0.3874, -0.0160, 0.0744), 速度大小: 0.3948m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-7.907, Y:0.346, Z:-2.541
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.014, Z=0.058, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 338.15, 356.67)
当前角速度: (0.002, -1.196, 0.006)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.79°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.372, 0.000, 0.928)
目标方向: (0.950, -0.041, 0.310)
方向点积: -0.066
最终旋转输出: 0.000
==================================================

[14:12:22.805] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.1° | 朝向误差: 93.8°
[14:12:22.822] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.1° | 朝向误差: 93.8°
[14:12:22.847] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.1° | 朝向误差: 93.8°
[14:12:22.864] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.1° | 朝向误差: 93.8°
[14:12:22.888] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.0° | 朝向误差: 93.8°
[14:12:22.900] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.0° | 朝向误差: 93.8°
[14:12:22.925] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.0° | 朝向误差: 93.8°
[14:12:22.942] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 338.0° | 朝向误差: 93.8°
[14:12:22.967] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.9° | 朝向误差: 93.8°
[14:12:22.983] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.9° | 朝向误差: 93.8°
=== ROV控制状态 Frame 1070 Time 21.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.121, 2.841, -0.794)
位置误差向量: (2.403, -0.104, 0.794), 距离: 2.5331m
当前速度: (0.3865, -0.0162, 0.0774), 速度大小: 0.3945m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-7.674, Y:0.336, Z:-2.499
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.800, Y=-0.013, Z=0.057, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 337.91, 356.67)
当前角速度: (0.002, -1.198, 0.006)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.82°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.376, 0.000, 0.927)
目标方向: (0.949, -0.041, 0.313)
方向点积: -0.067
最终旋转输出: 0.000
==================================================

[14:12:23.000] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.9° | 朝向误差: 93.8°
[14:12:23.025] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.9° | 朝向误差: 93.8°
[14:12:23.043] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.8° | 朝向误差: 93.8°
[14:12:23.066] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.8° | 朝向误差: 93.8°
[14:12:23.083] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.8° | 朝向误差: 93.8°
[14:12:23.102] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.8° | 朝向误差: 93.8°
[14:12:23.127] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.7° | 朝向误差: 93.8°
[14:12:23.144] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.7° | 朝向误差: 93.8°
[14:12:23.160] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.7° | 朝向误差: 93.8°
[14:12:23.194] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.7° | 朝向误差: 93.8°
=== ROV控制状态 Frame 1080 Time 21.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.198, 2.838, -0.778)
位置误差向量: (2.326, -0.101, 0.778), 距离: 2.4549m
当前速度: (0.3843, -0.0163, 0.0800), 速度大小: 0.3929m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-7.440, Y:0.327, Z:-2.455
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.782, Y=-0.013, Z=0.056, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 337.67, 356.68)
当前角速度: (0.002, -1.192, 0.041)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.84°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.380, 0.000, 0.925)
目标方向: (0.948, -0.041, 0.317)
方向点积: -0.067
最终旋转输出: 0.000
==================================================

[14:12:23.205] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.6° | 朝向误差: 93.8°
[14:12:23.222] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 337.6° | 朝向误差: 93.8°
[14:12:23.248] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.6° | 朝向误差: 93.9°
[14:12:23.264] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.6° | 朝向误差: 93.9°
[14:12:23.281] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.6° | 朝向误差: 93.9°
[14:12:23.307] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.5° | 朝向误差: 93.9°
[14:12:23.323] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.5° | 朝向误差: 93.9°
[14:12:23.347] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.5° | 朝向误差: 93.9°
[14:12:23.365] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.5° | 朝向误差: 93.9°
[14:12:23.382] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.4° | 朝向误差: 93.9°
=== ROV控制状态 Frame 1090 Time 21.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.274, 2.834, -0.762)
位置误差向量: (2.250, -0.097, 0.762), 距离: 2.3774m
当前速度: (0.3789, -0.0162, 0.0811), 速度大小: 0.3878m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-7.205, Y:0.317, Z:-2.408
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.758, Y=-0.012, Z=0.054, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 337.43, 356.70)
当前角速度: (0.002, -1.172, 0.133)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.87°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.384, 0.000, 0.923)
目标方向: (0.946, -0.041, 0.320)
方向点积: -0.067
最终旋转输出: 0.000
==================================================

[14:12:23.406] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.4° | 朝向误差: 93.9°
[14:12:23.425] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.4° | 朝向误差: 93.9°
[14:12:23.442] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.4° | 朝向误差: 93.9°
[14:12:23.465] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.3° | 朝向误差: 93.9°
[14:12:23.482] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.3° | 朝向误差: 93.9°
[14:12:23.507] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.3° | 朝向误差: 93.9°
[14:12:23.525] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.3° | 朝向误差: 93.9°
[14:12:23.542] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.2° | 朝向误差: 93.9°
[14:12:23.566] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.2° | 朝向误差: 93.9°
[14:12:23.584] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.2° | 朝向误差: 93.9°
=== ROV控制状态 Frame 1100 Time 22.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.350, 2.831, -0.745)
位置误差向量: (2.175, -0.094, 0.745), 距离: 2.3010m
当前速度: (0.3721, -0.0160, 0.0815), 速度大小: 0.3813m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-6.971, Y:0.307, Z:-2.360
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.735, Y=-0.011, Z=0.053, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 337.20, 356.73)
当前角速度: (0.002, -1.149, 0.209)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.88°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.387, 0.000, 0.922)
目标方向: (0.945, -0.041, 0.324)
方向点积: -0.068
最终旋转输出: 0.000
==================================================

[14:12:23.601] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.2° | 朝向误差: 93.9°
[14:12:23.626] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.2° | 朝向误差: 93.9°
[14:12:23.643] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.1° | 朝向误差: 93.9°
[14:12:23.662] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.1° | 朝向误差: 93.9°
[14:12:23.685] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.1° | 朝向误差: 93.9°
[14:12:23.702] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.1° | 朝向误差: 93.9°
[14:12:23.727] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.0° | 朝向误差: 93.9°
[14:12:23.744] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.0° | 朝向误差: 93.9°
[14:12:23.762] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.0° | 朝向误差: 93.9°
[14:12:23.780] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.0° | 朝向误差: 93.9°
=== ROV控制状态 Frame 1110 Time 22.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.423, 2.828, -0.729)
位置误差向量: (2.101, -0.091, 0.729), 距离: 2.2260m
当前速度: (0.3649, -0.0157, 0.0816), 速度大小: 0.3742m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-6.742, Y:0.297, Z:-2.312
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.711, Y=-0.010, Z=0.051, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 336.97, 356.78)
当前角速度: (0.003, -1.128, 0.255)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.89°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.391, 0.000, 0.920)
目标方向: (0.944, -0.041, 0.328)
方向点积: -0.068
最终旋转输出: 0.000
==================================================

[14:12:23.812] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 337.0° | 朝向误差: 93.9°
[14:12:23.822] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.9° | 朝向误差: 93.9°
[14:12:23.846] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.9° | 朝向误差: 93.9°
[14:12:23.864] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.9° | 朝向误差: 93.9°
[14:12:23.880] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.9° | 朝向误差: 93.9°
[14:12:23.906] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.8° | 朝向误差: 93.9°
[14:12:23.925] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.8° | 朝向误差: 93.9°
[14:12:23.941] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.8° | 朝向误差: 93.9°
[14:12:23.967] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.8° | 朝向误差: 93.9°
[14:12:23.985] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.8° | 朝向误差: 93.9°
=== ROV控制状态 Frame 1120 Time 22.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.495, 2.825, -0.713)
位置误差向量: (2.029, -0.088, 0.713), 距离: 2.1525m
当前速度: (0.3573, -0.0153, 0.0815), 速度大小: 0.3668m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-6.516, Y:0.288, Z:-2.263
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.689, Y=-0.009, Z=0.049, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 336.75, 356.83)
当前角速度: (0.002, -1.105, 0.278)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.90°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.395, 0.000, 0.919)
目标方向: (0.943, -0.041, 0.331)
方向点积: -0.068
最终旋转输出: 0.000
==================================================

[14:12:24.001] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.7° | 朝向误差: 93.9°
[14:12:24.026] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.7° | 朝向误差: 93.9°
[14:12:24.044] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.7° | 朝向误差: 93.9°
[14:12:24.061] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.7° | 朝向误差: 93.9°
[14:12:24.084] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.6° | 朝向误差: 93.9°
[14:12:24.101] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.6° | 朝向误差: 93.9°
[14:12:24.128] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.6° | 朝向误差: 93.9°
[14:12:24.145] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.6° | 朝向误差: 93.9°
[14:12:24.162] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.6° | 朝向误差: 93.9°
[14:12:24.187] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.5° | 朝向误差: 93.9°
=== ROV控制状态 Frame 1130 Time 22.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.566, 2.822, -0.696)
位置误差向量: (1.959, -0.085, 0.696), 距离: 2.0804m
当前速度: (0.3497, -0.0150, 0.0813), 速度大小: 0.3594m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-6.296, Y:0.278, Z:-2.214
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.666, Y=-0.008, Z=0.048, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 336.53, 356.89)
当前角速度: (0.002, -1.085, 0.284)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.89°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.398, 0.000, 0.917)
目标方向: (0.941, -0.041, 0.335)
方向点积: -0.068
最终旋转输出: 0.000
==================================================

[14:12:24.204] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.5° | 朝向误差: 93.9°
[14:12:24.221] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.5° | 朝向误差: 93.9°
[14:12:24.246] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.5° | 朝向误差: 93.9°
[14:12:24.272] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.4° | 朝向误差: 93.9°
[14:12:24.281] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.4° | 朝向误差: 93.9°
[14:12:24.306] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.4° | 朝向误差: 93.9°
[14:12:24.323] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.4° | 朝向误差: 93.9°
[14:12:24.341] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.4° | 朝向误差: 93.9°
[14:12:24.365] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.3° | 朝向误差: 93.9°
[14:12:24.383] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.3° | 朝向误差: 93.9°
=== ROV控制状态 Frame 1140 Time 22.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.635, 2.819, -0.680)
位置误差向量: (1.889, -0.082, 0.680), 距离: 2.0098m
当前速度: (0.3421, -0.0147, 0.0811), 速度大小: 0.3519m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-6.080, Y:0.269, Z:-2.166
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.644, Y=-0.007, Z=0.046, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 336.32, 356.95)
当前角速度: (0.003, -1.065, 0.281)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.88°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.402, 0.000, 0.916)
目标方向: (0.940, -0.041, 0.338)
方向点积: -0.068
最终旋转输出: 0.000
==================================================

[14:12:24.400] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.3° | 朝向误差: 93.9°
[14:12:24.426] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.3° | 朝向误差: 93.9°
[14:12:24.443] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.3° | 朝向误差: 93.9°
[14:12:24.461] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.2° | 朝向误差: 93.9°
[14:12:24.485] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.2° | 朝向误差: 93.9°
[14:12:24.502] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.2° | 朝向误差: 93.9°
[14:12:24.526] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.2° | 朝向误差: 93.9°
[14:12:24.546] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.1° | 朝向误差: 93.9°
[14:12:24.563] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.1° | 朝向误差: 93.9°
[14:12:24.582] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.1° | 朝向误差: 93.9°
=== ROV控制状态 Frame 1150 Time 23.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.703, 2.816, -0.664)
位置误差向量: (1.822, -0.079, 0.664), 距离: 1.9407m
当前速度: (0.3345, -0.0144, 0.0808), 速度大小: 0.3445m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-5.869, Y:0.260, Z:-2.117
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.623, Y=-0.007, Z=0.044, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 336.11, 357.00)
当前角速度: (0.003, -1.044, 0.276)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.87°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.405, 0.000, 0.914)
目标方向: (0.939, -0.041, 0.342)
方向点积: -0.067
最终旋转输出: 0.000
==================================================

[14:12:24.607] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.1° | 朝向误差: 93.9°
[14:12:24.624] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.1° | 朝向误差: 93.9°
[14:12:24.641] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.0° | 朝向误差: 93.9°
[14:12:24.664] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.0° | 朝向误差: 93.9°
[14:12:24.681] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.0° | 朝向误差: 93.9°
[14:12:24.706] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.0° | 朝向误差: 93.9°
[14:12:24.731] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 336.0° | 朝向误差: 93.9°
[14:12:24.743] 旋转事件: 快速旋转模式 - 误差: 93.9°, 输出: 2.000 | 当前朝向: 335.9° | 朝向误差: 93.9°
[14:12:24.760] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.9° | 朝向误差: 93.8°
[14:12:24.785] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.9° | 朝向误差: 93.8°
=== ROV控制状态 Frame 1160 Time 23.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.769, 2.813, -0.648)
位置误差向量: (1.756, -0.076, 0.648), 距离: 1.8731m
当前速度: (0.3268, -0.0140, 0.0805), 速度大小: 0.3369m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-5.662, Y:0.251, Z:-2.068
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.602, Y=-0.006, Z=0.042, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (359.99, 335.90, 357.06)
当前角速度: (0.003, -1.024, 0.272)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.85°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.408, 0.000, 0.913)
目标方向: (0.937, -0.041, 0.346)
方向点积: -0.067
最终旋转输出: 0.000
==================================================

[14:12:24.801] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.9° | 朝向误差: 93.8°
[14:12:24.825] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.9° | 朝向误差: 93.8°
[14:12:24.842] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.8° | 朝向误差: 93.8°
[14:12:24.865] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.8° | 朝向误差: 93.8°
[14:12:24.884] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.8° | 朝向误差: 93.8°
[14:12:24.902] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.8° | 朝向误差: 93.8°
[14:12:24.926] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.8° | 朝向误差: 93.8°
[14:12:24.944] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.7° | 朝向误差: 93.8°
[14:12:24.961] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.7° | 朝向误差: 93.8°
[14:12:24.985] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.7° | 朝向误差: 93.8°
=== ROV控制状态 Frame 1170 Time 23.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.833, 2.810, -0.632)
位置误差向量: (1.691, -0.073, 0.632), 距离: 1.8069m
当前速度: (0.3193, -0.0137, 0.0802), 速度大小: 0.3295m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-5.460, Y:0.242, Z:-2.020
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.581, Y=-0.005, Z=0.041, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (-0.01, 335.70, 357.11)
当前角速度: (0.002, -1.004, 0.269)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.82°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.412, 0.000, 0.911)
目标方向: (0.936, -0.041, 0.350)
方向点积: -0.067
最终旋转输出: 0.000
==================================================

[14:12:25.002] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.7° | 朝向误差: 93.8°
[14:12:25.034] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.7° | 朝向误差: 93.8°
[14:12:25.047] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.6° | 朝向误差: 93.8°
[14:12:25.068] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.6° | 朝向误差: 93.8°
[14:12:25.080] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.6° | 朝向误差: 93.8°
[14:12:25.107] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.6° | 朝向误差: 93.8°
[14:12:25.124] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.6° | 朝向误差: 93.8°
[14:12:25.143] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.5° | 朝向误差: 93.8°
[14:12:25.163] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.5° | 朝向误差: 93.8°
[14:12:25.193] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.5° | 朝向误差: 93.8°
=== ROV控制状态 Frame 1180 Time 23.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.896, 2.808, -0.616)
位置误差向量: (1.628, -0.071, 0.616), 距离: 1.7423m
当前速度: (0.3117, -0.0134, 0.0798), 速度大小: 0.3220m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-5.263, Y:0.234, Z:-1.972
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.561, Y=-0.004, Z=0.039, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 335.50, 357.16)
当前角速度: (0.002, -0.984, 0.267)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.78°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.415, 0.000, 0.910)
目标方向: (0.935, -0.041, 0.353)
方向点积: -0.066
最终旋转输出: 0.000
==================================================

[14:12:25.205] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.5° | 朝向误差: 93.8°
[14:12:25.224] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.5° | 朝向误差: 93.8°
[14:12:25.243] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.4° | 朝向误差: 93.8°
[14:12:25.262] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.4° | 朝向误差: 93.8°
[14:12:25.281] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.4° | 朝向误差: 93.8°
[14:12:25.306] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.4° | 朝向误差: 93.8°
[14:12:25.325] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.4° | 朝向误差: 93.8°
[14:12:25.345] 旋转事件: 快速旋转模式 - 误差: 93.8°, 输出: 2.000 | 当前朝向: 335.3° | 朝向误差: 93.8°
[14:12:25.367] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.3° | 朝向误差: 93.7°
[14:12:25.385] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.3° | 朝向误差: 93.7°
=== ROV控制状态 Frame 1190 Time 23.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (27.958, 2.805, -0.600)
位置误差向量: (1.567, -0.068, 0.600), 距离: 1.6791m
当前速度: (0.3041, -0.0131, 0.0795), 速度大小: 0.3146m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-5.070, Y:0.226, Z:-1.924
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.541, Y=-0.004, Z=0.037, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 335.30, 357.22)
当前角速度: (0.002, -0.966, 0.266)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.74°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.418, 0.000, 0.909)
目标方向: (0.933, -0.041, 0.357)
方向点积: -0.065
最终旋转输出: 0.000
==================================================

[14:12:25.404] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.3° | 朝向误差: 93.7°
[14:12:25.422] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.3° | 朝向误差: 93.7°
[14:12:25.447] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.2° | 朝向误差: 93.7°
[14:12:25.465] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.2° | 朝向误差: 93.7°
[14:12:25.491] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.2° | 朝向误差: 93.7°
[14:12:25.502] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.2° | 朝向误差: 93.7°
[14:12:25.527] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.2° | 朝向误差: 93.7°
[14:12:25.545] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.2° | 朝向误差: 93.7°
[14:12:25.565] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.1° | 朝向误差: 93.7°
[14:12:25.585] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.1° | 朝向误差: 93.7°
=== ROV控制状态 Frame 1200 Time 24.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.018, 2.802, -0.584)
位置误差向量: (1.507, -0.065, 0.584), 距离: 1.6173m
当前速度: (0.2966, -0.0127, 0.0791), 速度大小: 0.3072m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-4.882, Y:0.218, Z:-1.876
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.522, Y=-0.003, Z=0.035, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 335.11, 357.27)
当前角速度: (0.002, -0.946, 0.267)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.70°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.421, 0.000, 0.907)
目标方向: (0.932, -0.040, 0.361)
方向点积: -0.064
最终旋转输出: 0.000
==================================================

[14:12:25.603] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.1° | 朝向误差: 93.7°
[14:12:25.621] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.1° | 朝向误差: 93.7°
[14:12:25.649] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.1° | 朝向误差: 93.7°
[14:12:25.661] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.0° | 朝向误差: 93.7°
[14:12:25.682] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.0° | 朝向误差: 93.7°
[14:12:25.704] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.0° | 朝向误差: 93.7°
[14:12:25.724] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.0° | 朝向误差: 93.7°
[14:12:25.744] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 335.0° | 朝向误差: 93.7°
[14:12:25.764] 旋转事件: 快速旋转模式 - 误差: 93.7°, 输出: 2.000 | 当前朝向: 334.9° | 朝向误差: 93.7°
[14:12:25.785] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.9° | 朝向误差: 93.6°
=== ROV控制状态 Frame 1210 Time 24.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.076, 2.800, -0.568)
位置误差向量: (1.448, -0.063, 0.568), 距离: 1.5571m
当前速度: (0.2891, -0.0124, 0.0787), 速度大小: 0.2998m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-4.698, Y:0.210, Z:-1.829
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.503, Y=-0.003, Z=0.033, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 334.93, 357.32)
当前角速度: (0.002, -0.926, 0.269)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.65°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.424, 0.000, 0.906)
目标方向: (0.930, -0.040, 0.365)
方向点积: -0.064
最终旋转输出: 0.000
==================================================

[14:12:25.802] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.9° | 朝向误差: 93.6°
[14:12:25.820] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.9° | 朝向误差: 93.6°
[14:12:25.846] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.9° | 朝向误差: 93.6°
[14:12:25.863] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.9° | 朝向误差: 93.6°
[14:12:25.880] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.8° | 朝向误差: 93.6°
[14:12:25.906] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.8° | 朝向误差: 93.6°
[14:12:25.924] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.8° | 朝向误差: 93.6°
[14:12:25.951] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.8° | 朝向误差: 93.6°
[14:12:25.961] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.8° | 朝向误差: 93.6°
[14:12:25.980] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.7° | 朝向误差: 93.6°
=== ROV控制状态 Frame 1220 Time 24.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.133, 2.797, -0.553)
位置误差向量: (1.391, -0.060, 0.553), 距离: 1.4982m
当前速度: (0.2816, -0.0121, 0.0783), 速度大小: 0.2925m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-4.518, Y:0.202, Z:-1.781
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.485, Y=-0.002, Z=0.032, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 334.74, 357.38)
当前角速度: (0.002, -0.909, 0.270)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.60°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.427, 0.000, 0.904)
目标方向: (0.929, -0.040, 0.369)
方向点积: -0.063
最终旋转输出: 0.000
==================================================

[14:12:26.007] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.7° | 朝向误差: 93.6°
[14:12:26.023] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.7° | 朝向误差: 93.6°
[14:12:26.041] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.7° | 朝向误差: 93.6°
[14:12:26.065] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.7° | 朝向误差: 93.6°
[14:12:26.083] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.7° | 朝向误差: 93.6°
[14:12:26.109] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.6° | 朝向误差: 93.6°
[14:12:26.126] 旋转事件: 快速旋转模式 - 误差: 93.6°, 输出: 2.000 | 当前朝向: 334.6° | 朝向误差: 93.6°
[14:12:26.144] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.6° | 朝向误差: 93.5°
[14:12:26.160] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.6° | 朝向误差: 93.5°
[14:12:26.186] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.6° | 朝向误差: 93.5°
=== ROV控制状态 Frame 1230 Time 24.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.189, 2.795, -0.537)
位置误差向量: (1.336, -0.058, 0.537), 距离: 1.4408m
当前速度: (0.2743, -0.0118, 0.0778), 速度大小: 0.2854m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-4.343, Y:0.194, Z:-1.734
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.467, Y=-0.001, Z=0.030, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 334.56, 357.43)
当前角速度: (0.002, -0.888, 0.273)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.54°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.430, 0.000, 0.903)
目标方向: (0.927, -0.040, 0.373)
方向点积: -0.062
最终旋转输出: 0.000
==================================================

[14:12:26.204] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.5° | 朝向误差: 93.5°
[14:12:26.227] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.5° | 朝向误差: 93.5°
[14:12:26.243] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.5° | 朝向误差: 93.5°
[14:12:26.262] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.5° | 朝向误差: 93.5°
[14:12:26.286] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.5° | 朝向误差: 93.5°
[14:12:26.304] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.5° | 朝向误差: 93.5°
[14:12:26.323] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.4° | 朝向误差: 93.5°
[14:12:26.347] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.4° | 朝向误差: 93.5°
[14:12:26.364] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.4° | 朝向误差: 93.5°
[14:12:26.380] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.4° | 朝向误差: 93.5°
=== ROV控制状态 Frame 1240 Time 24.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.243, 2.793, -0.521)
位置误差向量: (1.282, -0.056, 0.521), 距离: 1.3849m
当前速度: (0.2669, -0.0115, 0.0774), 速度大小: 0.2782m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-4.173, Y:0.187, Z:-1.687
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.450, Y=-0.001, Z=0.028, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 334.39, 357.49)
当前角速度: (0.002, -0.871, 0.275)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.47°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.432, 0.000, 0.902)
目标方向: (0.926, -0.040, 0.377)
方向点积: -0.061
最终旋转输出: 0.000
==================================================

[14:12:26.415] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.4° | 朝向误差: 93.5°
[14:12:26.425] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.4° | 朝向误差: 93.5°
[14:12:26.442] 旋转事件: 快速旋转模式 - 误差: 93.5°, 输出: 2.000 | 当前朝向: 334.3° | 朝向误差: 93.5°
[14:12:26.467] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.3° | 朝向误差: 93.4°
[14:12:26.484] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.3° | 朝向误差: 93.4°
[14:12:26.500] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.3° | 朝向误差: 93.4°
[14:12:26.525] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.3° | 朝向误差: 93.4°
[14:12:26.544] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.2° | 朝向误差: 93.4°
[14:12:26.569] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.2° | 朝向误差: 93.4°
[14:12:26.585] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.2° | 朝向误差: 93.4°
=== ROV控制状态 Frame 1250 Time 25.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.295, 2.791, -0.506)
位置误差向量: (1.229, -0.054, 0.506), 距离: 1.3303m
当前速度: (0.2597, -0.0111, 0.0769), 速度大小: 0.2711m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-4.007, Y:0.180, Z:-1.641
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.433, Y=-0.001, Z=0.027, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 334.22, 357.54)
当前角速度: (0.002, -0.851, 0.276)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.41°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.435, 0.000, 0.900)
目标方向: (0.924, -0.040, 0.380)
方向点积: -0.059
最终旋转输出: 0.000
==================================================

[14:12:26.604] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.2° | 朝向误差: 93.4°
[14:12:26.643] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.2° | 朝向误差: 93.4°
[14:12:26.645] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.2° | 朝向误差: 93.4°
[14:12:26.674] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.1° | 朝向误差: 93.4°
[14:12:26.693] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.1° | 朝向误差: 93.4°
[14:12:26.704] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.1° | 朝向误差: 93.4°
[14:12:26.724] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.1° | 朝向误差: 93.4°
[14:12:26.740] 旋转事件: 快速旋转模式 - 误差: 93.4°, 输出: 2.000 | 当前朝向: 334.1° | 朝向误差: 93.4°
[14:12:26.764] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 334.1° | 朝向误差: 93.3°
[14:12:26.780] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 334.0° | 朝向误差: 93.3°
=== ROV控制状态 Frame 1260 Time 25.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.346, 2.788, -0.491)
位置误差向量: (1.178, -0.051, 0.491), 距离: 1.2771m
当前速度: (0.2524, -0.0108, 0.0764), 速度大小: 0.2640m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-3.845, Y:0.173, Z:-1.594
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.416, Y=0.000, Z=0.025, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 334.05, 357.60)
当前角速度: (0.002, -0.835, 0.279)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.34°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.438, 0.000, 0.899)
目标方向: (0.922, -0.040, 0.384)
方向点积: -0.058
最终旋转输出: 0.000
==================================================

[14:12:26.801] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 334.0° | 朝向误差: 93.3°
[14:12:26.824] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 334.0° | 朝向误差: 93.3°
[14:12:26.841] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 334.0° | 朝向误差: 93.3°
[14:12:26.863] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 334.0° | 朝向误差: 93.3°
[14:12:26.887] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 334.0° | 朝向误差: 93.3°
[14:12:26.903] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 333.9° | 朝向误差: 93.3°
[14:12:26.920] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 333.9° | 朝向误差: 93.3°
[14:12:26.944] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 333.9° | 朝向误差: 93.3°
[14:12:26.967] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 333.9° | 朝向误差: 93.3°
[14:12:26.984] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 333.9° | 朝向误差: 93.3°
=== ROV控制状态 Frame 1270 Time 25.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.396, 2.786, -0.475)
位置误差向量: (1.128, -0.049, 0.475), 距离: 1.2254m
当前速度: (0.2453, -0.0107, 0.0758), 速度大小: 0.2570m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-3.688, Y:0.209, Z:-1.548
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.400, Y=0.005, Z=0.023, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 333.88, 357.65)
当前角速度: (0.002, -0.816, 0.279)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.27°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.440, 0.000, 0.898)
目标方向: (0.921, -0.040, 0.388)
方向点积: -0.057
最终旋转输出: 0.000
==================================================

[14:12:27.003] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 333.9° | 朝向误差: 93.3°
[14:12:27.025] 旋转事件: 快速旋转模式 - 误差: 93.3°, 输出: 2.000 | 当前朝向: 333.9° | 朝向误差: 93.3°
[14:12:27.041] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.8° | 朝向误差: 93.2°
[14:12:27.064] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.8° | 朝向误差: 93.2°
[14:12:27.087] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.8° | 朝向误差: 93.2°
[14:12:27.104] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.8° | 朝向误差: 93.2°
[14:12:27.126] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.8° | 朝向误差: 93.2°
[14:12:27.142] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.8° | 朝向误差: 93.2°
[14:12:27.165] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.7° | 朝向误差: 93.2°
[14:12:27.184] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.7° | 朝向误差: 93.2°
=== ROV控制状态 Frame 1280 Time 25.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.444, 2.784, -0.460)
位置误差向量: (1.080, -0.047, 0.460), 距离: 1.1750m
当前速度: (0.2383, -0.0110, 0.0752), 速度大小: 0.2502m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-3.535, Y:0.204, Z:-1.502
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.384, Y=0.005, Z=0.022, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 333.72, 357.71)
当前角速度: (0.002, -0.797, 0.281)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.19°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.443, 0.000, 0.897)
目标方向: (0.919, -0.040, 0.392)
方向点积: -0.056
最终旋转输出: 0.000
==================================================

[14:12:27.204] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.7° | 朝向误差: 93.2°
[14:12:27.221] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.7° | 朝向误差: 93.2°
[14:12:27.244] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.7° | 朝向误差: 93.2°
[14:12:27.267] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.7° | 朝向误差: 93.2°
[14:12:27.284] 旋转事件: 快速旋转模式 - 误差: 93.2°, 输出: 2.000 | 当前朝向: 333.6° | 朝向误差: 93.2°
[14:12:27.301] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 333.6° | 朝向误差: 93.1°
[14:12:27.324] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 333.6° | 朝向误差: 93.1°
[14:12:27.353] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 333.6° | 朝向误差: 93.1°
[14:12:27.373] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 333.6° | 朝向误差: 93.1°
[14:12:27.386] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 333.6° | 朝向误差: 93.1°
=== ROV控制状态 Frame 1290 Time 25.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.491, 2.782, -0.445)
位置误差向量: (1.033, -0.045, 0.445), 距离: 1.1259m
当前速度: (0.2313, -0.0111, 0.0745), 速度大小: 0.2432m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-3.387, Y:0.198, Z:-1.457
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.369, Y=0.005, Z=0.020, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 333.56, 357.77)
当前角速度: (-0.017, -0.778, 0.293)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.11°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.445, 0.000, 0.895)
目标方向: (0.918, -0.040, 0.396)
方向点积: -0.054
最终旋转输出: 0.000
==================================================

[14:12:27.404] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 333.5° | 朝向误差: 93.1°
[14:12:27.420] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 333.5° | 朝向误差: 93.1°
[14:12:27.445] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 333.5° | 朝向误差: 93.1°
[14:12:27.462] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 333.5° | 朝向误差: 93.1°
[14:12:27.486] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 333.5° | 朝向误差: 93.1°
[14:12:27.503] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 333.5° | 朝向误差: 93.1°
[14:12:27.522] 旋转事件: 快速旋转模式 - 误差: 93.1°, 输出: 2.000 | 当前朝向: 333.5° | 朝向误差: 93.1°
[14:12:27.545] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.4° | 朝向误差: 93.0°
[14:12:27.562] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.4° | 朝向误差: 93.0°
[14:12:27.588] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.4° | 朝向误差: 93.0°
=== ROV控制状态 Frame 1300 Time 26.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.537, 2.780, -0.431)
位置误差向量: (0.988, -0.043, 0.431), 距离: 1.0782m
当前速度: (0.2243, -0.0111, 0.0740), 速度大小: 0.2365m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-3.242, Y:0.192, Z:-1.412
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.354, Y=0.006, Z=0.019, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 333.41, 357.82)
当前角速度: (0.014, -0.761, 0.273)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 93.03°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.448, 0.000, 0.894)
目标方向: (0.916, -0.039, 0.399)
方向点积: -0.053
最终旋转输出: 0.000
==================================================

[14:12:27.608] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.4° | 朝向误差: 93.0°
[14:12:27.625] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.4° | 朝向误差: 93.0°
[14:12:27.641] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.4° | 朝向误差: 93.0°
[14:12:27.673] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.3° | 朝向误差: 93.0°
[14:12:27.683] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.3° | 朝向误差: 93.0°
[14:12:27.700] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.3° | 朝向误差: 93.0°
[14:12:27.724] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.3° | 朝向误差: 93.0°
[14:12:27.741] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.3° | 朝向误差: 93.0°
[14:12:27.766] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.3° | 朝向误差: 93.0°
[14:12:27.783] 旋转事件: 快速旋转模式 - 误差: 93.0°, 输出: 2.000 | 当前朝向: 333.3° | 朝向误差: 93.0°
=== ROV控制状态 Frame 1310 Time 26.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.581, 2.777, -0.416)
位置误差向量: (0.944, -0.040, 0.416), 距离: 1.0319m
当前速度: (0.2175, -0.0111, 0.0732), 速度大小: 0.2298m/s
控制阶段: 全速接近, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-3.102, Y:0.185, Z:-1.367
速度因子: 0.800, 自适应速度因子: 0.800
最终轴输出: X=-0.339, Y=0.006, Z=0.018, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 333.26, 357.88)
当前角速度: (-0.003, -0.743, 0.285)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.95°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.450, 0.000, 0.893)
目标方向: (0.914, -0.039, 0.403)
方向点积: -0.051
最终旋转输出: 0.000
==================================================

[14:12:27.809] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 333.2° | 朝向误差: 92.9°
[14:12:27.826] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 333.2° | 朝向误差: 92.9°
[14:12:27.843] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 333.2° | 朝向误差: 92.9°
[14:12:27.867] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 333.2° | 朝向误差: 92.9°
[14:12:27.884] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 333.2° | 朝向误差: 92.9°
[14:12:27.901] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 333.2° | 朝向误差: 92.9°
[14:12:27.925] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 333.2° | 朝向误差: 92.9°
[14:12:27.942] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 333.1° | 朝向误差: 92.9°
[14:12:27.975] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 333.1° | 朝向误差: 92.9°
[14:12:27.986] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 333.1° | 朝向误差: 92.9°
=== ROV控制状态 Frame 1320 Time 26.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.624, 2.775, -0.401)
位置误差向量: (0.901, -0.038, 0.401), 距离: 0.9868m
当前速度: (0.2106, -0.0109, 0.0724), 速度大小: 0.2229m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-2.944, Y:0.176, Z:-1.312
速度因子: 0.789, 自适应速度因子: 0.789
最终轴输出: X=-0.318, Y=0.006, Z=0.016, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 333.11, 357.94)
当前角速度: (0.004, -0.723, 0.287)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.87°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.452, 0.000, 0.892)
目标方向: (0.913, -0.039, 0.407)
方向点积: -0.050
最终旋转输出: 0.000
==================================================

[14:12:28.003] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 333.1° | 朝向误差: 92.9°
[14:12:28.021] 旋转事件: 快速旋转模式 - 误差: 92.9°, 输出: 2.000 | 当前朝向: 333.1° | 朝向误差: 92.9°
[14:12:28.045] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 333.1° | 朝向误差: 92.8°
[14:12:28.061] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 333.1° | 朝向误差: 92.8°
[14:12:28.086] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 333.0° | 朝向误差: 92.8°
[14:12:28.104] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 333.0° | 朝向误差: 92.8°
[14:12:28.128] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 333.0° | 朝向误差: 92.8°
[14:12:28.145] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 333.0° | 朝向误差: 92.8°
[14:12:28.163] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 333.0° | 朝向误差: 92.8°
[14:12:28.180] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 333.0° | 朝向误差: 92.8°
=== ROV控制状态 Frame 1330 Time 26.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.665, 2.773, -0.387)
位置误差向量: (0.860, -0.036, 0.387), 距离: 0.9435m
当前速度: (0.2003, -0.0105, 0.0698), 速度大小: 0.2124m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-2.739, Y:0.163, Z:-1.235
速度因子: 0.752, 自适应速度因子: 0.752
最终轴输出: X=-0.282, Y=0.006, Z=0.014, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 332.97, 358.00)
当前角速度: (-0.033, -0.688, 0.410)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.79°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.454, 0.000, 0.891)
目标方向: (0.911, -0.038, 0.410)
方向点积: -0.049
最终旋转输出: 0.000
==================================================

[14:12:28.205] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 333.0° | 朝向误差: 92.8°
[14:12:28.223] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 332.9° | 朝向误差: 92.8°
[14:12:28.240] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 332.9° | 朝向误差: 92.8°
[14:12:28.268] 旋转事件: 快速旋转模式 - 误差: 92.8°, 输出: 2.000 | 当前朝向: 332.9° | 朝向误差: 92.8°
[14:12:28.286] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 332.9° | 朝向误差: 92.7°
[14:12:28.303] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 332.9° | 朝向误差: 92.7°
[14:12:28.320] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 332.9° | 朝向误差: 92.7°
[14:12:28.344] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 332.9° | 朝向误差: 92.7°
[14:12:28.361] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 332.9° | 朝向误差: 92.7°
[14:12:28.385] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 332.8° | 朝向误差: 92.7°
=== ROV控制状态 Frame 1340 Time 26.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.703, 2.771, -0.373)
位置误差向量: (0.821, -0.034, 0.373), 距离: 0.9026m
当前速度: (0.1878, -0.0099, 0.0669), 速度大小: 0.1996m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-2.550, Y:0.151, Z:-1.164
速度因子: 0.717, 自适应速度因子: 0.717
最终轴输出: X=-0.251, Y=0.005, Z=0.012, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 332.84, 358.10)
当前角速度: (0.022, -0.641, 0.502)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.70°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.456, 0.000, 0.890)
目标方向: (0.910, -0.038, 0.414)
方向点积: -0.047
最终旋转输出: 0.000
==================================================

[14:12:28.402] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 332.8° | 朝向误差: 92.7°
[14:12:28.421] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 332.8° | 朝向误差: 92.7°
[14:12:28.447] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 332.8° | 朝向误差: 92.7°
[14:12:28.464] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 332.8° | 朝向误差: 92.7°
[14:12:28.481] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 332.8° | 朝向误差: 92.7°
[14:12:28.507] 旋转事件: 快速旋转模式 - 误差: 92.7°, 输出: 2.000 | 当前朝向: 332.8° | 朝向误差: 92.7°
[14:12:28.523] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 332.8° | 朝向误差: 92.6°
[14:12:28.540] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 332.7° | 朝向误差: 92.6°
[14:12:28.565] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 332.7° | 朝向误差: 92.6°
[14:12:28.590] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 332.7° | 朝向误差: 92.6°
=== ROV控制状态 Frame 1350 Time 27.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.740, 2.769, -0.360)
位置误差向量: (0.785, -0.032, 0.360), 距离: 0.8643m
当前速度: (0.1745, -0.0093, 0.0632), 速度大小: 0.1858m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-2.376, Y:0.139, Z:-1.097
速度因子: 0.684, 自适应速度因子: 0.684
最终轴输出: X=-0.224, Y=0.005, Z=0.010, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 332.72, 358.21)
当前角速度: (-0.004, -0.595, 0.623)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.62°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.458, 0.000, 0.889)
目标方向: (0.908, -0.037, 0.417)
方向点积: -0.046
最终旋转输出: 0.000
==================================================

[14:12:28.606] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 332.7° | 朝向误差: 92.6°
[14:12:28.626] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 332.7° | 朝向误差: 92.6°
[14:12:28.642] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 332.7° | 朝向误差: 92.6°
[14:12:28.666] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 332.7° | 朝向误差: 92.6°
[14:12:28.683] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 332.7° | 朝向误差: 92.6°
[14:12:28.701] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 332.6° | 朝向误差: 92.6°
[14:12:28.725] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 332.6° | 朝向误差: 92.6°
[14:12:28.743] 旋转事件: 快速旋转模式 - 误差: 92.6°, 输出: 2.000 | 当前朝向: 332.6° | 朝向误差: 92.6°
[14:12:28.768] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 332.6° | 朝向误差: 92.5°
[14:12:28.785] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 332.6° | 朝向误差: 92.5°
=== ROV控制状态 Frame 1360 Time 27.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.773, 2.767, -0.348)
位置误差向量: (0.751, -0.030, 0.348), 距离: 0.8287m
当前速度: (0.1618, -0.0086, 0.0596), 速度大小: 0.1727m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-2.220, Y:0.128, Z:-1.036
速度因子: 0.653, 自适应速度因子: 0.653
最终轴输出: X=-0.200, Y=0.005, Z=0.008, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 332.60, 358.34)
当前角速度: (0.003, -0.552, 0.664)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.53°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.460, 0.000, 0.888)
目标方向: (0.907, -0.037, 0.420)
方向点积: -0.044
最终旋转输出: 0.000
==================================================

[14:12:28.802] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 332.6° | 朝向误差: 92.5°
[14:12:28.827] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 332.6° | 朝向误差: 92.5°
[14:12:28.844] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 332.6° | 朝向误差: 92.5°
[14:12:28.861] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 332.6° | 朝向误差: 92.5°
[14:12:28.894] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 332.5° | 朝向误差: 92.5°
[14:12:28.904] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 332.5° | 朝向误差: 92.5°
[14:12:28.921] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 332.5° | 朝向误差: 92.5°
[14:12:28.946] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 332.5° | 朝向误差: 92.5°
[14:12:28.963] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 332.5° | 朝向误差: 92.5°
[14:12:28.988] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 332.5° | 朝向误差: 92.5°
=== ROV控制状态 Frame 1370 Time 27.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.804, 2.766, -0.337)
位置误差向量: (0.720, -0.029, 0.337), 距离: 0.7956m
当前速度: (0.1499, -0.0079, 0.0563), 速度大小: 0.1603m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-2.079, Y:0.118, Z:-0.980
速度因子: 0.625, 自适应速度因子: 0.625
最终轴输出: X=-0.180, Y=0.004, Z=0.007, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 332.50, 358.47)
当前角速度: (0.005, -0.516, 0.661)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.46°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.462, 0.000, 0.887)
目标方向: (0.905, -0.036, 0.423)
方向点积: -0.043
最终旋转输出: 0.000
==================================================

[14:12:29.005] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.5° | 朝向误差: 92.4°
[14:12:29.022] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.5° | 朝向误差: 92.4°
[14:12:29.048] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.5° | 朝向误差: 92.4°
[14:12:29.065] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.5° | 朝向误差: 92.4°
[14:12:29.082] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.4° | 朝向误差: 92.4°
[14:12:29.100] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.4° | 朝向误差: 92.4°
[14:12:29.125] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.4° | 朝向误差: 92.4°
[14:12:29.142] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.4° | 朝向误差: 92.4°
[14:12:29.167] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.4° | 朝向误差: 92.4°
[14:12:29.186] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.4° | 朝向误差: 92.4°
=== ROV控制状态 Frame 1380 Time 27.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.833, 2.764, -0.326)
位置误差向量: (0.692, -0.027, 0.326), 距离: 0.7650m
当前速度: (0.1390, -0.0072, 0.0530), 速度大小: 0.1489m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.952, Y:0.108, Z:-0.929
速度因子: 0.599, 自适应速度因子: 0.599
最终轴输出: X=-0.162, Y=0.004, Z=0.006, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 332.40, 358.60)
当前角速度: (-0.007, -0.481, 0.629)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.38°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.463, 0.000, 0.886)
目标方向: (0.904, -0.035, 0.426)
方向点积: -0.042
最终旋转输出: 0.000
==================================================

[14:12:29.204] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.4° | 朝向误差: 92.4°
[14:12:29.220] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.4° | 朝向误差: 92.4°
[14:12:29.247] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.4° | 朝向误差: 92.4°
[14:12:29.265] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 332.4° | 朝向误差: 92.4°
[14:12:29.283] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.3° | 朝向误差: 92.3°
[14:12:29.307] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.3° | 朝向误差: 92.3°
[14:12:29.323] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.3° | 朝向误差: 92.3°
[14:12:29.340] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.3° | 朝向误差: 92.3°
[14:12:29.367] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.3° | 朝向误差: 92.3°
[14:12:29.384] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.3° | 朝向误差: 92.3°
=== ROV控制状态 Frame 1390 Time 27.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.860, 2.763, -0.315)
位置误差向量: (0.665, -0.026, 0.315), 距离: 0.7364m
当前速度: (0.1288, -0.0066, 0.0502), 速度大小: 0.1384m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.837, Y:0.100, Z:-0.883
速度因子: 0.574, 自适应速度因子: 0.574
最终轴输出: X=-0.146, Y=0.004, Z=0.005, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 332.30, 358.72)
当前角速度: (0.011, -0.450, 0.552)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.31°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.465, 0.000, 0.885)
目标方向: (0.903, -0.035, 0.428)
方向点积: -0.040
最终旋转输出: 0.000
==================================================

[14:12:29.401] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.3° | 朝向误差: 92.3°
[14:12:29.425] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.3° | 朝向误差: 92.3°
[14:12:29.444] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.3° | 朝向误差: 92.3°
[14:12:29.461] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.3° | 朝向误差: 92.3°
[14:12:29.486] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.3° | 朝向误差: 92.3°
[14:12:29.512] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.3° | 朝向误差: 92.3°
[14:12:29.523] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.2° | 朝向误差: 92.3°
[14:12:29.541] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.2° | 朝向误差: 92.3°
[14:12:29.566] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.2° | 朝向误差: 92.3°
[14:12:29.584] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 332.2° | 朝向误差: 92.3°
=== ROV控制状态 Frame 1400 Time 28.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.884, 2.761, -0.306)
位置误差向量: (0.640, -0.024, 0.306), 距离: 0.7099m
当前速度: (0.1196, -0.0061, 0.0474), 速度大小: 0.1288m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.733, Y:0.093, Z:-0.840
速度因子: 0.551, 自适应速度因子: 0.551
最终轴输出: X=-0.133, Y=0.004, Z=0.004, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 332.22, 358.83)
当前角速度: (-0.005, -0.421, 0.493)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.25°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.466, 0.000, 0.885)
目标方向: (0.902, -0.034, 0.431)
方向点积: -0.039
最终旋转输出: 0.000
==================================================

[14:12:29.602] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.2° | 朝向误差: 92.2°
[14:12:29.625] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.2° | 朝向误差: 92.2°
[14:12:29.645] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.2° | 朝向误差: 92.2°
[14:12:29.670] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.2° | 朝向误差: 92.2°
[14:12:29.681] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.2° | 朝向误差: 92.2°
[14:12:29.704] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.2° | 朝向误差: 92.2°
[14:12:29.722] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.2° | 朝向误差: 92.2°
[14:12:29.747] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.2° | 朝向误差: 92.2°
[14:12:29.765] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
[14:12:29.782] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
=== ROV控制状态 Frame 1410 Time 28.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.907, 2.760, -0.297)
位置误差向量: (0.617, -0.023, 0.297), 距离: 0.6852m
当前速度: (0.1111, -0.0056, 0.0449), 速度大小: 0.1200m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.639, Y:0.086, Z:-0.801
速度因子: 0.530, 自适应速度因子: 0.530
最终轴输出: X=-0.121, Y=0.003, Z=0.004, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 332.14, 358.92)
当前角速度: (0.006, -0.395, 0.417)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.20°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.467, 0.000, 0.884)
目标方向: (0.901, -0.034, 0.433)
方向点积: -0.038
最终旋转输出: 0.000
==================================================

[14:12:29.807] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
[14:12:29.825] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
[14:12:29.842] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
[14:12:29.861] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
[14:12:29.885] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
[14:12:29.902] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
[14:12:29.920] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
[14:12:29.944] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
[14:12:29.962] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
[14:12:29.987] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
=== ROV控制状态 Frame 1420 Time 28.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.929, 2.759, -0.288)
位置误差向量: (0.596, -0.022, 0.288), 距离: 0.6621m
当前速度: (0.1034, -0.0051, 0.0423), 速度大小: 0.1118m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.554, Y:0.080, Z:-0.764
速度因子: 0.510, 自适应速度因子: 0.510
最终轴输出: X=-0.110, Y=0.003, Z=0.003, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 332.06, 358.99)
当前角速度: (-0.013, -0.371, 0.369)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.16°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.469, 0.000, 0.883)
目标方向: (0.900, -0.034, 0.435)
方向点积: -0.038
最终旋转输出: 0.000
==================================================

[14:12:30.004] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 332.1° | 朝向误差: 92.2°
[14:12:30.021] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.045] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.063] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.081] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.106] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.132] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.142] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.166] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.184] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
=== ROV控制状态 Frame 1430 Time 28.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.948, 2.758, -0.280)
位置误差向量: (0.576, -0.021, 0.280), 距离: 0.6406m
当前速度: (0.0962, -0.0046, 0.0402), 速度大小: 0.1044m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.476, Y:0.075, Z:-0.731
速度因子: 0.492, 自适应速度因子: 0.492
最终轴输出: X=-0.101, Y=0.003, Z=0.003, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.99, 359.06)
当前角速度: (0.010, -0.349, 0.305)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.12°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.470, 0.000, 0.883)
目标方向: (0.899, -0.033, 0.436)
方向点积: -0.037
最终旋转输出: 0.000
==================================================

[14:12:30.202] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.226] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.243] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.261] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.289] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 332.0° | 朝向误差: 92.1°
[14:12:30.306] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.324] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.340] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.365] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.383] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
=== ROV控制状态 Frame 1440 Time 28.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.967, 2.757, -0.272)
位置误差向量: (0.557, -0.020, 0.272), 距离: 0.6205m
当前速度: (0.0896, -0.0042, 0.0380), 速度大小: 0.0975m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.405, Y:0.070, Z:-0.700
速度因子: 0.475, 自适应速度因子: 0.475
最终轴输出: X=-0.093, Y=0.003, Z=0.003, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.92, 359.12)
当前角速度: (-0.003, -0.330, 0.278)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.09°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.471, 0.000, 0.882)
目标方向: (0.898, -0.033, 0.438)
方向点积: -0.036
最终旋转输出: 0.000
==================================================

[14:12:30.400] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.425] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.444] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.461] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.486] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.503] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.520] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.545] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.562] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
[14:12:30.594] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.9° | 朝向误差: 92.1°
=== ROV控制状态 Frame 1450 Time 29.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (28.984, 2.757, -0.264)
位置误差向量: (0.540, -0.020, 0.264), 距离: 0.6018m
当前速度: (0.0837, -0.0037, 0.0360), 速度大小: 0.0912m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.341, Y:0.007, Z:-0.672
速度因子: 0.459, 自适应速度因子: 0.459
最终轴输出: X=-0.086, Y=-0.001, Z=0.002, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.86, 359.17)
当前角速度: (0.002, -0.311, 0.250)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.06°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.472, 0.000, 0.882)
目标方向: (0.898, -0.033, 0.439)
方向点积: -0.036
最终旋转输出: 0.000
==================================================

[14:12:30.604] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.1°
[14:12:30.621] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.1°
[14:12:30.647] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.1°
[14:12:30.665] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.1°
[14:12:30.682] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.1°
[14:12:30.707] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.0°
[14:12:30.724] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.0°
[14:12:30.749] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.0°
[14:12:30.766] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.0°
[14:12:30.783] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.0°
=== ROV控制状态 Frame 1460 Time 29.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.000, 2.756, -0.257)
位置误差向量: (0.524, -0.019, 0.257), 距离: 0.5842m
当前速度: (0.0781, -0.0029, 0.0341), 速度大小: 0.0853m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.281, Y:0.006, Z:-0.645
速度因子: 0.444, 自适应速度因子: 0.444
最终轴输出: X=-0.080, Y=-0.001, Z=0.002, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.80, 359.22)
当前角速度: (0.001, -0.294, 0.230)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.04°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.473, 0.000, 0.881)
目标方向: (0.897, -0.032, 0.441)
方向点积: -0.036
最终旋转输出: 0.000
==================================================

[14:12:30.802] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.0°
[14:12:30.826] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.0°
[14:12:30.842] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.0°
[14:12:30.868] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.0°
[14:12:30.885] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.0°
[14:12:30.904] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.0°
[14:12:30.921] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.8° | 朝向误差: 92.0°
[14:12:30.945] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:30.962] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:30.981] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
=== ROV控制状态 Frame 1470 Time 29.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.015, 2.755, -0.251)
位置误差向量: (0.509, -0.018, 0.251), 距离: 0.5678m
当前速度: (0.0731, -0.0023, 0.0322), 速度大小: 0.0800m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.227, Y:0.006, Z:-0.621
速度因子: 0.430, 自适应速度因子: 0.430
最终轴输出: X=-0.074, Y=-0.001, Z=0.002, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.74, 359.26)
当前角速度: (-0.001, -0.279, 0.218)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.03°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.473, 0.000, 0.881)
目标方向: (0.897, -0.033, 0.442)
方向点积: -0.035
最终旋转输出: 0.000
==================================================

[14:12:31.005] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.023] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.040] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.066] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.084] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.101] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.127] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.144] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.163] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.181] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
=== ROV控制状态 Frame 1480 Time 29.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.030, 2.755, -0.245)
位置误差向量: (0.495, -0.018, 0.245), 距离: 0.5523m
当前速度: (0.0685, -0.0019, 0.0305), 速度大小: 0.0750m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.177, Y:0.005, Z:-0.598
速度因子: 0.416, 自适应速度因子: 0.416
最终轴输出: X=-0.069, Y=-0.001, Z=0.002, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.68, 359.30)
当前角速度: (0.001, -0.264, 0.204)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.02°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.474, 0.000, 0.880)
目标方向: (0.896, -0.033, 0.443)
方向点积: -0.035
最终旋转输出: 0.000
==================================================

[14:12:31.206] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.223] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.247] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.264] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.283] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.300] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.7° | 朝向误差: 92.0°
[14:12:31.326] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.343] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.361] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.384] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
=== ROV控制状态 Frame 1490 Time 29.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.043, 2.755, -0.239)
位置误差向量: (0.482, -0.018, 0.239), 距离: 0.5379m
当前速度: (0.0643, -0.0015, 0.0289), 速度大小: 0.0705m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.132, Y:0.005, Z:-0.577
速度因子: 0.404, 自适应速度因子: 0.404
最终轴输出: X=-0.064, Y=0.000, Z=0.002, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.63, 359.34)
当前角速度: (-0.003, -0.250, 0.195)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.01°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.475, 0.000, 0.880)
目标方向: (0.896, -0.033, 0.444)
方向点积: -0.035
最终旋转输出: 0.000
==================================================

[14:12:31.401] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.427] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.445] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.461] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.486] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.512] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.522] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.541] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.564] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.582] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
=== ROV控制状态 Frame 1500 Time 30.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.055, 2.754, -0.233)
位置误差向量: (0.469, -0.017, 0.233), 距离: 0.5242m
当前速度: (0.0604, -0.0013, 0.0275), 速度大小: 0.0664m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.089, Y:0.005, Z:-0.558
速度因子: 0.392, 自适应速度因子: 0.392
最终轴输出: X=-0.060, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.58, 359.38)
当前角速度: (0.004, -0.238, 0.177)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.01°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.476, 0.000, 0.880)
目标方向: (0.895, -0.033, 0.445)
方向点积: -0.035
最终旋转输出: 0.000
==================================================

[14:12:31.607] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.624] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.647] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.666] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.682] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.700] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.726] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.6° | 朝向误差: 92.0°
[14:12:31.743] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:31.761] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:31.786] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
=== ROV控制状态 Frame 1510 Time 30.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.067, 2.754, -0.228)
位置误差向量: (0.458, -0.017, 0.228), 距离: 0.5114m
当前速度: (0.0568, -0.0010, 0.0261), 速度大小: 0.0625m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.050, Y:0.005, Z:-0.539
速度因子: 0.381, 自适应速度因子: 0.381
最终轴输出: X=-0.056, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.54, 359.42)
当前角速度: (-0.002, -0.227, 0.169)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.01°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.477, 0.000, 0.879)
目标方向: (0.895, -0.034, 0.445)
方向点积: -0.035
最终旋转输出: 0.000
==================================================

[14:12:31.802] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:31.827] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:31.843] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:31.860] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:31.884] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:31.902] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:31.927] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:31.945] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:31.971] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:31.982] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
=== ROV控制状态 Frame 1520 Time 30.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.078, 2.754, -0.223)
位置误差向量: (0.447, -0.017, 0.223), 距离: 0.4993m
当前速度: (0.0535, -0.0008, 0.0248), 速度大小: 0.0590m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-1.014, Y:0.004, Z:-0.522
速度因子: 0.371, 自适应速度因子: 0.371
最终轴输出: X=-0.053, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.49, 359.45)
当前角速度: (0.002, -0.217, 0.153)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.01°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.477, 0.000, 0.879)
目标方向: (0.894, -0.034, 0.446)
方向点积: -0.035
最终旋转输出: 0.000
==================================================

[14:12:32.006] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:32.022] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:32.046] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:32.062] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:32.087] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:32.105] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:32.132] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:32.141] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:32.166] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
[14:12:32.184] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.5° | 朝向误差: 92.0°
=== ROV控制状态 Frame 1530 Time 30.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.088, 2.754, -0.218)
位置误差向量: (0.436, -0.017, 0.218), 距离: 0.4878m
当前速度: (0.0505, -0.0007, 0.0236), 速度大小: 0.0558m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.980, Y:0.004, Z:-0.506
速度因子: 0.361, 自适应速度因子: 0.361
最终轴输出: X=-0.050, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.45, 359.48)
当前角速度: (-0.001, -0.206, 0.142)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.02°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.478, 0.000, 0.878)
目标方向: (0.894, -0.035, 0.446)
方向点积: -0.035
最终旋转输出: 0.000
==================================================

[14:12:32.200] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.225] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.242] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.265] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.291] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.303] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.327] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.344] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.360] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.384] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
=== ROV控制状态 Frame 1540 Time 30.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.098, 2.754, -0.213)
位置误差向量: (0.426, -0.017, 0.213), 距离: 0.4770m
当前速度: (0.0479, -0.0006, 0.0225), 速度大小: 0.0529m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.949, Y:0.004, Z:-0.491
速度因子: 0.352, 自适应速度因子: 0.352
最终轴输出: X=-0.047, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.41, 359.50)
当前角速度: (0.001, -0.197, 0.130)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.03°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.479, 0.000, 0.878)
目标方向: (0.894, -0.035, 0.447)
方向点积: -0.035
最终旋转输出: 0.000
==================================================

[14:12:32.402] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.426] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.445] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.463] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.480] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.505] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.522] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.540] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.564] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.582] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
=== ROV控制状态 Frame 1550 Time 31.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.107, 2.754, -0.209)
位置误差向量: (0.417, -0.017, 0.209), 距离: 0.4667m
当前速度: (0.0453, -0.0005, 0.0215), 速度大小: 0.0501m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.919, Y:0.004, Z:-0.477
速度因子: 0.343, 自适应速度因子: 0.343
最终轴输出: X=-0.044, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.37, 359.53)
当前角速度: (0.000, -0.189, 0.119)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.04°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.479, 0.000, 0.878)
目标方向: (0.894, -0.036, 0.447)
方向点积: -0.035
最终旋转输出: 0.000
==================================================

[14:12:32.607] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.621] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.646] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.661] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.686] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.711] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.4° | 朝向误差: 92.0°
[14:12:32.744] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.0°
[14:12:32.746] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.0°
[14:12:32.776] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.0°
[14:12:32.786] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.0°
=== ROV控制状态 Frame 1560 Time 31.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.116, 2.754, -0.205)
位置误差向量: (0.408, -0.017, 0.205), 距离: 0.4570m
当前速度: (0.0430, -0.0004, 0.0205), 速度大小: 0.0476m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.892, Y:0.004, Z:-0.464
速度因子: 0.335, 自适应速度因子: 0.335
最终轴输出: X=-0.042, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.34, 359.55)
当前角速度: (0.000, -0.179, 0.110)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.05°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.480, 0.000, 0.877)
目标方向: (0.893, -0.036, 0.448)
方向点积: -0.036
最终旋转输出: 0.000
==================================================

[14:12:32.804] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.0°
[14:12:32.822] 旋转事件: 快速旋转模式 - 误差: 92.0°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.0°
[14:12:32.846] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:32.864] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:32.880] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:32.904] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:32.931] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:32.941] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:32.966] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:32.983] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
=== ROV控制状态 Frame 1570 Time 31.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.125, 2.753, -0.201)
位置误差向量: (0.400, -0.016, 0.201), 距离: 0.4477m
当前速度: (0.0409, -0.0004, 0.0195), 速度大小: 0.0453m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.866, Y:0.004, Z:-0.451
速度因子: 0.327, 自适应速度因子: 0.327
最终轴输出: X=-0.040, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.30, 359.57)
当前角速度: (-0.002, -0.171, 0.104)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.06°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.480, 0.000, 0.877)
目标方向: (0.893, -0.037, 0.448)
方向点积: -0.036
最终旋转输出: 0.000
==================================================

[14:12:33.001] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.025] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.043] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.066] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.084] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.102] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.129] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.147] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.165] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.183] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
=== ROV控制状态 Frame 1580 Time 31.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.132, 2.753, -0.197)
位置误差向量: (0.392, -0.016, 0.197), 距离: 0.4389m
当前速度: (0.0388, -0.0003, 0.0187), 速度大小: 0.0431m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.842, Y:0.004, Z:-0.440
速度因子: 0.319, 自适应速度因子: 0.319
最终轴输出: X=-0.038, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.27, 359.59)
当前角速度: (0.001, -0.165, 0.095)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.07°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.481, 0.000, 0.877)
目标方向: (0.893, -0.037, 0.448)
方向点积: -0.036
最终旋转输出: 0.000
==================================================

[14:12:33.201] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.233] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.243] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.261] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.285] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.3° | 朝向误差: 92.1°
[14:12:33.303] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.320] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.340] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.364] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.389] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
=== ROV控制状态 Frame 1590 Time 31.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.140, 2.753, -0.193)
位置误差向量: (0.384, -0.016, 0.193), 距离: 0.4306m
当前速度: (0.0369, -0.0003, 0.0179), 速度大小: 0.0410m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.819, Y:0.004, Z:-0.429
速度因子: 0.312, 自适应速度因子: 0.312
最终轴输出: X=-0.036, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.24, 359.61)
当前角速度: (0.000, -0.157, 0.089)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.09°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.481, 0.000, 0.877)
目标方向: (0.893, -0.038, 0.449)
方向点积: -0.036
最终旋转输出: 0.000
==================================================

[14:12:33.407] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.425] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.443] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.467] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.485] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.502] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.528] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.548] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.565] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.581] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
=== ROV控制状态 Frame 1600 Time 32.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.147, 2.753, -0.190)
位置误差向量: (0.377, -0.016, 0.190), 距离: 0.4226m
当前速度: (0.0352, -0.0003, 0.0171), 速度大小: 0.0391m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.798, Y:0.004, Z:-0.418
速度因子: 0.305, 自适应速度因子: 0.305
最终轴输出: X=-0.034, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.20, 359.63)
当前角速度: (0.000, -0.153, 0.082)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.10°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.482, 0.000, 0.876)
目标方向: (0.893, -0.038, 0.449)
方向点积: -0.037
最终旋转输出: 0.000
==================================================

[14:12:33.607] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.625] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.642] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.665] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.683] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.702] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.727] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.745] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.762] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.785] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
=== ROV控制状态 Frame 1610 Time 32.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.154, 2.753, -0.186)
位置误差向量: (0.370, -0.016, 0.186), 距离: 0.4149m
当前速度: (0.0336, -0.0002, 0.0164), 速度大小: 0.0374m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.778, Y:0.004, Z:-0.408
速度因子: 0.299, 自适应速度因子: 0.299
最终轴输出: X=-0.033, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.17, 359.64)
当前角速度: (0.000, -0.145, 0.078)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.12°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.482, 0.000, 0.876)
目标方向: (0.893, -0.039, 0.449)
方向点积: -0.037
最终旋转输出: 0.000
==================================================

[14:12:33.804] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.821] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.852] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.862] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.885] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.902] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.927] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.945] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.2° | 朝向误差: 92.1°
[14:12:33.963] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.1°
[14:12:33.981] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.1°
=== ROV控制状态 Frame 1620 Time 32.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.161, 2.753, -0.183)
位置误差向量: (0.364, -0.016, 0.183), 距离: 0.4076m
当前速度: (0.0321, -0.0002, 0.0157), 速度大小: 0.0358m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.759, Y:0.004, Z:-0.399
速度因子: 0.292, 自适应速度因子: 0.292
最终轴输出: X=-0.031, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.15, 359.66)
当前角速度: (0.000, -0.140, 0.073)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.13°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.483, 0.000, 0.876)
目标方向: (0.893, -0.040, 0.449)
方向点积: -0.037
最终旋转输出: 0.000
==================================================

[14:12:34.007] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.1°
[14:12:34.024] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.1°
[14:12:34.047] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.1°
[14:12:34.064] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.1°
[14:12:34.080] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.1°
[14:12:34.104] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.1°
[14:12:34.122] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.1°
[14:12:34.147] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.1°
[14:12:34.165] 旋转事件: 快速旋转模式 - 误差: 92.1°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.1°
[14:12:34.182] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
=== ROV控制状态 Frame 1630 Time 32.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.167, 2.753, -0.180)
位置误差向量: (0.358, -0.016, 0.180), 距离: 0.4007m
当前速度: (0.0307, -0.0002, 0.0151), 速度大小: 0.0342m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.741, Y:0.004, Z:-0.390
速度因子: 0.286, 自适应速度因子: 0.286
最终轴输出: X=-0.030, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.12, 359.67)
当前角速度: (0.000, -0.134, 0.067)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.15°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.483, 0.000, 0.876)
目标方向: (0.892, -0.040, 0.449)
方向点积: -0.038
最终旋转输出: 0.000
==================================================

[14:12:34.206] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.223] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.240] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.264] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.281] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.316] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.327] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.343] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.362] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.385] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
=== ROV控制状态 Frame 1640 Time 32.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.173, 2.753, -0.177)
位置误差向量: (0.352, -0.016, 0.177), 距离: 0.3940m
当前速度: (0.0294, -0.0002, 0.0145), 速度大小: 0.0327m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.724, Y:0.004, Z:-0.381
速度因子: 0.281, 自适应速度因子: 0.281
最终轴输出: X=-0.029, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.09, 359.69)
当前角速度: (-0.001, -0.130, 0.064)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.17°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.483, 0.000, 0.875)
目标方向: (0.892, -0.041, 0.450)
方向点积: -0.038
最终旋转输出: 0.000
==================================================

[14:12:34.402] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.426] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.443] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.468] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.486] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.503] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.520] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.545] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.563] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.580] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
=== ROV控制状态 Frame 1650 Time 33.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.179, 2.753, -0.174)
位置误差向量: (0.346, -0.016, 0.174), 距离: 0.3876m
当前速度: (0.0281, -0.0002, 0.0139), 速度大小: 0.0314m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.708, Y:0.004, Z:-0.373
速度因子: 0.275, 自适应速度因子: 0.275
最终轴输出: X=-0.028, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.07, 359.70)
当前角速度: (0.001, -0.125, 0.060)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.18°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.484, 0.000, 0.875)
目标方向: (0.892, -0.041, 0.450)
方向点积: -0.038
最终旋转输出: 0.000
==================================================

[14:12:34.605] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.623] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.641] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.665] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.682] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.705] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.723] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.1° | 朝向误差: 92.2°
[14:12:34.740] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:34.774] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:34.783] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
=== ROV控制状态 Frame 1660 Time 33.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.184, 2.753, -0.172)
位置误差向量: (0.340, -0.016, 0.172), 距离: 0.3814m
当前速度: (0.0270, -0.0002, 0.0134), 速度大小: 0.0301m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.693, Y:0.004, Z:-0.366
速度因子: 0.270, 自适应速度因子: 0.270
最终轴输出: X=-0.026, Y=0.000, Z=0.001, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.04, 359.71)
当前角速度: (-0.001, -0.121, 0.058)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.20°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.484, 0.000, 0.875)
目标方向: (0.892, -0.042, 0.450)
方向点积: -0.038
最终旋转输出: 0.000
==================================================

[14:12:34.808] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:34.826] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:34.843] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:34.860] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:34.883] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:34.901] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:34.926] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:34.942] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:34.960] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:34.984] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
=== ROV控制状态 Frame 1670 Time 33.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.189, 2.753, -0.169)
位置误差向量: (0.335, -0.016, 0.169), 距离: 0.3755m
当前速度: (0.0259, -0.0001, 0.0129), 速度大小: 0.0290m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.678, Y:0.004, Z:-0.358
速度因子: 0.265, 自适应速度因子: 0.265
最终轴输出: X=-0.025, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.02, 359.72)
当前角速度: (0.000, -0.116, 0.052)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.22°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.485, 0.000, 0.875)
目标方向: (0.892, -0.043, 0.450)
方向点积: -0.039
最终旋转输出: 0.000
==================================================

[14:12:35.002] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.026] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.043] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.066] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.084] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.101] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.126] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.144] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.162] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.187] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
=== ROV控制状态 Frame 1680 Time 33.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.195, 2.753, -0.166)
位置误差向量: (0.330, -0.016, 0.166), 距离: 0.3699m
当前速度: (0.0250, -0.0001, 0.0124), 速度大小: 0.0279m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.664, Y:0.004, Z:-0.351
速度因子: 0.260, 自适应速度因子: 0.260
最终轴输出: X=-0.024, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 331.00, 359.73)
当前角速度: (0.000, -0.113, 0.050)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.24°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.485, 0.000, 0.875)
目标方向: (0.892, -0.043, 0.450)
方向点积: -0.039
最终旋转输出: 0.000
==================================================

[14:12:35.204] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.221] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.245] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.262] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.286] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.303] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.322] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.345] 旋转事件: 快速旋转模式 - 误差: 92.2°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.2°
[14:12:35.363] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
[14:12:35.388] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
=== ROV控制状态 Frame 1690 Time 33.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.199, 2.753, -0.164)
位置误差向量: (0.325, -0.016, 0.164), 距离: 0.3644m
当前速度: (0.0239, -0.0001, 0.0119), 速度大小: 0.0268m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.651, Y:0.003, Z:-0.345
速度因子: 0.255, 自适应速度因子: 0.255
最终轴输出: X=-0.024, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.97, 359.74)
当前角速度: (0.000, -0.108, 0.047)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.25°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.485, 0.000, 0.874)
目标方向: (0.892, -0.044, 0.450)
方向点积: -0.039
最终旋转输出: 0.000
==================================================

[14:12:35.405] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
[14:12:35.422] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
[14:12:35.446] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
[14:12:35.463] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
[14:12:35.488] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
[14:12:35.505] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
[14:12:35.521] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
[14:12:35.547] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
[14:12:35.565] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
[14:12:35.582] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
=== ROV控制状态 Frame 1700 Time 34.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.204, 2.753, -0.162)
位置误差向量: (0.320, -0.016, 0.162), 距离: 0.3592m
当前速度: (0.0231, -0.0001, 0.0115), 速度大小: 0.0258m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.638, Y:0.003, Z:-0.338
速度因子: 0.251, 自适应速度因子: 0.251
最终轴输出: X=-0.023, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.95, 359.75)
当前角速度: (0.000, -0.104, 0.044)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.27°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.486, 0.000, 0.874)
目标方向: (0.892, -0.044, 0.450)
方向点积: -0.040
最终旋转输出: 0.000
==================================================

[14:12:35.606] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 331.0° | 朝向误差: 92.3°
[14:12:35.623] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.640] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.665] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.681] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.706] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.724] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.742] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.768] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.785] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
=== ROV控制状态 Frame 1710 Time 34.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.209, 2.753, -0.159)
位置误差向量: (0.316, -0.016, 0.159), 距离: 0.3541m
当前速度: (0.0223, -0.0001, 0.0111), 速度大小: 0.0249m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.627, Y:0.003, Z:-0.332
速度因子: 0.246, 自适应速度因子: 0.246
最终轴输出: X=-0.022, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.93, 359.76)
当前角速度: (0.000, -0.101, 0.043)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.29°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.486, 0.000, 0.874)
目标方向: (0.892, -0.045, 0.450)
方向点积: -0.040
最终旋转输出: 0.000
==================================================

[14:12:35.802] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.826] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.844] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.862] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.886] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.902] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.921] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.944] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.961] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:35.980] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
=== ROV控制状态 Frame 1720 Time 34.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.213, 2.753, -0.157)
位置误差向量: (0.311, -0.016, 0.157), 距离: 0.3492m
当前速度: (0.0215, -0.0001, 0.0107), 速度大小: 0.0240m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.615, Y:0.003, Z:-0.327
速度因子: 0.242, 自适应速度因子: 0.242
最终轴输出: X=-0.021, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.91, 359.77)
当前角速度: (0.000, -0.098, 0.040)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.30°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.486, 0.000, 0.874)
目标方向: (0.892, -0.045, 0.450)
方向点积: -0.040
最终旋转输出: 0.000
==================================================

[14:12:36.016] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.027] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.044] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.062] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.083] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.103] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.122] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.140] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.167] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.186] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
=== ROV控制状态 Frame 1730 Time 34.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.217, 2.753, -0.155)
位置误差向量: (0.307, -0.016, 0.155), 距离: 0.3445m
当前速度: (0.0207, -0.0001, 0.0104), 速度大小: 0.0232m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.604, Y:0.003, Z:-0.321
速度因子: 0.238, 自适应速度因子: 0.238
最终轴输出: X=-0.020, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.89, 359.78)
当前角速度: (0.000, -0.095, 0.038)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.32°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.486, 0.000, 0.874)
目标方向: (0.892, -0.046, 0.450)
方向点积: -0.040
最终旋转输出: 0.000
==================================================

[14:12:36.203] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.221] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.246] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.263] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.280] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.306] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.325] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.343] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.360] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.385] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
=== ROV控制状态 Frame 1740 Time 34.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.221, 2.753, -0.153)
位置误差向量: (0.303, -0.016, 0.153), 距离: 0.3400m
当前速度: (0.0199, -0.0001, 0.0100), 速度大小: 0.0223m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.593, Y:0.003, Z:-0.316
速度因子: 0.234, 自适应速度因子: 0.234
最终轴输出: X=-0.020, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.88, 359.78)
当前角速度: (0.000, -0.092, 0.035)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.34°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.487, 0.000, 0.874)
目标方向: (0.892, -0.047, 0.450)
方向点积: -0.041
最终旋转输出: 0.000
==================================================

[14:12:36.403] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.427] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.444] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.461] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.486] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.504] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.521] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.546] 旋转事件: 快速旋转模式 - 误差: 92.3°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.3°
[14:12:36.562] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.4°
[14:12:36.581] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.4°
=== ROV控制状态 Frame 1750 Time 35.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.225, 2.753, -0.151)
位置误差向量: (0.299, -0.016, 0.151), 距离: 0.3356m
当前速度: (0.0194, -0.0001, 0.0097), 速度大小: 0.0217m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.583, Y:0.003, Z:-0.310
速度因子: 0.231, 自适应速度因子: 0.231
最终轴输出: X=-0.019, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.86, 359.79)
当前角速度: (0.000, -0.090, 0.035)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.35°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.487, 0.000, 0.873)
目标方向: (0.892, -0.047, 0.450)
方向点积: -0.041
最终旋转输出: 0.000
==================================================

[14:12:36.607] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.4°
[14:12:36.631] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.4°
[14:12:36.642] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.9° | 朝向误差: 92.4°
[14:12:36.666] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.683] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.700] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.726] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.743] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.760] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.785] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
=== ROV控制状态 Frame 1760 Time 35.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.229, 2.753, -0.149)
位置误差向量: (0.295, -0.016, 0.149), 距离: 0.3313m
当前速度: (0.0187, -0.0001, 0.0094), 速度大小: 0.0209m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.574, Y:0.003, Z:-0.306
速度因子: 0.227, 自适应速度因子: 0.227
最终轴输出: X=-0.018, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.84, 359.80)
当前角速度: (0.000, -0.087, 0.032)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.37°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.487, 0.000, 0.873)
目标方向: (0.892, -0.048, 0.450)
方向点积: -0.041
最终旋转输出: 0.000
==================================================

[14:12:36.804] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.822] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.845] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.863] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.887] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.904] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.922] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.941] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.964] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:36.980] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
=== ROV控制状态 Frame 1770 Time 35.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.233, 2.753, -0.147)
位置误差向量: (0.292, -0.016, 0.147), 距离: 0.3272m
当前速度: (0.0181, -0.0001, 0.0091), 速度大小: 0.0203m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.564, Y:0.003, Z:-0.301
速度因子: 0.223, 自适应速度因子: 0.223
最终轴输出: X=-0.018, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.82, 359.80)
当前角速度: (0.000, -0.082, 0.032)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.38°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.488, 0.000, 0.873)
目标方向: (0.892, -0.048, 0.450)
方向点积: -0.042
最终旋转输出: 0.000
==================================================

[14:12:37.006] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.023] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.041] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.066] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.090] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.101] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.126] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.143] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.167] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.185] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
=== ROV控制状态 Frame 1780 Time 35.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.236, 2.753, -0.146)
位置误差向量: (0.288, -0.016, 0.146), 距离: 0.3233m
当前速度: (0.0175, -0.0001, 0.0088), 速度大小: 0.0196m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.555, Y:0.003, Z:-0.296
速度因子: 0.220, 自适应速度因子: 0.220
最终轴输出: X=-0.017, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.81, 359.81)
当前角速度: (0.000, -0.081, 0.029)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.40°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.488, 0.000, 0.873)
目标方向: (0.892, -0.049, 0.450)
方向点积: -0.042
最终旋转输出: 0.000
==================================================

[14:12:37.204] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.222] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.249] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.266] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.283] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.307] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.324] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.344] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.365] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.384] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
=== ROV控制状态 Frame 1790 Time 35.80s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.240, 2.753, -0.144)
位置误差向量: (0.285, -0.016, 0.144), 距离: 0.3194m
当前速度: (0.0170, -0.0001, 0.0086), 速度大小: 0.0190m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.547, Y:0.003, Z:-0.292
速度因子: 0.217, 自适应速度因子: 0.217
最终轴输出: X=-0.017, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.79, 359.81)
当前角速度: (0.000, -0.079, 0.029)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.42°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.488, 0.000, 0.873)
目标方向: (0.892, -0.049, 0.450)
方向点积: -0.042
最终旋转输出: 0.000
==================================================

[14:12:37.403] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.423] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.441] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.467] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.485] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.502] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.526] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.553] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.563] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.581] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
=== ROV控制状态 Frame 1800 Time 36.00s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.243, 2.753, -0.142)
位置误差向量: (0.281, -0.016, 0.142), 距离: 0.3157m
当前速度: (0.0165, -0.0001, 0.0083), 速度大小: 0.0185m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.538, Y:0.003, Z:-0.288
速度因子: 0.213, 自适应速度因子: 0.213
最终轴输出: X=-0.016, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.77, 359.82)
当前角速度: (0.000, -0.076, 0.027)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.43°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.488, 0.000, 0.873)
目标方向: (0.892, -0.050, 0.450)
方向点积: -0.042
最终旋转输出: 0.000
==================================================

[14:12:37.608] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.629] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.644] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.667] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.680] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.708] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.720] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.742] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.762] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.786] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
=== ROV控制状态 Frame 1810 Time 36.20s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.246, 2.753, -0.140)
位置误差向量: (0.278, -0.016, 0.140), 距离: 0.3120m
当前速度: (0.0160, -0.0001, 0.0081), 速度大小: 0.0179m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.530, Y:0.003, Z:-0.283
速度因子: 0.210, 自适应速度因子: 0.210
最终轴输出: X=-0.016, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.76, 359.82)
当前角速度: (-0.001, -0.075, 0.026)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.45°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.488, 0.000, 0.873)
目标方向: (0.891, -0.050, 0.450)
方向点积: -0.043
最终旋转输出: 0.000
==================================================

[14:12:37.806] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.826] 旋转事件: 快速旋转模式 - 误差: 92.4°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.4°
[14:12:37.845] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.5°
[14:12:37.863] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.5°
[14:12:37.883] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.5°
[14:12:37.902] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.8° | 朝向误差: 92.5°
[14:12:37.922] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:37.946] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:37.962] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:37.987] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
=== ROV控制状态 Frame 1820 Time 36.40s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.249, 2.753, -0.139)
位置误差向量: (0.275, -0.016, 0.139), 距离: 0.3085m
当前速度: (0.0154, -0.0001, 0.0078), 速度大小: 0.0173m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.522, Y:0.003, Z:-0.279
速度因子: 0.207, 自适应速度因子: 0.207
最终轴输出: X=-0.015, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.74, 359.83)
当前角速度: (0.000, -0.073, 0.024)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.46°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.489, 0.000, 0.872)
目标方向: (0.891, -0.051, 0.450)
方向点积: -0.043
最终旋转输出: 0.000
==================================================

[14:12:38.015] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.026] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.045] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.063] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.080] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.105] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.122] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.140] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.166] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.184] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
=== ROV控制状态 Frame 1830 Time 36.60s ===
目标位置: (29.524, 2.737, 0.000), 当前位置: (29.252, 2.753, -0.137)
位置误差向量: (0.272, -0.016, 0.137), 距离: 0.3051m
当前速度: (0.0151, -0.0001, 0.0076), 速度大小: 0.0169m/s
控制阶段: 线性减速, 控制模式: PositionOnly
位置控制: True, 旋转控制: False
PID输出 - X:-0.515, Y:0.003, Z:-0.276
速度因子: 0.204, 自适应速度因子: 0.204
最终轴输出: X=-0.015, Y=0.000, Z=0.000, RotY=0.000
--- 旋转状态详情 ---
当前欧拉角: (0.00, 330.73, 359.83)
当前角速度: (0.000, -0.070, 0.023)
旋转输出倍数: 2.00
最终旋转力: 0.000
--- 朝向控制详情 ---
朝向控制模式: FastRotation
朝向误差: 92.48°
朝向控制输出: 2.000
快速旋转模式: 是
旋转强度倍数: 2.0x
目标朝向位置: (29.524, 2.737, 0.000)
当前朝向: (-0.489, 0.000, 0.872)
目标方向: (0.891, -0.051, 0.450)
方向点积: -0.043
最终旋转输出: 0.000
==================================================

[14:12:38.203] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.226] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.245] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.263] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.281] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°
[14:12:38.301] 旋转事件: 快速旋转模式 - 误差: 92.5°, 输出: 2.000 | 当前朝向: 330.7° | 朝向误差: 92.5°

================================================================================
日志结束时间: 2025-08-05 14:12:38
================================================================================
